<template>
  <div class="table-container">
    <el-table fit v-loading="dataLoading" :data="tableData" class="swd-table" height="calc(100% - 52px)" row-key="id">
      <el-table-column :label="$t('lg.serial')" align="left" width="50">
        <span slot-scope="scope">
          {{ (searchParams.pageNo - 1) * searchParams.pageSize + scope.$index + 1 }}
        </span>
      </el-table-column>
      <el-table-column :label="$t('8nrk8LIy6KzpJQv8D1o0w')" prop="name" width="150">
        <template slot-scope="{ row }">
          {{ row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.email')" prop="email" minWidth="100"> </el-table-column>
      <el-table-column :label="$t('nJDVObsHH5ogCl6MxIO4C')" minWidth="150">
        <template slot-scope="{ row }">
          {{ row.ownedServiceProvider ? row.ownedServiceProvider.join('、 ') : '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('daGXDH-K0OYEPY_EUdT-c')" prop="boundTime">
        <template slot-scope="{ row }">
          <!-- {{ row.cars | carsTolist }} -->
          <div class="user-manage-list-row-device" @click="$emit('onDevices', row.userId)">
            <span class="user-manage-list-row-device-label" :class="{ active: !item.expire }" v-for="(item, index) in row.cars" :key="item.carId">
              <span v-if="index !== 0">/</span>
              {{ item.machineTypeName }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('PxiKJqRk2NZgR5rrcfbxV')" prop="serviceStatus" minWidth="100">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" @click="$emit('onOrders', row.userId)">{{ $t('lg.view') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('t22DN7yhHaAkxCW69UYsh')" prop="serviceTime" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.lastOnlineTime | utcToLocalTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('zdVunWKu5AhodKvqgP4rI')" prop="importTime" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.updateTime | utcToLocalTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('registrationTime')" prop="importTime" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.createTime | utcToLocalTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.operator')" v-if="[0].indexOf(+this.userType) !== -1">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" v-if="row.available" @click="$emit('onForbidden', row.userId)">{{ $t('D-b7dWflNTTmQ5ISj_Fcy') }}</span>
          <span class="swd-table-cell-btn" v-else @click="$emit('onEnable', row.userId)">{{ $t('lg.startUsing') }}</span>
        </template>
      </el-table-column>
      <template slot="empty">
        <p class="empty-container">
          {{ dataLoading ? '' : $t('lg.noData') }}
        </p>
      </template>
    </el-table>
    <!-- pagination -->
    <div class="swd-pagination-container">
      <!-- <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" :loading="exportLoading" class="swd-download-btn" @click="downloadTableData">
          <svg-icon v-if="!exportLoading" icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip> -->
      <base-pagination
        :current-page.sync="searchParams.pageNo"
        :page-sizes="[10, 15, 20, 30]"
        :page-size.sync="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="$emit('pageChange')"
        @size-change="sizeChange"
      >
      </base-pagination>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    dataLoading: {
      type: Boolean,
      default: false
    },
    searchParams: {
      type: Object,
      default: () => {}
    },
    tableData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    }
  },
  computed: {
    ...mapGetters(['userType'])
  },
  methods: {
    sizeChange(size) {
      this.$emit('sizeChange', size)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container {
  height: 0;
  flex: 1;
  margin-top: -5px;
  .user-manage-list-row-device {
    cursor: pointer;
    &-label {
      &.active {
        color: $primary;
      }
    }
    &:hover {
      text-decoration: underline;
      text-underline-offset: 3px;
      // text-decoration-color: $primary;
    }
  }
  .swd-pagination-container {
    position: relative;
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;
    .swd-download-btn {
      position: absolute;
      right: 0;
    }
  }
}
</style>
