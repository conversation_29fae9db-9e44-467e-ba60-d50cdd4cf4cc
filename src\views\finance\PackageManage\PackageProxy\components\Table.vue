<template>
  <div class="table-container">
    <!-- table -->
    <el-table
      ref="deviceTable"
      v-loading="dataLoading"
      :data="tableData"
      class="swd-table"
      height="calc(100% - 52px)"
      row-key="agentConfigId"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column :label="$t('lg.serviceProvide')" prop="serviceProviderName"> </el-table-column>
      <el-table-column :label="$t('uigvo2391weT6pPqyfxYg')" prop="packName">
        <template slot-scope="{ row }">
          <span style="color: #3370ff;cursor: pointer" @click="showDetailDialog(row, 1)">{{ row.packName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('qzwEjrvjWERzbbwcvI5rK')" prop="levelStr">
        <template slot-scope="{ row }">
          <span>{{ getCurrentPackageType(row.packType) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('_CXEjvnybYTxiZPXdJknx')" prop="packStatus">
        <template slot-scope="{ row }">
          <span>{{ row.packStatus | packStatusFilterVer3 }}</span>
        </template></el-table-column
      >

      <el-table-column :label="currencyParams == 'CNY' ? $t('1W2i1KY0Bfz_8olXlC2d6') : $t('aaBbwSAmqzqUCcJRh515g')" prop="agentPrice"> </el-table-column>
      <el-table-column :label="currencyParams == 'CNY' ? $t('hqZrtrvlMrwFhVBJDoK9d') : $t('OFM2oOsQ2x9IwEF2ttv_b')" prop="guidingPrice"> </el-table-column>
      <el-table-column :label="$t('lg.operator')" prop="handle">
        <template slot-scope="{ row }">
          <span
            class="swd-table-cell-btn"
            @click="showAddOrEditDialog(row, 1)"
            v-if="[0, 1, 2].indexOf(+bisLevel) !== -1 && row.serviceProviderId == bisUserId"
            >{{ $t('biaeNmEm5PyRHVooq6ijB') }}</span
          >
          <span
            class="swd-table-cell-btn"
            @click="showAddOrEditDialog(row, 2)"
            v-if="([0].indexOf(+bisLevel) !== -1 || [1681, 8108].indexOf(+userId) !== -1) && row.serviceProviderId != bisUserId"
            >{{ $t('lg.edit') }}</span
          >
          <span class="swd-table-cell-btn" @click="confirmDelete(row)" v-if="[0, 1, 2].indexOf(+bisLevel) !== -1 && row.serviceProviderId != bisUserId">{{
            $t('lg.delete')
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <div class="swd-pagination-container">
      <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" class="swd-download-btn" @click="emitExport">
          <svg-icon icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip>
      <base-pagination
        :current-page.sync="searchParams.pageIndex"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>

    <AddOrEdit ref="AddOrEdit" :type="editType" :info="packInfo" :visible.sync="AddOrEditVisible" @success="handleSuccess" />
    <PackageDetail ref="PackageDetail" :visible.sync="detailVisible" :info="packInfo" :id="packInfo.packId" :type="detailType" :showCost="false" />
  </div>
</template>

<script>
//
import AddOrEdit from './AddOrEdit.vue'
import PackageDetail from '../../components/PackageDetail.vue'
import { _getPackageProxyList, _deletePackageProxy, _exportPackageProxy, getPackageClassify } from '@/api/order.js'
import { mapGetters } from 'vuex'
export default {
  components: {
    AddOrEdit,
    PackageDetail
  },
  data() {
    return {
      detailType: 1, // 套餐详情弹窗类型 0-新增 1-详情 2-编辑
      packInfo: {}, // 选择的套餐代理信息
      editType: 1, // 编辑弹窗类型：1-新增 2-编辑
      AddOrEditVisible: false,
      detailVisible: false,
      dataLoading: false,
      tableData: [],
      total: 0,
      searchParams: {
        pageIndex: 1,
        pageSize: 15
      },
      packageTypeList: []
    }
  },
  computed: {
    ...mapGetters(['userType', 'userId', 'bisLevel', 'bisUserId', 'currencyParams'])
  },
  created() {
    this.getPackageClassifyList()
  },
  methods: {
    // 查询
    search(info) {
      this.searchParams = {
        ...{
          pageIndex: 1,
          pageSize: 15
        },
        ...info
      }

      this.getPackageProxyList()
    },
    // 打开套餐详情
    showDetailDialog(row, type) {
      this.detailType = type
      this.packInfo = row
      this.detailVisible = true
    },
    // 打开新增、编辑弹窗
    showAddOrEditDialog(row, type) {
      this.editType = type
      if (type === 1) {
        let { serviceProviderId, ...info } = row
        this.packInfo = info
      } else if (type === 2) {
        this.packInfo = row
      }
      this.AddOrEditVisible = true
    },
    /* downloadExcel */
    emitExport() {
      if (this.tableData.length == 0) {
        this.$message.warning(this.$t('lg.noData'))
        return
      }
      let { pageIndex, pageSize, ...param } = this.searchParams
      _exportPackageProxy(param).then(res => {
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel;charset=UTF-8' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
      })
    },
    /* getTableData */
    async getPackageProxyList() {
      try {
        let res = await _getPackageProxyList(this.searchParams)
        if (res.ret === 1) {
          this.tableData = res.data || []
          this.total = res.total || 0
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 点击删除
    confirmDelete(row) {
      this.$confirm(this.$t('fPpUDmYmZBfijNxuYo-dT'), this.$t('lg.notification'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      }).then(() => {
        this.deletePackageProxy(row.agentConfigId)
      })
    },
    // 删除套餐代理
    async deletePackageProxy(agentConfigId) {
      try {
        let res = await _deletePackageProxy({ agentConfigId })
        if (res.ret === 1) {
          this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
          this.getPackageProxyList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 编辑成功
    handleSuccess() {
      this.getPackageProxyList()
    },
    /* changeCurrentPage */
    handleCurrentChange(page) {
      this.searchParams.pageIndex = page
      this.getPackageProxyList()
    },
    /* changeTableRowSize */
    handleSizeChange(size) {
      this.searchParams.pageSize = size
      this.getPackageProxyList()
    },
    // 翻译函数
    te(arg) {
      const hasKey = this.$t(arg)
      if (hasKey) {
        const result = this.$t(arg)
        return result
      }
      return arg
    },
    async getPackageClassifyList() {
      const result = await getPackageClassify()
      this.packageTypeList = result.data.reduce((pre, cur) => {
        pre.push(...cur.childrens)
        return pre
      }, [])
    },
    getCurrentPackageType(code) {
      return this.packageTypeList.find(item => item.code === code)?.name || '-'
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0px 20px 10px;
  height: 0;
  flex: 1;
  .el-table {
    width: 100% !important;
    ::v-deep .el-table__header,
    ::v-deep .el-table__body {
      width: 100% !important;
    }
  }
}

// 对齐根节点 无论有无子节点
::v-deep .el-table__row:not([class*='el-table__row--level-']) {
  td:first-child {
    padding-left: 24px;
  }
}
::v-deep .el-table__placeholder {
  margin-right: 3px;
}
</style>
