import Vue from 'vue'
import VueRouter from 'vue-router'
Vue.use(VueRouter)
//获取原型对象上的push函数
const originalPush = VueRouter.prototype.push
//修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

/* Layout */
import Layout from '@/layout'

export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    hidden: true,
    component: () => import('@/views/Login/index'),
    meta: { title: '', icon: 'home', trans: 'login' }
  },
  {
    path: '/appdownload',
    name: 'Appdownload',
    hidden: true,
    component: () => import('@/views/CustomMade/Appdownload'),
    meta: { title: '应用下载', icon: 'home', trans: 'appdownload' }
  },
  {
    path: '/positionApp',
    name: 'positionApp',
    hidden: true,
    component: () => import('@/views/CustomMade/PositionApp'),
    meta: { title: '应用下载', icon: 'home', trans: 'appdownload' }
  },
  // {
  //   path: '/invite',
  //   name: 'Invite',
  //   hidden: true,
  //   component: () => import('@/views/H5/Invite/index.vue'),
  //   meta: { title: '在这儿-全球动态物联网平台', auth: false }
  // },
  {
    path: '/autologin',
    name: 'AutoLogin',
    hidden: true,
    component: () => import('@/views/AutoLogin/index'),
    meta: { title: '免登录测试', icon: 'home', trans: 'login' }
  },
  {
    path: '/v2/autologin',
    name: 'AutoLoginV2',
    hidden: true,
    component: () => import('@/views/AutoLoginV2/index'),
    meta: { title: '免登录测试', icon: 'home', trans: 'login' }
  },
  {
    path: '/info',
    component: Layout,
    meta: {
      icon: 'edit'
    },
    hidden: true,
    children: [
      {
        path: '/info/instruction',
        component: () => import('@/views/Information/Instruction'),
        name: 'Instruction',
        meta: { title: '操作说明', auth: true, trans: 'Info_Instruction' },
        hidden: true
      },
      {
        path: '/info/question',
        component: () => import('@/views/Information/Question'),
        name: 'Question',
        meta: { title: '常见问题', auth: true, trans: 'Info_Question' },
        hidden: true
      },
      {
        path: '/info/allcenter',
        component: () => import('@/views/Information/AllCenter'),
        name: 'AllCenter',
        meta: { title: '消息中心', auth: true, trans: 'Message_Center' },
        redirect: '/info/allcenter/messagecenter',
        hidden: true,
        children: [
          {
            path: '/info/allcenter/messagecenter',
            component: () => import('@/views/Information/Message/subpages/MessageCenter'),
            name: 'MessageCenter',
            meta: { title: '消息中心', auth: true, trans: 'Message_Center' }
          },
          {
            path: '/info/allcenter/warningdetail',
            component: () => import('@/views/Information/Message/subpages/WarningDetail'),
            name: 'WarningDetail',
            meta: { title: '告警消息', auth: true, trans: 'Alarm_message' }
          },
          {
            path: '/info/allcenter/stationdetail',
            component: () => import('@/views/Information/Message/subpages/StationDetail'),
            name: 'StationDetail',
            meta: { title: '站内消息', auth: true, trans: 'Station_message' }
          },
          {
            path: '/info/allcenter/renewal',
            component: () => import('@/views/Information/Message/subpages/BulletinCenter'),
            name: 'Renewal',
            meta: { title: '更新公告', auth: true, trans: 'Update_bulletin' }
          }
        ]
      },
      {
        path: '/viewbulletin',
        name: 'ViewBulletin',
        component: () => import('@/views/message/ViewBulletin.vue'),
        meta: {
          title: '公告详情',
          trans: 'proclamat_manager_list',
          auth: true,
          keepAlive: false
        }
      },
      {
        path: '/info/personalcenter',
        component: () => import('@/views/Information/PersonalCenter/index'),
        name: 'PersonalCenter',
        meta: { title: '个人中心', auth: true, trans: 'Personal_Center' },
        beforeEnter: (to, from, next) => {
          //  如果是终端用户，跳到终端用户的个人中心
          if (Vue.$cookies.get('userType') == 5) next('/info/terminalcenter')
          next()
        },
        hidden: true
      },
      {
        path: '/info/terminalcenter',
        component: () => import('@/views/Information/TerminalCenter'),
        name: 'TerminalCenter',
        meta: { title: '个人中心', auth: true, trans: 'Personal_Center' },
        beforeEnter: (to, from, next) => {
          //  如果不是终端用户，跳到普通用户的个人中心
          if (Vue.$cookies.get('userType') != 5) next('/info/personalcenter')
          next()
        },
        hidden: true
      }
    ]
  },
  {
    path: '/findpassword',
    component: () => import('@/layout/SimpleHead'),
    name: 'FindPassword',
    meta: { title: '找回密码', exits: true },
    redirect: '/findpassword/forget',
    hidden: true,
    children: [
      {
        path: '/findpassword/forget',
        component: () => import('@/views/Information/Password/Forget'),
        name: 'Forget',
        meta: { title: '填写账号', exits: true, trans: 'Find_Password' },
        hidden: true
      },
      {
        path: '/findpassword/validphone',
        component: () => import('@/views/Information/Password/ContactServe'),
        name: 'ValidPhone',
        meta: { title: '联系服务商', exits: true, trans: 'Contact_Provider' },
        hidden: true
      }
    ]
  },
  {
    path: '/track',
    name: 'Track',
    component: () => import('@/views/Monitor/track'),
    meta: {
      title: '跟踪',
      trans: 'track',
      auth: false,
      icon: 'location',
      exits: true
    },
    hidden: true
  },
  {
    path: '/trackShare',
    name: 'TrackShare',
    component: () => import('@/views/Monitor/TrackShare/index.vue'),
    meta: {
      title: '',
      trans: 'track',
      auth: false,
      exits: true
    },
    hidden: true
  },
  {
    path: '/outchain',
    name: 'OutChain',
    component: () => import('@/views/OutChain/index.vue'),
    meta: {
      title: '工厂验证',
      exits: true,
      auth: false
    },
    hidden: true
  },
  {
    path: '/adsadsm',
    name: 'AdsaDsm',
    component: () => import('@/views/AdsaDsm/index.vue'),
    meta: {
      title: '报警详情',
      exits: true,
      auth: false
    },
    hidden: true
  },
  {
    path: '/machine',
    name: 'Machine',
    component: () => import('@/views/MachineType/index.vue'),
    meta: {
      title: '设备型号管理',
      trans: 'machine_type_manage',
      auth: false
    },
    hidden: true
  },
  {
    path: '/oilPreview',
    name: 'OilPreview',
    component: () => import('@/views/Preview/OilVolumePreview'),
    meta: {
      title: '油量总览',
      trans: 'track',
      auth: false
    },
    hidden: true
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/index',
    meta: {
      icon: 'edit',
      trans: 'Account_Home',
      auth: true
    },
    children: [
      {
        path: '/dashboard',
        component: () => import('@/views/Dashboard/index'),
        name: 'Dashboard',
        meta: { title: '概况', trans: 'Account_Home', auth: true, icon: 'home' }
      }
    ]
  },
  {
    path: '/monitor',
    component: Layout,
    redirect: '/monitor/index',
    meta: {
      trans: 'Location_monitoring',
      auth: true
    },
    children: [
      {
        path: '/monitor',
        // component: () => import('@/views/Monitor/index.vue'),
        component: () => import('@/views/Monitor/RealTime/index.vue'),
        name: 'Monitor',
        meta: { title: '定位监控', trans: 'Location_monitoring', auth: true, icon: 'location' }
      }
    ]
  },
  {
    path: '/alarm',
    name: 'Alarm',
    component: () => import('@/views/Monitor/alarm'),
    meta: {
      title: '报警',
      trans: 'Call_the_police',
      auth: false,
      icon: 'location',
      exits: true
    },
    hidden: true
  },
  {
    path: '/history',
    name: 'History',
    component: () => import('@/views/Monitor/history'),
    meta: {
      title: '回放',
      trans: 'Replay',
      auth: true,
      icon: 'location',
      exits: true
    },
    hidden: true
  },
  {
    path: '/line',
    name: 'Line',
    component: () => import('@/views/Line/index.vue'),
    redirect: '/line/setting',
    meta: {
      title: '线路管理',
      trans: 'line_manage',
      auth: true,
      icon: 'location',
      exits: true
    },
    hidden: true,
    children: [
      {
        path: '/line/setting',
        component: () => import('@/views/Line/LineSetting'),
        name: 'LineSetting',
        meta: {
          title: '线路管理',
          trans: 'line_manage',
          auth: true,
          icon: 'location',
          exits: true
        }
      },
      {
        path: '/line/alarmSetting',
        component: () => import('@/views/Line/LineAlarmSetting'),
        name: 'LineAlarmSetting',
        meta: {
          title: '线路管理',
          trans: 'line_manage',
          auth: true,
          icon: 'location',
          exits: true
        }
      }
    ]
  },
  {
    path: '/videomonitor',
    component: Layout,
    redirect: '/videomonitor/index',
    meta: { trans: 'Video_Monitor', icon: 'video_monitor', auth: true, exits: false },
    alwaysShow: true,
    // index: { redirect: 'TimeVideo' },
    children: [
      {
        path: '/videomonitor/index',
        // component: () => import('@/views/MyClient/index'),
        component: () => import('@/views/VideoMonitor/RealTimeVideo'),
        name: 'TimeVideo',
        meta: { title: '实时视频', trans: 'Time_Video', auth: true, exits: false }
      },
      {
        path: '/eventcenter',
        component: () => import('@/views/VideoMonitor/EventCenter/index'),
        name: 'EventCenter',
        meta: { title: '事件中心', trans: 'Event_Center', auth: true, exits: false }
      },
      {
        path: '/playbackvideo',
        component: () => import('@/views/VideoMonitor/PlaybackVideo/PlayBack'),
        name: 'PlaybackVideo',
        meta: { title: '视频回放', trans: 'Playback_Video', auth: true, exits: false }
      },
      {
        path: '/evidence',
        component: () => import('@/views/VideoMonitor/VideoEvidence/Evidence'),
        name: 'VideoEvidence',
        meta: { title: '视频任务', trans: 'Video_Task', auth: true, exits: false }
      },
      {
        path: '/bigFullScreen',
        name: 'bigFullScreen', // EventTask
        meta: { title: '监控大屏', trans: 'bigFullScreen', auth: true, exits: false }
      }
    ]
  },
  {
    path: '/myclient',
    component: Layout,
    redirect: '/myclient/index',
    meta: { trans: 'My_client', icon: 'my_client', spareTrans: 'My_business', auth: true },
    alwaysShow: true,
    index: { redirect: 'MyClient' },
    children: [
      {
        path: '/myclient/index',
        component: () => import('@/views/MyClient/ClientIndex/index.vue'),
        name: 'MyClient',
        meta: { title: '我的客户', trans: 'My_client', spareTrans: 'My_client', auth: true }
      },
      {
        path: '/devicerecord',
        component: () => import('@/views/MyClient/DeviceRecord'),
        name: 'DeviceRecord',
        meta: { title: '设备记录', trans: 'device_record', auth: true }
      },
      {
        path: '/maintain-log',
        component: () => import('@/views/MyClient/maintain-log'),
        name: 'MaintainLog',
        meta: { title: '保养记录', trans: 'car_maintain_list', auth: true }
      },
      {
        path: '/share-log',
        component: () => import('@/views/MyClient/share-log'),
        name: 'ShareLog',
        meta: { title: '分享管理', trans: 'Sharing_management', auth: true }
      },
      {
        path: '/user-managment',
        component: () => import('@/views/MyClient/UserManagement/index.vue'),
        name: 'usermanagmen',
        meta: { title: '用户管理', trans: 'User_management', auth: true }
      },
      {
        path: '/deviceInfo',
        component: () => import('@/views/MyClient/DeviceInfo/index.vue'),
        name: 'deviceInfo',
        meta: { title: '设备详情', trans: 'device_info' }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/point/manager',
    meta: {
      icon: 'asset',
      title: '财务中心',
      trans: 'finance_center',
      auth: true
    },
    children: [
      {
        path: '/point/manager',
        component: () => import('@/views/finance/AssetManage/index'),
        name: 'AssetManage',
        meta: { title: '资产管理', trans: 'asset_manager', auth: true }
      },
      {
        path: '/finance/package',
        component: () => import('@/views/finance/PackageManage/index'),
        name: 'PackageManage',
        meta: { title: '套餐管理', trans: 'package_manager', auth: true }
      },
      {
        path: '/finance/fanagement',
        component: () => import('@/views/finance/FinancialManagement/index'),
        name: 'FinancialManagement',
        meta: { title: '财务管理', trans: 'financial_management', auth: true }
      }
    ]
  },
  {
    path: '/Statistics',
    component: Layout,
    redirect: '/Statistics/index',
    meta: { trans: 'Statistical_report', auth: true },
    children: [
      {
        path: '/Statistics',
        component: () => import('@/views/Statistics/index'),
        name: 'Statistics',
        meta: { title: '数据统计', trans: 'Statistical_report', auth: true, icon: 'statistics' }
      }
    ]
  },
  {
    path: '/device',
    component: Layout,
    redirect: '/device/index',
    meta: { trans: 'Equipment_management' },
    name: 'DeviceManagerParent',
    children: [
      {
        path: '/device/index',
        component: () => import('@/views/DeviceManager/index'),
        name: 'DeviceManager',
        meta: { title: '我的设备', trans: 'Equipment_management', auth: true, icon: 'device' }
      }
    ]
  },
  {
    path: '/permission',
    component: Layout,
    redirect: '/permission/roles',
    meta: {
      title: '权限管理',
      trans: 'auth_manager',
      icon: 'permission',
      auth: true
    },
    index: { redirect: 'Roles' },
    children: [
      {
        path: '/permission/roles',
        component: () => import('@/views/Permission/Roles'),
        name: 'Roles',
        meta: { title: '角色管理', trans: 'Role_management', auth: true }
      },
      {
        path: '/permission/account',
        component: () => import('@/views/Permission/Account'),
        name: 'Account',
        meta: { title: '权限账号', trans: 'System_account_management', auth: true }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    alwaysShow: true, // will always show the root menu
    meta: {
      title: '系统管理',
      trans: 'system_manager',
      icon: 'setting',
      auth: true
    },
    index: { redirect: 'KeyManager' },
    children: [
      {
        path: '/system/menu',
        component: () => import('@/views/System/Menu'),
        name: 'Menu',
        meta: { title: '菜单配置', trans: 'Menu_management', auth: true }
      },
      {
        path: '/system/operatelogs',
        component: () => import('@/views/System/OperateLogs.vue'),
        name: 'Operatelogs',
        meta: { title: '操作日志', trans: 'Log_management', auth: true }
      },
      {
        path: '/system/keymanager',
        component: () => import('@/views/System/GeoKey/index.vue'),
        name: 'KeyManager',
        meta: { title: 'Key管理', trans: 'GeoKey', auth: true }
      },
      {
        path: '/system/feedback',
        name: 'Feedback',
        component: () => import('@/views/System/Feedback.vue'),
        meta: {
          title: '用户反馈',
          trans: 'feedback_manager',
          auth: true,
          noCache: false,
          reload: false
        }
      },
      {
        path: '/system/fbview',
        name: 'FeedbackView',
        component: () => import('@/views/System/FeedbackView.vue'),
        meta: {
          title: '反馈详情',
          trans: 'feedback_list',
          auth: true
        },
        hidden: true
      },
      {
        path: '/system/bulletin',
        name: 'Bulletin',
        component: () => import('@/views/System/Bulletin.vue'),
        meta: {
          title: '公告管理',
          trans: 'proclamat_manager',
          auth: true,
          noCache: false,
          reload: false
        }
      },
      {
        path: '/updatebulletin',
        name: 'AddUpdateBulletin',
        component: () => import('@/views/System/AddUpdateBulletin.vue'),
        meta: {
          title: '新增公告',
          trans: 'proclamat_manager',
          auth: true
        },
        hidden: true
      },
      {
        path: '/updateactivity',
        name: 'AddUpdateActivity',
        component: () => import('@/views/System/AddUpdateActivity.vue'),
        meta: {
          title: '新增公告',
          trans: 'proclamat_manager',
          auth: true
        },
        hidden: true
      },
      {
        path: '/system/replace',
        name: 'Replace',
        component: () => import('@/views/System/Replace.vue'),
        meta: {
          title: '更换IMEI',
          trans: 'REPLACE',
          auth: true
        }
      },
      {
        path: '/system/config',
        name: 'Config',
        component: () => import('@/views/System/ConfigManagement/index.vue'),
        meta: { title: '配置管理', trans: 'config_management', auth: true }
      },
      {
        path: '/system/command',
        name: 'CommandConfiguration',
        component: () => import('@/views/System/ConfigManagement/CommandConfiguration'),
        meta: { title: '指令配置', trans: 'config_management', auth: false, isHide: true }
      },
      {
        path: '/system/manage',
        name: 'ConfigurationManagement',
        component: () => import('@/views/System/ConfigManagement/ConfigManagement'),
        meta: { title: '配置管理', trans: 'custom_model', auth: false, isHide: true }
      },
      {
        path: '/system/customize',
        name: 'CustomizeManagement',
        component: () => import('@/views/System/CustomModel'),
        meta: { title: '自定义型号', trans: 'custom_model', auth: false }
      },
      {
        path: '/system/channel',
        name: 'ChannelManagement',
        component: () => import('@/views/System/Channel'),
        meta: { title: '渠道管理', trans: 'Channel_Manage', auth: false }
      },
      {
        path: '/system/channelDataDetail',
        name: 'ChannelDataDetail',
        component: () => import('@/views/System/Channel/dataChannelDetail.vue'),
        meta: { title: '渠道管理', trans: 'Channel_Manage', auth: false, isHide: true }
      },
      {
        path: '/system/update',
        name: 'AppUpdate',
        component: () => import('@/views/System/App'),
        meta: { title: 'APP管理', trans: 'app_update', auth: false }
      }
    ]
  },
  {
    path: '/feedbacklist',
    name: 'FeedbackList',
    component: () => import('@/views/message/FeedbackList.vue'),
    meta: {
      title: '意见反馈',
      auth: true,
      noCache: false,
      reload: false,
      exits: true,
      keepAlive: true
    },
    hidden: true
  },
  {
    path: '/feedbackdetail',
    name: 'FeedbackDetail',
    component: () => import('@/views/message/FeedbackDetail.vue'),
    meta: {
      title: '反馈详情',
      auth: true,
      exits: true,
      keepAlive: false
    },
    hidden: true
  },
  {
    path: '/platformbl',
    name: 'BulletinList',
    component: () => import('@/views/message/BulletinList.vue'),
    meta: {
      title: '平台通知',
      auth: true,
      noCache: false,
      reload: false,
      exits: true
    },
    hidden: true
  },
  {
    path: '/platformbldetail',
    name: 'BulletinDetail',
    component: () => import('@/views/message/BulletinDetail.vue'),
    meta: {
      title: '通知详情',
      auth: true,
      exits: true
    },
    hidden: true
  },
  {
    path: '/edittopic',
    name: 'EditTopic',
    component: () => import('@/views/Topic/EditTopic.vue'),
    meta: {
      title: '热点问题详情',
      exits: true,
      auth: true
    },
    hidden: true
  },
  {
    path: '/topiclist',
    name: 'TopicList',
    component: () => import('@/views/Topic/TopicList.vue'),
    meta: {
      title: '热点问题',
      exits: true,
      auth: true
    },
    hidden: true
  },
  /** 匹配不到页面 **/
  {
    path: '*',
    redirect: '/',
    meta: {
      exits: true
    },
    hidden: true
  }
]

const createRouter = () =>
  new VueRouter({
    mode: 'hash',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()
// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
