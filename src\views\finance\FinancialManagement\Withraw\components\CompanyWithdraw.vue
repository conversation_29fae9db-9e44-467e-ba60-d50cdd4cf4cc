<template>
  <div class="company" v-loading="loading">
    <div class="top-info-empty" v-if="!accountInfo">
      <div class="img-border"><img src="@/assets/img/none.png" alt="" /></div>
      <span class="tip-msg">{{ $t('GZy7CzuyKTwsBKOmuCs1T') }}</span>
      <span style="color: #3370ff;cursor: pointer" @click="toCompanyAccount(1)">{{ $t('lg.limits.GeoKey_Add') }}</span>
    </div>
    <div class="top-info" v-else>
      <div class="img-border"><img src="@/assets/img/company1.png" alt="" /></div>
      <div class="right">
        <div class="item">{{ $t('4ox8EnAHNJ1ge4l1vollX') }}：{{ accountInfo.withdrawalAccountName }}</div>
        <div class="receive-info">
          <span>{{ $t('-2qRzHpb3yJe-NXYwm9Q7') }}：</span>
          <div>
            <div>{{ accountInfo.bankName }}</div>
            <br />
            <div>{{ receivingAccountStr }}</div>
          </div>
        </div>
      </div>
      <div class="btn" @click="toCompanyAccount(2)">{{ $t('7SBVFKuCpv5odq1HN33Ln') }}</div>
    </div>
    <div class="split"></div>
    <div class="bottom-content">
      <el-form :model="form" status-icon :rules="rules" ref="form" label-width="auto">
        <el-form-item :label="$t('pHaDrlW6Ws3jwOkuAbQoM') + ':'" prop="invoiceUrl" class="receipt">
          <BtnUpload
            ref="BtnUpload"
            url="/imageCommon/upload.do"
            :coverUpload="true"
            :beforeUpload="beforeReceiptUpload"
            :tipMessage="$t('qR0w92Dg9fchUpj2DzSC_')"
            :expMessage="$t('LgRB21jCFOEI2iFWMhk_u')"
            @viewExample="viewExample"
            @on-success="handleInvoiceUrlSuccess"
            @on-remove="handleInvoiceRemove"
            @on-preview="handleInvoicePreview"
          />
        </el-form-item>
        <el-form-item :label="$t('aBPmzVAoqSZiJWXyn9bN-') + ':'" prop="taxRate">
          <el-input style="width: 310px" v-model="form.taxRate" autocomplete="off" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('CqTkd8dVrd22g3nDNVCoy') + ':'" prop="withdrawalsAmount">
          <el-input style="width: 310px" v-model="form.withdrawalsAmount" disabled></el-input>
          <!-- <div class="withdraw-tip">{{ $t('fU-37Env5DcFrKxRb3xsW', [1]) }}</div> -->
        </el-form-item>
        <el-form-item :label="$t('vx8cJaHHxhErDo_u98V9g') + ':'" prop="chargeRate">
          <el-input style="width: 310px" v-model="form.chargeRate" autocomplete="off" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('JN6k5nduoDoNFRKZS3DEn') + ':'" prop="deductionAmount">
          <span slot="label">
            <div style="display:flex;align-items:center">
              {{ $t('JN6k5nduoDoNFRKZS3DEn') + ':' }}
              <el-tooltip placement="top">
                <div slot="content" style="width: 200px;">
                  {{
                    `${$t('JN6k5nduoDoNFRKZS3DEn')}=${$t('CqTkd8dVrd22g3nDNVCoy')}+{${$t('CqTkd8dVrd22g3nDNVCoy')}/(1+${$t('aBPmzVAoqSZiJWXyn9bN-')})*(6%-${$t(
                      'aBPmzVAoqSZiJWXyn9bN-'
                    )})*(1+12%)}+${$t('CqTkd8dVrd22g3nDNVCoy')}*1%`
                  }}
                  <!-- 扣除金额=提现金额+{提现金额/(1+税率)*(6%-税率)*(1+12%)}+提现金额*1% -->
                </div>
                <img style="cursor: pointer;margin-left:2px" src="@/assets/img/tip1.png" alt="" />
              </el-tooltip>
            </div>
          </span>
          <el-input style="width: 310px" v-model="form.deductionAmount" autocomplete="off" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('eKj89wVWw918dADwmoY6a') + ':'" prop="phone">
          <el-input style="width: 310px" v-model.number="form.phone" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('ZcMHgev4PYIoGliHjPmtn') + ':'" prop="verificationCode">
          <VerifyCode :phone="form.phone" ref="VerifyCode" @get-code="getPhoneCode" />
        </el-form-item>
      </el-form>
    </div>
    <el-image class="my-img" ref="myImg" :src="imageUrl" :preview-src-list="srcList"> </el-image>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createUUID } from '@/utils/common.js'
import { _getCompanyAccountInfo, _getServiceProviderInfo, _getCcrInfo } from '@/api/order.js'
import BtnUpload from '@/components/Upload/BtnUpload.vue'
import VerifyCode from '@/components/Common/VerifyCode.vue'
import { add, subtract, multiply, divide } from '@/utils/float.js'
export default {
  props: {
    validBalance: {
      type: [String, Number],
      default: ''
    },
    enterpriseInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { BtnUpload, VerifyCode },
  data() {
    return {
      loading: false,
      uploadError: {
        invoiceError: false
      },
      accountInfo: null,
      form: {
        invoiceUrl: '',
        withdrawalsAmount: '',
        deductionAmount: '',
        phone: '',
        verificationCode: '',
        uuid: '',
        chargeRate: '1%',
        taxRate: '',
        serviceCharge: '',
        invoiceName: '',
        purchaserName: '',
        purchaserTaxNumber: '',
        sellerName: '',
        sellerTaxNumber: ''
      },
      rules: {
        invoiceUrl: [{ required: true, message: this.$t('9OPyc0VO0-5p0d5Qed7Qy'), trigger: 'blur' }],
        verificationCode: [{ required: true, message: this.$t('prX3JUTouWcIjV3_duH37'), trigger: 'blur' }]
      },
      srcList: [],
      imageUrl: ''
    }
  },
  computed: {
    ...mapGetters(['currencySymbol', 'currencyParams']),
    receivingAccountStr() {
      let replacedString
      if (this.accountInfo) {
        let receivingAccount = this.accountInfo.receivingAccount
        let startIndex = 0
        let endIndex = receivingAccount.length - 4 // 倒数第四位的索引是字符串长度减去5

        replacedString =
          receivingAccount.substring(0, startIndex) + receivingAccount.substring(startIndex, endIndex).replace(/./g, '*') + receivingAccount.substring(endIndex)
      }
      return replacedString
    }
  },
  mounted() {
    this.getCompanyAccountInfo()
    this.form.phone = this.enterpriseInfo.phone
  },
  methods: {
    // 查询
    search() {
      this.getCompanyAccountInfo()
    },
    setAmount() {
      this.form.deductionAmount = ''

      // let deductionAmount = (Number(this.form.withdrawalsAmount) + Number(this.form.withdrawalsAmount * 0.01) + 0.********* + Number.EPSILON).toFixed(2)
      //扣除金额=提现金额+{提现金额/(1+税率)*(6%-税率)*(1+12%)}+提现金额*1%（1020修改）

      // 提现金额
      let withdrawal = Number(this.form.withdrawalsAmount)
      //税率
      let taxRate = parseFloat(this.form.taxRate) / 100
      let tax1 = divide(withdrawal, 1 + taxRate)
      let tax2 = multiply(tax1, 0.06 - taxRate)
      let tax3 = multiply(tax2, 1.12)
      //税差额这一项也向上取整
      let finalTax = this.toFixedCeil(tax3, 2)

      //手续费，向上取整2位
      let serviceCharge = this.toFixedCeil(withdrawal * 0.01, 2)
      this.form.serviceCharge = serviceCharge

      let total1 = add(withdrawal, finalTax)
      let totalAmount = add(total1, serviceCharge)
      console.log(withdrawal, tax1, tax2, tax3, finalTax, serviceCharge, total1, totalAmount)

      if (totalAmount > this.validBalance) {
        this.$message.warning(this.$t('VDbpijEe2xTelunTQS-4e'))
      } else {
        this.form.deductionAmount = totalAmount
      }
    },
    //向上取整金额并且保留两位小数点
    toFixedCeil(num, decimal) {
      return (Math.ceil(num * Math.pow(10, decimal)) / Math.pow(10, decimal)).toFixed(decimal)
    },
    // 设置手机验证码
    getPhoneCode(code, uuid) {
      this.form.verificationCode = code
      this.form.uuid = uuid
    },
    // 查询账户信息
    async getCompanyAccountInfo() {
      debugger
      let res = await _getCompanyAccountInfo({ serviceProviderId: this.$cookies.get('bisUserId') })
      if (res.ret == 1) {
        if (res.data && res.data.receivingAccount) {
          this.accountInfo = res.data
        } else {
          this.accountInfo = null
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 发票上传校验
    beforeReceiptUpload(file) {
      let type = file.type || (file.raw && file.raw.type) || ''
      let isValid = ['application/pdf'].indexOf(type)

      if (!type || isValid == -1 || file.size / 1024 / 1024 > 5) {
        this.$message.warning(this.$t('OWZM_rW5ca_xlQz_J8Xvy'))
        return false
      }
      return true
    },
    // 发票上传成功
    async handleInvoiceUrlSuccess({ type, file, data }) {
      this.form.invoiceUrl = data.imagePath || ''
      this.$refs['form'].clearValidate('invoiceUrl')

      try {
        this.loading = true
        let res = await _getCcrInfo({ file, type: 5 })
        if (res.ret == 1) {
          this.loading = false
          if (parseFloat(res.data.taxRate) / 100 > 0.06) {
            //发票税率不能大于6%
            this.$message.error(this.$t('MxKJZnXtEbLQy_CJM4Wo0'))
            this.$refs.BtnUpload.reset()
            return
          }
          this.uploadError.invoiceError = false
          this.form.withdrawalsAmount = res.data.amountInFiguers || ''
          const { taxRate, invoiceName, purchaserName, purchaserTaxNumber, sellerName, sellerTaxNumber } = res.data
          this.form = {
            ...this.form,
            taxRate,
            invoiceName,
            purchaserName,
            purchaserTaxNumber,
            sellerName,
            sellerTaxNumber
          }
          this.setAmount()
        } else {
          this.uploadError.invoiceError = true
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      } finally {
        this.loading = false
      }
    },
    // 发票删除
    handleInvoiceRemove() {
      this.form.invoiceUrl = ''
      this.form.withdrawalsAmount = ''
      this.form.deductionAmount = ''
      this.form.taxRate = ''
      this.uploadError.invoiceError = false
    },
    // 发票预览
    handleInvoicePreview({ info }) {
      window.open(info.url, '_blank')
    },
    getParams() {
      if (!this.accountInfo) {
        this.$message.warning(this.$t('n_gZB7je9SGq7ENwoME-1'))
        return
      }
      if (this.uploadError.invoiceError) {
        this.$message.warning(this.$t('oJ1yZ_MvfuN0QEeBrJW39'))
        return
      }
      if (!this.form.withdrawalsAmount) {
        this.$message.warning(this.$t('JmBSnAvO4DN3XSi1cxZnb'))
        return
      }
      if (!this.form.deductionAmount) {
        this.$message.warning(this.$t('Z0Tm_VlEKyRO3z_N1zVo5'))
        return
      }
      if (!this.form.phone) {
        this.$message.warning(this.$t('85Bv3yRHBX1Q-bx2fsC7i'))
        return
      }
      let params
      this.$refs['form'].validate(valid => {
        if (valid) {
          params = {
            ...this.form,
            bankName: this.accountInfo.bankName,
            receivedAmount: this.form.withdrawalsAmount,
            currency: this.currencyParams,
            receiver: this.accountInfo.withdrawalAccountName,
            receivingAccount: this.accountInfo.receivingAccount,
            serviceCharge: this.form.serviceCharge,
            serviceProviderId: this.$cookies.get('bisUserId'),
            type: 1,
            withdrawalType: 1,
            chargeRate: parseFloat(this.form.chargeRate),
            taxRate: parseFloat(this.form.taxRate)
          }
        }
      })
      return params
    },
    // 重置
    reset() {
      this.loading = false
      this.uploadError = {
        invoiceError: false
      }
      this.$refs.BtnUpload.reset()
      this.accountInfo = null
      this.form = {
        invoiceUrl: '',
        withdrawalsAmount: '',
        deductionAmount: '',
        phone: '',
        verificationCode: '',
        uuid: '',
        chargeRate: '1%',
        taxRate: '',
        serviceCharge: ''
      }
      this.$refs.VerifyCode.reset()
      this.$refs['form'].resetFields()
    },
    // 前往账户管理
    toCompanyAccount(type) {
      this.$emit('company-account', { type })
    },
    viewExample() {
      this.srcList = ['https://seeworld-lbs-app.oss-cn-beijing.aliyuncs.com/ljdw-pc/examplePic/electronic-invoice.png']
      this.$refs.myImg.showViewer = true
    }
  }
}
</script>

<style lang="scss" scoped>
.company {
  .my-img {
    display: none;
  }
  .top-info-empty {
    height: 168px;
    background-color: #f8faff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    .tip-msg {
      color: #262626;
      margin: 8px 0 9px 0;
    }
    img {
      width: 60px;
      height: 65px;
    }
  }
  .top-info {
    padding-left: 55px;
    display: flex;
    align-items: center;
    height: 168px;
    background-color: #f8faff;
    font-size: 12px;
    position: relative;
    img {
      width: 57px;
      height: 57px;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 16px;
      color: #262626;
      .item {
        margin-bottom: 13px;
      }
      .receive-info {
        display: flex;
      }
    }
    .btn {
      position: absolute;
      cursor: pointer;
      color: #3370ff;
      top: 20px;
      right: 20px;
    }
  }
  .split {
    height: 1px;
    background-color: #e9e9e9;
    margin: 20px 0;
  }
  .bottom-content {
    .withdraw-tip {
      position: absolute;
      color: #ff6648;
      font-size: 12px;
      top: 26px;
    }
  }
}
::v-deep .el-form {
  .el-form-item__label {
    height: 32px;
    line-height: 32px;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
