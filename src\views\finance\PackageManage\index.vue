<template>
  <div class="wrapper-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="cus-tab">
      <el-tab-pane lazy :label="$t('lg.limits.package_list')" name="first" v-if="permissionArr.indexOf('finance:package:list') !== -1"
        ><PackageList @changeTab="changeTab"
      /></el-tab-pane>
      <el-tab-pane lazy :label="$t('lg.limits.package_proxy')" name="second" v-if="[0].indexOf(+userType) !== -1 || [1, 2].indexOf(+bisLevel) !== -1"
        ><PackageProxy @changeTab="changeTab" ref="second"
      /></el-tab-pane>
      <el-tab-pane lazy :label="$t('lg.limits.package_setting')" name="third" v-if="[0].indexOf(+userType) !== -1"><PackageSetting ref="third"/></el-tab-pane>
      <el-tab-pane
        lazy
        :label="$t('1Lm2fks-D28PGJDB2-QEm')"
        name="fourth"
        v-if="permissionArr.includes('finance:package:gift_record') || [1681, 8108].indexOf(+userInfo.userId) !== -1"
        ><DonateRecord ref="fourth"
      /></el-tab-pane>
      <el-tab-pane
        lazy
        :label="$t('lg.transRecord')"
        name="fifth"
        v-if="permissionArr.includes('finance:package:gift_record') || [1681, 8108].indexOf(+userInfo.userId) !== -1"
        ><TransferRecord ref="fifth"
      /></el-tab-pane>
    </el-tabs>
    <div class="right-btn">
      <span class="cursor-point ljdw-color-primary ljdw-pr-8" v-if="[0, 8].indexOf(+userType) !== -1" @click="showPackageTrasfer">{{
        $t('eg65yL7TTeZtMUi-SI7Rf')
      }}</span>
      <!-- <span class="cursor-point ljdw-color-primary" v-if="[8].indexOf(+userType) !== -1" @click="showPackageDonate">{{ $t('9NTH28CxVugtC2EK38qjg') }}</span> -->
    </div>
    <!-- 套餐转移 -->
    <PackageTransfer ref="PackageTransfer" :visible.sync="packageTransferVisible" @service-detail="showServiceDetail" @success="packageTransferSuccess" />
    <!-- 套餐赠送 -->
    <PackageDonate ref="PackageDonate" :visible.sync="packageDonateVisible" @package-detail="showPackageDetail" @success="packageDonateSuccess" />
    <!-- 服务详情 -->
    <ServiceDetail ref="ServiceDetail" :visible.sync="serviceDetailVisible" :info="serviceInfo" :searchType="1" />
    <!-- 套餐详情 -->
    <PackageDetail
      ref="PackageDetail"
      :visible.sync="packageDetailVisible"
      :info="packInfo"
      :id="packInfo.packId"
      :type="packageDetailType"
      :showCost="false"
    />
  </div>
</template>

<script>
import { _getClientLevel } from '@/api/order.js'
import { mapGetters, mapState } from 'vuex'
export default {
  components: {
    PackageList: () => import('./PackageList/index.vue'),
    PackageProxy: () => import('./PackageProxy/index.vue'),
    PackageSetting: () => import('./PackageSetting/index.vue'),
    DonateRecord: () => import('./DonateRecord/index.vue'),
    TransferRecord: () => import('./TransferRecord/index.vue'),
    PackageTransfer: () => import('./components/PackageTransfer.vue'),
    PackageDonate: () => import('./components/PackageDonate.vue'),
    ServiceDetail: () => import('./components/ServiceDetail.vue'),
    PackageDetail: () => import('@/views/finance/PackageManage/components/PackageDetail.vue')
  },
  data() {
    return {
      activeName: 'first',
      level: null,
      serviceDetailVisible: false, // 服务详情弹窗
      packageTransferVisible: false, // 套餐转移弹窗
      packageDonateVisible: false, // 套餐赠送弹窗
      packageDetailVisible: false, // 套餐详情弹窗
      serviceInfo: {}, // 服务信息
      packInfo: {}, // 套餐信息
      packageDetailType: 1 // 0-新增 1-详情 2-编辑
    }
  },
  computed: {
    ...mapGetters(['userType', 'bisLevel', 'userInfo']),
    ...mapState({
      permissions: state => state.permissions
    })
  },
  created() {
    this.permissionArr = this.permissions.filter(item => {
      return item.type === 2
    })
    this.permissionArr = this.permissionArr.map(item => item.perms)
    this.$store.dispatch('user/getProviderInfo', {
      bisUserId: this.$cookies.get('userId')
    })
    // this.getClientLevel(this.$cookies.get('userId'))
  },
  mounted() {},
  methods: {
    // 获取用户层级
    async getClientLevel(bisUserId) {
      try {
        let res = await _getClientLevel({ bisUserId })
        if (res) {
          this.level = res.level
          this.$cookies.set('bisLevel', res.level, 'ly')
          this.$cookies.set('bisUserId', res.id, 'ly')
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 打开套餐详情
    showPackageDetail({ type, info, visible }) {
      if (info) {
        this.packInfo = info || {}
      }
      this.packageDetailType = type
      this.packageDetailVisible = visible
    },
    // 打开套餐转移
    showPackageTrasfer() {
      this.packageTransferVisible = true
    },
    // 打开套餐赠送
    showPackageDonate() {
      this.packageDonateVisible = true
    },
    // 打开服务详情
    showServiceDetail(info) {
      this.serviceInfo = info
      this.serviceDetailVisible = true
    },
    // 套餐赠送成功
    packageDonateSuccess() {
      this.activeName = 'fourth'
      this.$nextTick(() => {
        this.$refs.fourth.handleSearch()
      })
    },
    // 套餐转移成功
    packageTransferSuccess() {
      this.activeName = 'fifth'
      this.$nextTick(() => {
        this.$refs.fifth.handleSearch()
      })
    },
    handleClick() {},
    changeTab() {}
  }
}
</script>

<style lang="scss" scoped>
.wrapper-container {
  height: calc(100% - 32px);
  overflow-y: auto;
  background-color: #fff;
  border-radius: $containerRadius;
  margin: 16px 16px 0 16px;
  position: relative;
  box-sizing: border-box;
  .right-btn {
    position: absolute;
    top: 22px;
    right: 20px;
  }
}
// tab样式
::v-deep .cus-tab {
  height: 100%;
  .el-tabs__nav-wrap.is-top {
    padding: 5px 20px 0 20px;
    margin: 0 0 20px;
  }
  .el-tabs__item.is-top {
    height: 46px;
    line-height: 46px;
  }
  .el-tabs__content {
    height: calc(100% - 71px);
  }
  .el-tab-pane {
    height: 100%;
  }
}
</style>
