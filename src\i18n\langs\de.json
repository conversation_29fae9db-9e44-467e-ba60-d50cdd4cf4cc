{"language": "德语（此字段不使用，备注）", "lg": {"sun": "Sonntag", "mon": "Montag", "tue": "Dienstag", "wed": "Mittwoch", "thu": "Don<PERSON><PERSON>", "fri": "Freitag", "sat": "Samstag", "loginTimeout": "Zeitüberschreitung beim Anmelden, bitte erneut anmelden!", "_year": "y", "_month": "m", "_day": "d", "_hour": "h", "_minute": "m", "_second": "s", "_yes": "<PERSON>a", "_no": "NEIN", "error503": "<PERSON><PERSON><PERSON><PERSON> Betrieb, bitte später erneut versuchen!", "carlogremark1": "Neu generierte gewöhnliche Importpunkte zu", "carlogremark2": "Neu generierte lebenslange Einstiegspunkte auf", "carlogremark3": "Jahreskarte erstellen für", "carlogremark4": "<PERSON><PERSON><PERSON>n Si<PERSON> eine lebenslange Karte für", "carlogremark5": "Importieren Sie ein Jahr Ausrüstung nach", "carlogremark6": "Importieren Sie lebenslange Geräte nach", "carlogremark7": "<PERSON><PERSON>uern Sie die Jahreskarte auf", "carlogremark8": "Erneuern Sie die Lebenszeitkarte auf", "carlogremark9": "Übertragen Sie normale Einstiegspunkte auf", "carlogremark10": "Übertragen Sie den lebenslangen Induktionspunkt auf", "carlogremark11": "Jahreskarte an übertragen", "carlogremark12": "Übertragen Sie die lebenslange Karte an", "carlogremark13": "Neu generierter gemeinsamer Einstiegspunkt", "carlogremark14": "Neu generierter lebenslanger Einstiegspunkt", "carlogremark15": "Jahreskarte erstellen", "carlogremark16": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine lebenslange Karte", "carlogremark17": "Gemeinsamen Einstiegspunkt übertragen", "carlogremark18": "Übertragen Sie den lebenslangen Induktionspunkt", "carlogremark19": "Jahreskarte übertragen", "carlogremark20": "Lebenslange Karte übertragen", "inputContent": "<PERSON>te geben Si<PERSON> den Inhalt ein", "card": "<PERSON><PERSON>", "customer": "Kunde", "importPoint": "Karte importieren", "renew": "<PERSON><PERSON><PERSON><PERSON>", "cardgenerate": "<PERSON><PERSON> gene<PERSON>", "cardtrans": "Kartenübertragung", "cardback": "<PERSON><PERSON> zurückbekommen", "superior": "Untergeordneter Vorgesetzter", "commonImportPoint": "Karte importieren", "lifetimeImportPoint": "Importkarte für das Leben", "annualcard": "Jahreskarte", "lifelongcard": "Lebenslange Karte", "cardtype": "Speicherkarten-Typ", "incomeexpend": "Aufstellung der Einnahmen und Ausgaben", "inandouttype": "Einnahmen- / Ausgabenart", "balance": "Balance", "carbalance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chosetarget": "Bitte wählen Sie den Zielkunden", "generateIsZero": "Die Anzahl der generierten Karten ist 0!", "transIsZero": "Die Anzahl der übertragenen Karten beträgt 0!", "recycleIsZero": "Die Anzahl der recycelten Karten beträgt 0!", "transoneyeartip": "Die Anzahl der in einem Jahr übertragenen Importkarten darf nicht größer sein als der Restbetrag!", "translifelongtip": "Die Anzahl der lebenslangen Importkarten kann nicht größer sein als das G<PERSON>aben!", "transannualcardtip": "Die Anzahl der übertragenen Jahreskarten darf nicht größer sein als der Kontostand!", "translifetimecardtip": "Die Anzahl der übertragenen lebenslangen Karten darf nicht größer sein als das Guthaben!", "recycleoneyeartip": "Die Anzahl der recycelten Importkarten pro Jahr kann nicht höher sein als der Restbetrag!", "recyclelifelongtip": "Die Anzahl der wiederhergestellten lebenslangen Importkarten kann nicht höher sein als der Restbetrag!", "recycleannualcardtip": "Die Anzahl der recycelten Jahreskarten darf den Kontostand nicht überschreiten!", "recyclelifetimecardtip": "Die Anzahl der lebenslangen Karten darf das Guthaben nicht überschreiten!", "yearCard": "Jahreskarte", "lifetimeOfCard": "Lebenslange Karte", "generatesuccess": "Erfolgreich für $ generiert", "transsuccess": "Erfolgreich für $ übertragen", "recyclesuccess": "Erfolgreich für $ recycelt", "saleBatch": "Verkaufscharge", "operateAccount": "Das Betriebskonto", "targetAccount": "Zielkonto", "saleTime": "Verkaufszeit", "transNo": "Charge übertragen", "operatebyself": "Kann nicht an sich selbst arbeiten", "importRecord": "Datensatz importieren", "saleRecord": "Verkaufsrekord", "transRecord": "Datensatz übertragen", "importBatch": "Stapel importieren", "operateUser": "<PERSON>", "targetUser": "Zielkunde", "batchNo": "<PERSON><PERSON><PERSON><PERSON>", "importTotal": "Gesamtimportnummer", "checkdetails": "Überprüfen Sie die Details", "query": "Anfrage", "saleTotal": "Gesamtzahl der Verkäufe", "selectStartTime": "Bitte wählen Sie eine Startzeit", "selectEndTime": "Bitte wählen Sie eine Endzeit", "totalDevice": "Die Gesamtzahl der Geräte", "originaluser": "Ursprünglicher Kunde", "originalaccount": "Ursprüngliches Kundenkonto", "enterRole": "<PERSON>te geben Si<PERSON> den Rollennamen ein", "inputuser": "Bitte geben Si<PERSON> den Benutzernamen ein", "details": "Einzelheiten", "reqresult": "<PERSON><PERSON><PERSON><PERSON>", "logDetail": "Protokolldetails", "logNumber": "Protokollnummer", "uName": "<PERSON><PERSON><PERSON><PERSON>", "uType": "<PERSON><PERSON><PERSON><PERSON>", "reqmethod": "Anforderungsmethode", "reqparam": "Parameter an<PERSON>", "month": "<PERSON><PERSON>", "oneyear": "<PERSON><PERSON><PERSON><PERSON>", "lifetime": "Lebenslang", "income": "Einkommen", "pay": "Ausgaben", "imei": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (IMEI)", "machineType": "Ausstattungsmodell", "machineName": "G<PERSON><PERSON><PERSON><PERSON>", "imeiOrClientOrAccount": "Gerätenummer (IMEI) / Kundenname / Kontonummer", "imeiOrUserEmpty": "Die Gerätenummer (IMEI) / der Kundenname darf nicht leer sein!", "device": "G<PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "delAccount": "Konto löschen", "resetPswFailure": "Passwort konnte nicht zurückgesetzt werden", "notification": "Aufforderung", "isResetPsw_a": "Das {0}-Passwort sicher zurückgesetzt?", "resetPsw": "Passwort zurücksetzen", "virtualAccountTipsText": "Wenn Sie ein virtuelles Konto erstellen, handelt es sich um ein Alias-Konto des derzeit registrierten Händlerkontos, und Sie können Berechtigungen für das virtuelle Konto festlegen.", "password": "Passwort", "oldPsw": "Altes Passwort", "newPsw": "Neues Passwort", "confirmPsw": "Passwortbestätigung", "pswNoSame": "Inkonsistente Passworteingabe", "pswUpdateSuccess": "Das Passwort wurde erfolgreich geändert!", "oldPwdWarn": "Bitte geben Sie das alte Passwort ein", "newPwdWarn": "Bitte geben Si<PERSON> ein neues Passwort ein", "pwdConfirmWarn": "Bitte bestätigen Sie das neue Passwort", "pswCheckTip": "Der Vorschlag ist eine Kombination aus 6-20 Ziffern, Buchstaben und Symbolen", "pwdCheckTips1": "Vorschläge sind 6-20 Buchstaben, Zahlen oder Symbole", "pwdCheckTips2": "Das eingegebene Passwort ist zu schwach.", "pwdCheckTips3": "Ihr Passwort kann komplizierter sein.", "pwdCheckTips4": "Ihr Passwort ist sicher.", "pwdLevel1": "<PERSON><PERSON><PERSON>", "pwdLevel2": "Medium", "pwdLevel3": "<PERSON>", "clientName": "Kundenname", "loginAccount": "Login-Ko<PERSON>", "status": "Zustand", "showAll": "<PERSON><PERSON><PERSON> alle", "startUsing": "Aktivieren", "stopUsing": "Deaktivieren", "search": "<PERSON><PERSON>", "reset": "Z<PERSON>ücksetzen", "newAdd": "<PERSON>eu", "viewLimitConf": "Berechtigungseinstellungen anzeigen", "viewLimit": "Berechtigungen anzeigen", "newSysAcc": "Neues Systemkonto", "editSysAcc": "Berechtigungskonto bearbeiten", "virtualAcc": "<PERSON><PERSON><PERSON><PERSON>", "oriVirtualAcc": "Ursprüngliches virtuelles Konto", "virtualTip": "Das virtuelle Kontomodul wurde zu einem Systemkontomodul aktualisiert. Erstellen Sie ein neues Systemkonto", "view": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "remark": "<PERSON><PERSON><PERSON>", "createTime": "Erstellungszeit", "modifyTime": "Zeit ändern", "confirm": "<PERSON><PERSON><PERSON>", "serial": "NO.", "title": "Titel", "upload": "Hochladen", "all": "Alle", "unread": "<PERSON><PERSON><PERSON><PERSON>", "readed": "<PERSON><PERSON> gelesen", "type": "<PERSON><PERSON> von", "feedback": "<PERSON><PERSON><PERSON>", "bulletin": "Plattformansage", "about": "auf", "reply": "Antworten", "date": "Datum", "setRead": "Als gelesen markieren", "setAllRead": "<PERSON><PERSON> gelesen", "messageCenter": "Nachrichtenzentrum", "backpage": "Zurück zum vorherigen", "from": "<PERSON>", "gpsnow": "WhatsGPS", "pleaseChoose": "bitte auswählen", "success": "Erfolg", "fail": "<PERSON><PERSON>", "transfer": "Übertragen", "newGeneration": "Neue Generation", "consume": "<PERSON><PERSON><PERSON><PERSON>", "give": "<PERSON><PERSON><PERSON>", "limitconfig": "Berechtigungseinstellungen", "role": "<PERSON><PERSON>", "rolename": "Rollenname", "addRole": "Neue Rolle", "editRole": "<PERSON><PERSON> bearbeiten", "deleteRole": "Rolle löschen", "delRoleTip": "Möchten Sie diese Rolle wirklich löschen?", "delAccountTip": "Möchten Sie dieses Konto wirklich löschen?", "newAccountTip1": "Das Berechtigungskonto ähnelt dem alten virtuellen Konto und ist das Unterkonto des Administrators. Administratoren können Berechtigungskonten erstellen und Berechtigungskonten unterschiedliche Rollen zuweisen, sodass unterschiedliche Konten unterschiedliche Inhalte und Vorgänge auf der Plattform anzeigen können.", "newAccountTip2": "Prozess zum Erstellen eines Berechtigungskontos:", "newAccountTip31": "1. <PERSON>f der Rollenverwaltungsseite", "newAccountTip32": "Neue Rolle", "newAccountTip33": ", Und konfigurieren Sie Berechtigungen für die Rolle.", "newAccountTip4": "2. <PERSON><PERSON><PERSON><PERSON> <PERSON> auf der Seite zur Verwaltung von Berechtigungskonten ein neues Berechtigungskonto und weisen Sie dem Konto Rollen zu.", "newRoleTip1": "Administratoren können Rollen erstellen und unterschiedliche Betriebsberechtigungen für unterschiedliche Rollen konfigurieren, um die Geschäftsanforderungen in verschiedenen Szenarien zu erfüllen.", "newRoleTip2": "Konfigurieren Sie beispiels<PERSON>, ob eine Finanzrolle die Berechtigung zum Suchen und Überwachen hat, ob sie die Berechtigung zum Hinzufügen von Ku<PERSON> hat, ob sie die Berechtigung zum Ändern von Geräteinformationen hat usw.", "runtime": "Laufzeit", "locTime": "Positionierungszeit", "speedNum": "Geschwindigkeit (km / h)", "barCode": "Barcode", "belongCustom": "Kunde", "sweepCodeTime": "<PERSON>an <PERSON>", "startLoc": "Ausgangsposition", "endLoc": "Endposition", "totalMileage": "Gesamtkilometer", "totalOverSpeed": "Gesamtüberdre<PERSON>hl (Zeiten)", "totalStop": "Total Stop (Zeiten)", "totalOil": "Total oil", "mileageNum": "Kilometerstand (km)", "stopTimes": "Aufenthalt (Zeiten)", "offlineTime": "Offline-Zeit", "averageSpeed": "Durchschnittsgeschwindigkeit", "averageOil": "Durchschnittlicher Kraftstoffverbrauch", "fuelTimes": "Betankungszeiten", "fuelTotal": "Gesamtbetankung", "fuelDate": "Betankungszeit", "refuelingTime": "Auftankzeit", "choseDate": "Zeit wählen", "noData": "<PERSON><PERSON>", "machineCount": "Anzahl der Geräte", "openAccQuery": "ACC-Abfrage", "temperature1": "Temperature1", "temperature2": "Temperature2", "temperature3": "Temperature3", "run": "Fahren", "selected": "Ausgewählt", "sets": "Sets", "directionarray": {"0": "Due North", "1": "Nordosten", "2": "Due East", "3": "Südosten", "4": "Due South", "5": "Südwesten", "6": "Due West", "7": "Nordwesten"}, "pointedarray": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "1": "GPS", "2": "LAC", "3": "LAC Location", "4": "WIFI-Standort", "5": "Differenzielle Positionierung"}, "pointType": {"0": "Punkttyp", "1": "Satellitenort", "2": "Kompassort", "3": "LBS Location", "4": "WIFI-Standort", "5": "Differenzielle Positionierung"}, "alarmType": {"0": "Alerttyp", "1": "Vibrationsalert", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON> bei schwacher Batterie", "4": "SOS-<PERSON>ert", "5": "Überdrehzahlalert", "6": "Geofence Out Alert", "7": "<PERSON><PERSON><PERSON><PERSON>ung<PERSON><PERSON>", "8": "<PERSON><PERSON> bei schwacher Batterie", "9": "Außerhalb des Bereichs Alert", "10": "<PERSON><PERSON>", "11": "<PERSON><PERSON>", "12": "Magnetic Sensing Alert", "13": "<PERSON><PERSON>", "14": "Bluetooth Alert", "15": "<PERSON>ab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "16": "Falscher Basisstationsalert", "17": "Geofence In Alert", "18": "Geofence In Alert", "19": "Geofence Out Alert", "20": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "21": "Fatigue Driving Alert", "22": "Entry Mortgage Point", "23": "Exit Mortgage Point", "24": "Mortgage Point Parking", "25": "Terminal Offline", "26": "Geofence In Alert", "27": "Geofence Out Alert", "28": "Geofence In Alert", "29": "Geofence Out Alert", "30": "Kraftstoffalert", "31": "Alarm für Zündung an", "32": "Alarm für Zündung aus", "33": "Kollisionsalert", "34": "<PERSON><PERSON> zu spät zur Arbeit", "35": "Fr<PERSON>her Alert bei Verlassen der Arbeit", "36": "<PERSON><PERSON>", "37": "Neigungsalert", "40": "Hochtemperaturalert", "45": "Niedrigtemperatur-Alert", "50": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "55": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "60": "Parkalert", "70": "Alert für schnelle Beschleunigung", "71": "Schnellverzögerungsalert", "72": "<PERSON><PERSON><PERSON>", "73": "Außer Route Alert", "74": "Leitungsübergeschwindigkeitsalert", "75": "Überstundenalert beim <PERSON>en", "76": "<PERSON><PERSON><PERSON><PERSON>", "77": "Terminal-Pull-Alert", "78": "Terminal-Einsteckalert", "79": "Anhängeralert", "80": "Ans Telefon gehen", "81": "<PERSON><PERSON><PERSON><PERSON>", "82": "Ablenkung beim Fahren", "83": "<PERSON><PERSON><PERSON><PERSON>", "84": "Vorwärtskollisionswarnung", "85": "Spurabweichung", "86": "zu nah für die Autodistanz", "87": "Videosignalverlust", "88": "Videoframe-Okklusion", "89": "Fehler der Speichereinheit", "90": "Fatigue Driving Alert", "91": "Vorwärtskollisionswarnung", "92": "Zündüberwachungsalarm einschalten", "93": "Zündüberwachungsalarm ausschalten", "94": "<PERSON><PERSON>", "95": "<PERSON><PERSON>", "96": "<PERSON>cht angeschnallt sein <PERSON>", "97": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "98": "Bluetooth-Trennung", "99": "Dringende Hilfe", "101": "Warnung vor eingeschränkter Fahrweise", "102": "<PERSON><PERSON>", "107": "Alarm bei Annäherung an die Rückseite", "108": "Annäherungsalarm hinten links", "109": "Annäherungsalarm hinten rechts", "110": "P1 Bat<PERSON>iewarnung", "111": "Anti-Interferenz-Alarm", "114": "Der zaun fährt zu schnell zur polizei", "116": "Untertag Hochtemperaturalarm", "117": "Subtag-Warnung bei niedriger Temperatur", "118": "Überschlagsalarm", "119": "Eintrittsmeldung", "120": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "121": "Warnung bei unzureichendem Kraftstoffverbrauch", "122": "Warnung bei leichter Kollision", "123": "Warnung bei mittlerer Kollision", "124": "Warnung bei schwerer Kollision", "125": "Der deputy hat benzin gestohlen"}, "bsLogType": {"车辆管理": "Flottenmanagement", "打卡管理": "Anwesenheitsmanagement", "用户管理": "Kundenmanagement", "菜单管理": "Menüverwaltung", "角色管理": "Rollenverwaltung", "设备管理": "<PERSON><PERSON><PERSON><PERSON>", "录音管理": "Aufnahmemanagement", "围栏管理": "Zaunmanagement", "轨迹管理": "Verlaufswiedergabe", "告警管理": "Alertverwaltung", "用户卡券": "<PERSON><PERSON>", "远程控制": "Fernbedienung", "指令管理": "Befehlsverwaltung", "电子围栏": "Geofence", "消息提醒": "Anmerkungen", "虚拟用户": "<PERSON>irt<PERSON><PERSON>", "公告管理": "Ankündigungsmanagement", "热点管理": "FAQ", "角色模块": "<PERSON>enmo<PERSON><PERSON>", "登录": "Login-Log"}, "cardType": {"0": "Unbekannt Typ", "1": "Import Jahr Punkt", "2": "lebenslanger Import Punkt", "3": "in Karte", "4": "lebenslange Karte"}, "userType": {"0": "Administrator", "1": "<PERSON><PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON><PERSON>", "5": "Autonutzer"}, "cardState": {"0": "Normal", "1": "<PERSON><PERSON>", "2": "Fällige Erinnerung", "3": "Übermäßige Ausfallzeiten", "4": "Ungeöffnetes Paket", "5": "<PERSON>ten auf die Maschine", "6": "GPRS To Be Opened"}, "errorCode": {"error90010": "Das Gerät ist nicht online und der benutzerdefinierte Befehl konnte nicht gesendet werden!", "error70003": "Der Fernbedienungswert darf nicht leer sein", "error70006": "Unterstützt diesen Befehl nicht oder verfügt nicht über die entsprechende Berechtigung", "error20001": "Fahrzeug-ID darf nicht leer sein"}, "logDict": {"other": "andere", "insert": "einfügen", "update": "aktualisieren", "save": "sparen", "delete": "löschen", "grant": "gewähren", "export": "Export", "import": "importieren", "select": "w<PERSON><PERSON><PERSON>", "trans": "trans", "sale": "<PERSON><PERSON><PERSON><PERSON>", "renew": "<PERSON><PERSON><PERSON><PERSON>", "control": "Steuerung", "login": "<PERSON><PERSON><PERSON><PERSON>"}, "limits": {"appdownload": "Here", "ACC_statistics": "ACC-Statistiken", "Account_Home": "Überblick", "Add": "<PERSON>eu", "Add_POI": "POI hinzufügen", "Add_customer": "Neue Kunden hinzufügen", "Add_device_group": "Gerätegruppe hinzufügen", "Add_fence": "<PERSON><PERSON><PERSON><PERSON>", "Add_sharing_track": "Freigabespur hinzufügen", "Add_system_account": "Neues Berechtigungskonto", "Alarm_details": "<PERSON><PERSON><PERSON><PERSON>", "Alarm_message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Alarm_overview": "Alertübersicht", "Alarm_statistics": "Alertstatistik", "All_news": "Alle Neuigkeiten", "activity_board": "Kommende Veranstaltungen", "Associated_equipment": "Zugehörig<PERSON> G<PERSON>", "Available_points": "Verfügbare Punkte", "Barcode_statistics": "Barcode-Statistik", "Batch_Import": "Batch-Import", "Batch_renewal": "Chargenerneuerung", "Batch_reset": "Massen-Reset", "Bulk_sales": "Großverkäufe", "Call_the_police": "<PERSON><PERSON>", "Customer_details": "Kundendetails", "Customer_transfer": "Kundenübertragung", "Delete_POI": "POI löschen", "Delete_account": "Konto löschen", "Delete_customer": "Kunden löschen", "Delete_device": "Gerät löschen", "Delete_device_group": "Gerätegruppe löschen", "Delete_fence": "<PERSON>aun l<PERSON>", "Delete_role": "Rolle löschen", "Device_List": "Geräteli<PERSON>", "Device_grouping": "Gruppierung", "Device_transfer": "Geräteübertragung", "Due_reminder": "Ablauferinnerung", "Edit_details": "Details bearbeite", "Equipment_management": "Gerätemanagement", "My_clinet": "Gerätemanagement", "Fence": "<PERSON><PERSON><PERSON>", "Fence_management": "Zaunmanagement", "Generate": "generieren", "Generate_lead-in_points": "Generieren Sie Importpunkte", "Generate_renewal_points": "Erneuerungspunkt generieren", "Have_read": "<PERSON>s gelesen markiert", "Idle_speed_statistics": "Leerlaufgeschwindigkeitsstatistik", "Import": "Importieren", "Import_Device": "Gerät importieren", "Industry_Statistics": "Branchenstatistik", "Location_monitoring": "Bildschirm", "Log_management": "Log", "Mark_read": "<PERSON>s gelesen markiert", "Menu_management": "Speisekarte", "config_management": "Konfigurationsmanagement", "custom_model": "Definiertes modell.", "Message_Center": "Mitteilungszentrum", "Mileage_statistics": "Meilenstatistik", "Modify_POI": "POI ändern", "Modify_device_details": "Gerätedetails ändern", "Modify_device_group": "Gerätegruppe ändern", "Modify_role": "<PERSON><PERSON> ä<PERSON>n", "Modify_sharing_track": "Ändern Sie die Freigabespur", "Modify_user_expiration": "Ändern Sie das Ablaufdatum des Benutzers", "More": "<PERSON><PERSON>", "My_business": "Geschäft", "My_client": "<PERSON><PERSON>", "New_role": "Neue Rolle", "New_users": "Benutzer hinzufügen", "Oil_statistics": "Ölmengenstatistik", "POI_management": "POI-Management", "Points_record": "Gleichgewicht", "Push": "<PERSON><PERSON><PERSON>", "Quick_sale": "<PERSON><PERSON>elle Verkäufe", "Renew": "Verlängerungsgebühr", "Replay": "Wiedergabe", "Role_management": "<PERSON><PERSON>", "Run_overview": "Betriebsübersicht", "Running_statistics": "Betriebsstatistik", "Sales_equipment": "Verkaufsausrüstung", "Set_expiration_reminder": "Legen Sie die Ablauferinnerung fest", "Share_track": "Teilen", "regional_statistic": "Region", "Sharing_management": "Teilen", "User_management": "Benutzerverwaltung", "Speeding_detailed_list": "Geschwindigkeitsliste", "Statistical_report": "Bericht", "Stay_detailed_list": "Liste bleiben", "System_account_management": "Behördenkonto", "Temperature_statistics": "Temperaturstatistik", "Transfer": "Übertragen", "Transfer_group": "Gruppe übertragen", "Transfer_point": "Importpunkt übertragen", "Transfer_renewal_point": "Erneuerungspunkt übertragen", "Trip_statistics": "Reisestatistik", "Status_statistics": "Statusstatistik", "Static_statistics": "Statische Statistik", "Driver_alarm": "Fahrerverhaltensalert", "Driver_analysis": "Treiberanalyse", "Unlink": "Verknüpfung aufheben", "View": "<PERSON><PERSON><PERSON>", "View_POI": "POI anzeigen", "View_device_group": "Gerätegruppe anzeigen", "View_fence": "<PERSON><PERSON>n anzeigen", "View_role": "<PERSON>e anzeigen", "View_sharing_track": "Freigabespur anzeigen", "Virtual_account": "<PERSON><PERSON><PERSON><PERSON>", "Voltage_analysis": "Spannungsanalyse", "Voltage_statistics": "Spannungsstatistik", "batch_deletion": "Massenlöschung", "change_Password": "Passwort ändern", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "instruction": "Anweisung", "login": "<PERSON><PERSON><PERSON><PERSON>", "modify": "Ändern", "monitor": "Überwachung", "my_account": "<PERSON><PERSON>", "reset_Password": "Passwort zurücksetzen", "share_it": "Teilen", "sub_user": "Untergeordneter Benutzer", "sub_customers": "<PERSON><PERSON><PERSON><PERSON>", "track": "Tracking", "Custom_Order": "Benutzerdefinierte Anweisung", "GeoKey": "<PERSON><PERSON><PERSON><PERSON>", "GeoKey_Update": "ändern", "GeoKey_Delete": "löschen", "GeoKey_Add": "Hinzufügen", "GeoKey_View": "Au<PERSON>cht", "feedback_manager": "<PERSON><PERSON><PERSON>", "feedback_list": "Au<PERSON>cht", "feedback_handle": "Feed<PERSON> verar<PERSON>ten", "proclamat_manager": "Ankündigung", "proclamat_manager_list": "Ankündigung anzeigen", "proclamat_manager_update": "Änderungsansage", "proclamat_manager_delete": "Ansage löschen", "proclamat_manager_save": "Neue Ankündigung", "device_update_batch_model": "Gerätemodell stapelweise ändern", "login_manager": "Login-Verwaltung", "asset_manager": "<PERSON><PERSON><PERSON><PERSON>", "package_manager": "Paketmanager", "order_manager": "Auftragsmanager", "financial_management": "Finanzmanagement", "finance_center": "Finanzzentrum", "device_record": "Aufzeichnung", "auth_manager": "Beh<PERSON><PERSON>", "system_manager": "System", "point_generate": "<PERSON><PERSON> gene<PERSON>", "point_recovery": "<PERSON><PERSON> zurückbekommen", "point_transfer": "Kartenübertragung", "device_transfer_record": "Datensatz übertragen", "device_sale_record": "Verkaufsrekord", "device_import_record": "Datensatz importieren", "user_point_list": "<PERSON><PERSON>", "Info_Instruction": "Anleitung", "Info_Question": "Häufige Fragen", "Info_AllCenter": "Nachrichtencenter", "user_point_log_list": "Überprüfen Sie die Details", "car_maintain_list": "Instandhaltung", "REPLACE": "IMEI ersetzen", "punch_record_list": "Anwesenheitsverwaltung", "Station_message": "Station News.", "Update_bulletin": "Ankündigung aktualisieren.", "Personal_Center": "Persönliches Zentrum", "task_list": "Datenexport.", "Write_Account": "Das Konto ausfüllen", "Find_Method": "Methode abrufen.", "Contact_Provider": "Anbieter kontaktieren", "device_report_offline": "Offline-Statistiken", "Select_Equipment": "Ausrüstung auswählen", "Offline_statistics": "Offline-Statistiken", "Driving_Behavior": "Fahrverhalten", "drive_analysis": "Fahrverhalten", "Fence_statistics": "Zaunstatistik", "line_manage": "Linienverwaltung", "Line_statistics": "Linienstatistik", "machine_type_manage": "Geräteverwaltung", "carfince_first": "Haus-Auto-Finanzierung", "carfince_risk_control": "Risikoleitstelle", "carfince_monitor": "Überwachungsplattform", "carfince_two_bet_manager": "Zweite Depotverwaltung", "carfince_tow_bet_point_setting": "Zweite Wettpunkteinstellung", "carfince_tow_bet_alarm": "Zweiter Anruf der Polizei", "carfince_permanent_manager": "Bewohnerverwaltung", "carfince_permanent_verify": "Resident Management Audit", "carfince_permanent_statistics": "Einwohnerstatistik", "carfince_permanent_alarm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carfince_abnormal_aggregation": "Anormale Aggregation", "carfince_parking_timeout": "Zeitüberschreitung beim Parken", "carfince_fence_alarm": "Zaunalert", "carfince_my_client": "Mein Geschäft Autofinanzierung", "carfince_customer_management": "Kundenverwaltung", "topic_list": "Problemmanagement", "topic_save": "Frage hinzufügen", "topic_update": "Ändere die Frage", "topic_delete": "Frage löschen", "Find_Password": "Passwort wiederherstellen", "Video_Monitor": "Video", "Time_Video": "Live-Video", "Playback_Video": "Videowiedergabe", "Video_Evidence": "Videobeweise", "Video_Task": "Videoaufgaben", "EventTask": "Ereignisaufgaben", "bigFullScreen": "Dashboard", "Capture_Center": "Erfassungszentrum", "Event_Center": "Veranstaltungszentrum", "package_list": "Paketliste", "package_proxy": "Paketagent", "package_setting": "Paketeinstellungen", "charge_stat": "Ladestatistik", "person_punch": "Personal-Punch-in", "driver_punch": "Fahrer-Punch-In", "report_task": "Aufgabe melden", "package_edit": "Paketänderung", "device_import": "Geräteimport", "device_reset": "<PERSON><PERSON><PERSON>", "income_detail": "Aufstellung der Einnahmen und Ausgaben", "withrawal_detail": "Auszahlungsdetails", "bill_detail": "Details des Sachkontos", "order_manage": "Auftragsmanager", "service_order": "Auftrag des Dienstleisters", "gift_record": "Geschenkaufzeichnung", "import_record": "Datensatz importieren", "provider_manage": "Dienstanbieterverwaltung", "amazon_order": "Amazon-Tracking-Nummer", "withrawal_verify": "Auszahlungsüberprüfung", "device_info": "Geräteinformationen", "app_update": "APP management", "car_manager_upgrade": "Es gibt eine eskalation.", "car_CarUpgrade_upgrade": "Updates zu jeder zeit.", "Channel_Manage": "Kanal management.", "operation_manager": "Das betriebsmanagement.", "ad_manager": "Agentur werbung"}, "cancel": "Stornieren", "cancelSpace": "Stornieren", "submit": "e<PERSON>re<PERSON>n", "submitSpace": "Einreichen", "resetSpace": "Z<PERSON>ücksetzen", "uploadType": "Bitte laden Sie Dateien vom Typ .jpg .png .jpeg .gif hoch", "uploadSize": "Die Upload-<PERSON><PERSON> darf nicht größer als 3 MB sein", "uploadAvatar": "Avatar hochladen kann nicht größer als 1 MB sein", "submitfail": "Übermittlung fehlgeschlagen！", "fbUploadTip": "Bitte wählen Sie den Feedback-Typ", "alerttitle": "Der Titel darf nicht leer sein!", "alertcontent": "Feedback darf nicht leer sein!", "submitsuccess": "Erfolgreich eingereicht! Wir werden Ihr Feedback so schnell wie möglich bearbeiten ~", "contact": "Kontakt Informationen", "contact_p": "Füllen Sie Ihr Telefon oder Ihre E-Mail aus", "uploadImg": "Bild hochladen", "fbcontent_p": "Beschreiben Sie kurz die Fragen und Kommentare, die Sie Feedback geben möchten, und wir werden uns für Sie weiter verbessern.", "fbcontent": "Fragen und Meinungen", "fbType": "Fe<PERSON><PERSON>-<PERSON>", "fbType1": "Beratung", "fbType2": "Fehlfunktion", "fbType3": "Benutzererfahrung", "fbType4": "Vorschläge für neue Funktionen", "fbType5": "<PERSON><PERSON>", "fbtitle": "Frage- und Meinungstitel", "serviceProvide": "Dienstleister", "linkMan": "Kontakt", "linkPhone": "Telefon", "email": "Email", "address": "<PERSON><PERSON><PERSON>", "commonProblem": "häufiges Problem", "instructions": "Anleitung", "feeds": "<PERSON><PERSON><PERSON>", "fotbidPassword": "Passwort ändern", "exitStytem": "Ausgang", "account": "Ko<PERSON>", "operaTime": "Betriebszeit", "ipaddr": "IP Adresse", "businessType": "Unternehmensart", "operateType": "Operationstyp", "operator": "Arbeiten", "time": "Zeit", "primaryKind": {"1": "<PERSON><PERSON>", "2": "Benutzerdefinierte Erinnerungen", "3": "Wartungsanzeige", "4": "Verlängerungsmitteilung", "5": "Ablauferinnerung", "6": "<PERSON><PERSON><PERSON>", "7": "Announcement"}, "Monday": "Am montag!", "Tuesday": "Dienstag.", "Wednesday": "Am mittwoch?", "Thursday": "<PERSON> donnerstag.", "Friday": "<PERSON><PERSON> das dope.", "alarmType[119]": "Wollen sie sie befreien?"}, "remind": "<PERSON><PERSON><PERSON>", "importRemind": "Das Gerät wird initialisiert. <PERSON>te warten Si<PERSON> zehn Minuten, bevor <PERSON> diese Gerätecharge in Betrieb nehmen, um Geräteanomalien zu vermeiden.", "importRemindFile": "Liste der defekten Geräte zur Spende", "idleSpeedInputError": "Falscher alert. Falscher alert", "batchImportTemplate": "Der fahrer verwaltet die sammelvorlage", "batchImportTip": "Einträge für einen fahrer können über das hochladen Von formularen eingereicht werden. Die tabellenkalkulation muss in das für die vorlage vorlage zuständige dateiformat eingegeben werden Sorg dafür, dass die fahrkarte korrekt nummeriert ist und den befehl auf das gerät gesetzt wurde;", "batchAlarmSettings": "Stapelalerteinstellungen", "pay": "<PERSON>ahlen", "batchInstructions": "Stapelbefehl senden", "generalFunctions": "Gemeinsame Funktionen", "offlineJudgment": "Offline-Urteil", "offlineJudgmentTipOne": "Wenn das Gerät unerlaubter Weise vom Bordnetz getrennt wird können Sie hier einstellen ab wann das Gerät eine Benachrichtigung senden soll. Wenn Sie nichts einstellen wird eine Benachrichtigung nach 10 min gesendet.", "offlineJudgmentTipTwo": "Wenn keine Einstellung vorhanden ist, wird das kabelgebundene Gerät standardmäßig als offline eingestuft, wenn innerhalb von 10 Minuten kein Heartbeat-Positionierungspaket hochgeladen wird. Das drahtlose Gerät wird als offline eingestuft, wenn innerhalb von 30 Minuten kein Heartbeat-Positionierungspaket hochgeladen wird .", "expandFunctionality": "Funktionen erweitern", "expandFunctionalityTip": "Bitte bestätigen Sie vor der Einrichtung, dass das Gerät diese Art von erweiterter Funktion unterstützt.", "resultErrorTip": "{0} Geräte wurden erfolgreich eingerichtet, aber {1} Geräte konnten aus den folgenden Gründen nicht eingerichtet werden.", "scheduledTime": "Planungszeit", "plannedMileage": "Wartungsintervall planen", "maintenanceStatus": "Wartungssta<PERSON>", "comprehensiveMaintenance": "Umfassende Wartung", "mileageMaintenance": "Kilometerwartung", "dateMaintenance": "Datumspflege", "warned": "Bereits gewarnt", "endUser": "<PERSON><PERSON><PERSON><PERSON>", "maintenanceType": "Wartungstyp", "alertDate": "Warnungsdatum", "scheduledDate": "<PERSON><PERSON><PERSON><PERSON>", "offlineInputError": "Offline-Beurteilung des Alertwert-Eingabefehlers", "oilOutage": "<PERSON><PERSON><PERSON>", "serviceNumberOrder": "Auftrag des Dienstleisters", "purchaseQuantity": "Количество закупок", "iWYURze5gw6IuatRIrbE9": "<PERSON><PERSON> beim <PERSON>", "u8042nAxHpYgsu7a58sQ6": "Übertragen", "O3NI5gV9UN7-mT9gi-KHC": "generieren", "elr13plxYiqSiRBQuS4As": "Rückkehr", "_c1C4zcPDX-ZWed2eRbdA": "Verkaufen", "TlgmpSebIew_qw82Z7273": "IMEI", "pp7ggW3s8XBX7RhFkk1_v": "User Due", "12YEhb_OD-ZHwKO7vWITk": "Kunde", "7fJLSw_PZ8o4rUM051emO": "Model", "xe_DoA8ge_gmef88_vh4p": "Plattform fällig", "fEKIzjDvXOhTv3JkjXuKc": "Ger<PERSON> hinzufügen", "NEfgPcoBVZIP3f585kbxV": "Erneuerungstyp", "DrMjlHHpaxp_yFcEvOcQi": "Operationstipps: Nach dem Zurücksetzen werden die Testdaten wie Geräteaktivierungszeit und -verlauf gelöscht und der Gerätestatus online oder offline auf inaktiv zurückgesetzt.", "DsMzz4HiiZh_zjcZCg7sv": "<PERSON><PERSON>", "grwMQULKZ0LrPztl-Z7wr": "Total", "6VQ3tYGZgExw1rZnn8Cbj": "Stock", "PfpLQ5G3zI6xZpZlialvg": "Welt", "zj98NBvqfCTJqrwUaGhji": "Gerätebereitstellung", "Z8MQX4KbfoflrmZEy5Vj6": "Untergebene einschließen", "JH4HcH8lq4j75he7Raack": "Geräteanalyse", "fCdSYzcuCLxP0iO32z_7j": "Unterkonto", "pKGbb1Y6o7498BtbB9dWf": "Geräteanalyse", "d4TPm1qg_yKyoFhGhc49y": "Gesamtaktivierung", "zMqE-woZfLlDun2A-ZmhU": "wöchentliche Neuaktivierung", "ZiuUTHENQjPPZw4IEUVOK": "Ankündigung der Versionsaktualisierung", "QoEq85osl9LN1AmO58BiM": "Online-Gerätetrenddiagramm", "YI-CDEbvHMaK4Qo-P0C54": "<PERSON><PERSON>", "mngS5pANeQga3_X0KfIkm": "<PERSON><PERSON>", "3QA6lHuTpmOtPdjicAlLd": "<PERSON><PERSON><PERSON>", "OfQYeUUlRnfql7Er4dYXT": "Sales top10", "0cT7QoohrYcbqyDW-BGwX": "7 Tage", "TImMByZR7gX-T5SE4wW_C": "7 Tage", "8XStVR83GO-UD-vuXceyN": "30 Tage", "0gU5oOZxYWY5c8diw5GGt": "Gestern", "w6SqBL1DnKWu5v2QWD_Qd": "Fügen Sie Chargen hinzu", "8XaFdQXQQQExEY6VjBIoV": "Derzeit {0}", "QHewpO-a1QEPaAwvF6Xzr": "Aktualisieren", "1NfCtPMIHa5qxyRCS84S1": "<PERSON>te geben Sie die IMEI-Nummer ein, aktuell: {0}", "iAVxi7DIE0592dotpSI-W": "Bitte geben Sie die IMEI-Nummer ein", "Qd6gmq65WiHFshYKYFr4F": "<PERSON>te geben Sie eine 15-stellige IMEI-Nummer ein!", "h89Z-UD7uIkZ6egxwKDSM": "3 Monate", "8yaI2Muj-dOcsIgudaSgP": "12 Monate", "Sg3DiQowXqC8D35OICtD8": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> {0} <PERSON><PERSON><PERSON><PERSON> an {1} verkaufen möchten?", "DBBhTdEjXuithUnfBj4uF": "Bitte geben Sie das Gerät ein", "MRqCFQeb7tjZ4vzCQR9Rn": "<PERSON><PERSON><PERSON>", "dGiezzpH2ifwm00fwd6Ou": "Verlängerung fehlgeschlagen", "RnDDKP0_zNeMqMvt83zyQ": "Die aktuelle {0} ist kleiner als {1}, nicht genug, um zu bezahlen! Bitte löschen Sie ein Gerät oder fügen Sie eine Karte hinzu!", "jVw447QvAeRR4NrTNp5wI": "{0} Geräteerneuerung erfolgreich", "HahpWiGqvHlj74U8VvXkS": "{0} Geräteverkauf erfolgreich, {1} Geräteverkauf fehlgeschlagen. Die spezifischen Gründe sind folgende:", "SwtmYZpc1RnK8sKvik1ND": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> dieses {0} -G<PERSON><PERSON> wirklich zurücksetzen?", "2gK1OvSK1N490nqe6BmML": "Erfolg zurücksetzen", "mAyV3e2y7beLqKbttMEyP": "Möchten Sie wirklich {1} Geräte des Modells {2} für {0} importieren?", "ZrFQNTbU10MZPjYNkDZ0c": "{0} Daten wurden erfolgreich importiert, aber {1} konnte die Daten nicht importieren. Die spezifischen Gründe sind folgende:", "ZvBsO_CTD_qH1mggQdOEz": "<PERSON><PERSON>", "Fjjy4njX1Z-5GjVk2Ry8x": "Gerätemodellstatistik", "V1DtS-jLSC38n1jMuvxaX": "Ablaufzeit nach Verlängerung", "jhzwcMuCQSBt0s7QsRL6J": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> {0} <PERSON>er<PERSON>e erneuern möchten?", "93mxIMhUPjOdxz1mAg5VT": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass Si<PERSON> {0} Geräte für {1} erneuern möchten?", "8_eOeIb1VOROEQ4EsuJGG": "Erfolgreich erneuerte {0} Geräte {1}, die Gültigkeitsdauer ist wie folgt", "t__tQ1COBdJfeegyNEut4": "<PERSON>ur sich selbst", "V5ni5q4iTECCMrUfdW93x": "<PERSON>ine IMEI-<PERSON><PERSON><PERSON> vorhanden", "5WGqM6dwWgfZNerSr08_j": "{0} G<PERSON><PERSON><PERSON> wurden erfolgreich importiert", "5P76CIxqHeL97BTdshJjp": "Eingabe der Seriennummer", "hrJVpqMEc8TKXFgGT79a_": "{0} G<PERSON><PERSON><PERSON> wurden erfolgreich verkauft", "xtan0lEAvk31wH5vywZIy": "Superior", "myzCJIAtsYrsBH7YLrbMC": "Unterkonto hinzufügen", "8EX2meYNZa_kdhW80e8L_": "Distributor", "zjj2zR3u_qvaZPmcIQmZc": "Verkaufserfolg", "EGou8TaGJwBFQKJoHe1Gy": "IMEI Existing", "PdDwaW3ZMpo7a1kMh1QTQ": "Model", "iMVebDXUsA5c_fxl671kL": "<PERSON><PERSON>", "RFQZq8sjjZ5vjV5m6CAZH": "<PERSON><PERSON><PERSON>", "M-1KNMPFUhwUz1A54afuJ": "Das Anmeldekonto ist bereits vorhanden", "vYM6cI35WYG617ajkDXbg": "Bitte wählen Si<PERSON> einen überlegenen Benutzer aus", "8L8DMNF_TiVeobqbKjzNp": "Kundenname", "CVRtg6QBo3_I3SdSEnOFN": "<PERSON><PERSON><PERSON>", "HIYRKGjbiHPM1vChmwTtS": "<PERSON><PERSON><PERSON><PERSON><PERSON> darf nicht 15-stellig sein!", "_Ir0v6itPxvLoVX8jyypM": "Passwort", "gjf_Ayv-A3bIgDrLUyaLl": "Passwort bestätigen", "wqK1AUIRFGiB5eHofupGL": "Passwort nicht Atypismus!", "fXrxuyIkLD3QEgjPvKuJa": "Bitte wählen Si<PERSON> einen Kunden aus", "MTr2Ks8d0Ny8erQoUJ2kJ": "Bitte Modell auswählen", "pleaseSelectPackVersion": "Bitte wählen sie die variante im paket aus", "60Cg-7z2oV7N30quX6B1c": "Bitte wählen Sie die Plattformablaufzeit aus", "lfSrcQrITYhRu4d4uMcDs": "Ergebnisse importieren", "uIEq4TWD9LFaCJEFLt84g": "Zahlenformatfehler", "qyDjiNshB1P2cTPWxQcIA": "<PERSON>te geben Si<PERSON> eine feste Nummer ein", "n25N1-n4SySYruwj301zO": "<PERSON>te geben Si<PERSON> den Start<PERSON> ein", "8K5x8ehQZuA1mdBtxsMeY": "<PERSON>te geben Si<PERSON> den Endwert ein", "t1vi6s7lcTAtqd_7kwEOT": "Eine IMEI für eine Zeile", "aFq5tmk1n281NDkCSp8X6": "Mitgliedschaft", "E9BGOCAvkhyQewmSWvUHr": "Erfolgreich um 1 Jahr verlängern", "w0vVvEoUupXMqiJV9fCF2": "Die lebenslange Karte muss nicht erneuert werden", "Z-IXWYRO5QljfIOj9i_sa": "Ein Jahr", "O_ft1yy-v3_td6h9X6_CF": "Verkaufsergebnisse", "ZFrlcab8jFZcpRNRcCnMA": "Die Benutzerablaufzeit ist länger als die Plattformablaufzeit imei number", "HmTLpP09V2cIjAWRcUHzQ": "IMEI wied<PERSON>n", "eY-kEiHnxN-wrVrq1fVX3": "4. Stock, Nr. 121, KeCheng-Gebäude, Wissenschaftsstraße, Bezirk Luogang, Guangzhou", "dr1VBKYi7Au5f9nwUBwd_": "Hoch", "yn6gOaagimB00Dh3sw0Xw": "<PERSON><PERSON><PERSON>", "09dzRv47cdB8ngpXdmrNA": "Area", "ZxAVznWm0Arp2GO-C5elp": "Prozentsatz", "7fVMapQ6M_RkSzO5jDN18": "<PERSON>ng", "KQBFZQ0Voyfhg6fQXT_Rn": "Name", "1JpPsfqrgrwbDs32GzHmo": "Sales (Set)", "qmmSQeOd7o-k-CKy8cGQ1": "Erneuerungsergebnis", "cilqXZDxJIkYAni2p6D18": "<PERSON><PERSON><PERSON>", "ySCL835cERmc-2ui46kJg": "Guten Tag", "CP6KtfpD2nRp4YsehmLV4": "<PERSON><PERSON><PERSON>", "XhZ_mUXUaEoscKh4O__52": "Verkaufsmenge", "B-oqbyA9EYX3_b604CHoI": "Gerätemodelldetails", "i6izKntdBPjj1CVgTxoFv": "Kundenmenge", "h8y6BrYJM46qcer2hAm-e": "Direktkunden", "LMGhuoa55f6P3zBo-I6_h": "Die Anzahl der Geräte, die in der letzten Woche online waren", "yHUUUDenNX-ZDlHH-h8Bf": "Kann Monate nicht überspannen", "c9Ois07PLUg8-LT8ypZpx": "Import fehlgeschlagen", "qNXcCC7yboHklZOmNBtso": "Die Anzahl der Geräte, die in den letzten 30 Tagen online waren", "oC3aVgjq_fLItbKC4dzC0": "<PERSON>ine ausführbare IMEI-Nummer", "6LW1YZbmhuEq6LyLyYwqR": "Die Anzahl der Importpunkte ist unzureichend", "_L3SCIlbztecHYB9zhGmZ": "Die IMEI-Nummer ist illegal", "mXaZcmYFfm7x55jOUq3WJ": "IMEI-Nummer existiert bereits", "qmNtvRFOkHtbTvTeWLK0j": "IMEI, die das Fahrzeug nicht importieren konnte", "c-aC4vVpgvcu0VyfYe2dU": "Geräteinfo", "GmeIr45NbbW42QwT7mmrf": "Maschinenverkauf", "Wylya-4JRSp679tGf_q4g": "<PERSON><PERSON> verwalten", "nd_7J9mswRt2IfBGNjIWa": "Geofence Setting", "bo540fd46Pfi59A-x8C_R": "Alertverwaltung", "51VD4AjMhSFl6EBZnYW3g": "Versandanweisung", "bWqxJSaBirj0pJXbd4ubc": "<PERSON>ur Moniter anzeigen", "uTfF4X61wgUVv37Uhu0aw": "Update Success", "hiO2JmZ3oYl5uM8mflDcL": "Modifizierbare Funktion", "lD-unyqzZNFq1t9SB0eNy": "Berechtigungsbereich", "1yPhethvo0Yf9j-z3fQaZ": "Ko<PERSON>", "igFoXwa2BzYkyYAJDqU9R": "Möchten Sie das Passwort auf {0} zurücksetzen?", "XsvtYpl8vyei8PJ4zNqBV": "{0} Passwort wurde auf 123456 zurückgesetzt", "6czW74k8BArZqrVE40tKG": "Restkennwort fehlgeschlagen", "KK1ozzOjBr5DpFPKLt1C6": "Möchten Sie {0} löschen?", "HzV5269cO5xVNwIaK3De_": "Virtuelles Konto löschen", "yXpq6xUfZ9hRjfzRQLSzh": "<PERSON><PERSON><PERSON><PERSON>", "EshFaJOQM_Oh3ICyukZP-": "IMEi nicht vorhanden", "mN6Jwf0wh5RTbxZiGQCoR": "Gerätestatistik", "sdeE2Xfr5yaOVDKBpwToH": "Wachstum TOP10", "7-o5VuFdLBdBRPbue62gr": "item", "flK64mYHgrj-k7-D2XupZ": "online", "dtFn22xfEx789uFKvaG_n": "offline", "dzaHeFci7OhejMnw1GSix": "inaktiviert", "lkPusWocD2jptbcRGKhFR": "Virtuelles Konto hinzufügen", "Z9YgHkSg2uB840tCIrZyC": "Menschen", "7C1maetdK8mVxRyzBW2H6": "Nicht direkte Benutzer", "2Qfae-GXcMhGQxMT0Ds6A": "Verkauft", "qs5MKLWLhFEFf1W5CGOCn": "Wachstum (gesetzt)", "kt7tkMjYVOmcxeQ8WIe42": "Nicht unterstützter Kartentyp", "vlKiesuPzDnB0GMEntyQf": "Total", "dh_0Cvxs6eGNbLMY-7v8Y": "<PERSON><PERSON>", "nHg2M0jmyailtWvemNIU2": "Untergeordnet", "hYo3Cd35MbLWBTXRKT_HP": "Noch keine Ankündigung", "_bu8UzncFEL9nkg5qPBDs": "<PERSON>s gibt unzulässige Zeichen", "3RP2ACHSyN8l-084R5ia9": "Ankündigung", "lOnLduR3l_8WRTpPLViDr": "<PERSON><PERSON>", "D_CFBCrWWcdam_cGkm90t": "Allgemeiner Benutzer", "HPZ9-4BkksYDf9bOYA7Bq": "<PERSON><PERSON>", "dGJYSHKNhX1So1amUzfdO": "Erweiterte Suche", "0HfLeevl06T5-c-6Hybn9": "Stapelbewegung", "RIiHXilwzDZZXOC0RFjS4": "Informationen ändern", "pWAH6OlawcOyPzpCQiD3t": "<PERSON><PERSON>", "fi97wBDxt-kI86UwsAgTt": "IMEI-Nummer", "2WVzsA1lduRG2ofkXI1gv": "SIM-Karte", "p533TIv1HaDSMgWw1t4a_": "Kennzeichen", "gEN4YPGMoNz8HYKg2kgMi": "Importzeit", "vys-I8F3EiJEhY84SJ2hE": "Aktivierte Zeit", "ZofyuSsvgF1XR16Ey32FU": "Das ist die sim-karte.", "zhx-pYPkX0douf0ybSGBM": "Fahrzeuginformationen", "AmK-1t3nyDtFLUWuuGc7m": "num<PERSON><PERSON><PERSON><PERSON>", "5NW3PUUAmaqAZxIV48c2V": "Kraftstoffverbrauch", "8CyJDVju_fn2eLYBfCFa5": "Überdrehzahlalert", "2J4Y_8V79GZz8oVP1uRLy": "ACC-<PERSON>ert", "dlW7RH6fkSItw_Mtz2Ykw": "Geschwindigkeit", "Jet1u7xx2NupyskORg_e-": "Abgelaufen", "Ht-5xfyfOJaPLfPI1Ina2": "{0} Tage abgelaufen", "M_dYy2C4ldXbU0GcSh8XQ": "<PERSON>cht verwendet", "IikV2EDWehRNB_DuPyoAi": "<PERSON><PERSON>", "ihcJF8i_680UPUSADlQBQ": "Tracking", "FNZ9KgOe3_nZPHLr-gemV": "Wiedergabe", "nG52x-1HCtQNlZdzdGke_": "<PERSON><PERSON><PERSON><PERSON>", "nuMj_JYBMK5JmtZA19Q7D": "<PERSON><PERSON><PERSON>", "_LruCTL_GmFwkLYdKRGPL": "Share Location", "d2rfxOdgIK8sx-aXn_UjT": "Detail", "JvE2ZJEopyBOH0nltAgpC": "In Gruppe verschieben", "tJFFxuGRgmWrx8jXwGawx": "Gruppierung", "E2cJxJ0qcSBiNzRMKbaj0": "Map", "IKjGxwah7gczqSoJQWeJJ": "Satellite", "eq8ubUOnx-iIN5d8flNTZ": "Verkehr", "p5s_fNJw6Bfo4E59dkz8t": "Lineal", "YmEYZtN_qZQbAvfoi-EnT": "Bitte Adresse oder Breiten-Längengrad eingeben.", "sxg1Ago7Wgz102blS1Uw0": "<PERSON><PERSON><PERSON>", "sgH0wIb6CRMhrPErfzZNo": "<PERSON><PERSON>", "YRsd0eFEx-XpEoHM4bQOf": "Polygon", "vMpgu5wFuK5er9Dlkz9fF": "<PERSON><PERSON><PERSON><PERSON>", "FMD-CUGF0TJ-UFTwcQV2j": "Bundle", "3bKE4RZjKEEnP-6Lvd3QO": "Group", "g0Z4WYTOSoNVw6icvU8Xa": "Icon", "a3JOJwEQkkXHUNJ9B8gzq": "<PERSON><PERSON> ho<PERSON>n", "oNh27yZ5e5LDkyOhuFrCO": "Beispiel-Download", "YRfNHk3LACKCdKZKpdGFt": "Sie können POI importieren, indem Sie eine Excel-Datei mit zugehörigen Informationen hochladen. Bitte folgen Sie dem Format des Beispiels, um die Datei vorzubereiten.", "993p62YTFUY0OgmGiplEj": "Name: <PERSON><PERSON><PERSON><PERSON><PERSON>, nicht mehr als 32 Zeichen", "SXm03GuPs_W8_bHDUIiOO": "Symbol: <PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> 1,2,3,4 ein", "WdUDofXEM4vPVut2ggyll": "Latitude ： Required", "ihJPRdZfKZWbwqQg3TC6p": "Längengrad: E<PERSON><PERSON>erlich", "GPiUGvJyTduYFCJS0VF_W": "Gruppenname: Optional, nicht mehr als 32 Zeichen. Wenn der Gruppenname nicht ausgefüllt ist, gehört der POI-Punkt zur Standardgruppe. Wenn der gefüllte Gruppenname mit dem erstellten Gruppennamen übereinstimmt, dem POI-Punkt gehört zur erstellten Gruppe. Der Name der Gruppe wurde nicht erstellt, das System fügt die Gruppe hinzu ", "K98R9Kl6t3eFtWbQBdzh-": "Anmerkungen: Optional, nicht mehr als 50 Zeichen", "ntUmxqogGfJUuLkjvK6ZV": "Alerttyp", "CA1jOSCwNEQbOx07cHlZm": "Alertzeit", "6ed5szctmCrXayO-9xgWW": "<PERSON><PERSON> l<PERSON>schbaren Alertinformationen", "A7xrOFhybj9Js9R7Oulub": "Baidu Map", "TrFNV69OZRAH0okN4sumS": "Baidu Satellite", "2yf5VqhJTX_NvDljHWexs": "Google Map", "rc-VB0jPoY98SMUVoUe7C": "Bing Map", "r9Z0vOw35FkxLAXFabw2R": "Google Satellite", "goF8T8dHU6gao6PljrtNi": "Static", "OCvfOWyVHgRIOLEA021Kl": "Verschieben", "CzvhWCze3pQCK5M_1KoaB": "Abgelaufen", "K_s5NFaeLyxrujarrzfMk": "<PERSON><PERSON><PERSON><PERSON>", "XoaUHxf1yCHUm8G-ttmOA": "verd<PERSON><PERSON><PERSON>", "oeP8MwRcMAnrw4rbs7DY3": "Ein", "dMl02a_mb9XIn8CyX3A_D": "Aus", "7vdkNJd4kRZP2sQMmyxpv": "<PERSON><PERSON>n anzeigen", "MENG0IZDkTNnl3WoRl_p5": "Report", "IJW7kt6UjfglE5oKPiJ0z": "Streetview", "gbtfYMFIkzFPpyAatuQPP": "Zaunverwaltung", "4UElrpFn7scbLfe2oYK0B": "Unterkonten", "9JFj_wyOKWTwMbNqgGrRC": "Gruppe hinzufügen", "XjJkfhK6Qd8NBMfxckAle": "News", "H7ZxbhzZKtJsZjdkN7YJL": "Der Ausrüstungsservice ist abgelaufen, bitte wenden Si<PERSON> sich zur Erneuerung an den Händler!", "EDcFx0tNb2Z2qVSxFsaJh": "<PERSON>s gibt einen neuen Alert", "kbqyQYySG3XGM2bVA-4-t": "Zum Anzeigen klicken", "LtNPazzpjZ8Ki6SHzTfs6": "Suchname", "SUBLSbsZXN6Yu-QsHnH5_": "Zeichentutorial", "AmtYv4ISEbwIRe2OC3KP3": "<PERSON><PERSON>", "hu_Qd-LVVX--ytlHSCPNI": "Klicken Sie auf die Karte, um den Mittelpunkt des Kreises zu bestimmen", "AmLlcGde2A2OHyFdaqiJa": "<PERSON><PERSON><PERSON> Si<PERSON> die Maus, um die kreisförmige Fläche zu dehnen", "qAs2oKd_k5MsKqmoor8Py": "<PERSON><PERSON><PERSON> Si<PERSON> erneut, um das Zeichnen zu beenden", "X7_1ZjxC6jNbqqwtSdZ77": "Polygonal<PERSON>", "B8Vp2heRHQQFC19TLbWtu": "<PERSON>lick<PERSON> Sie auf die Karte, um Interpunktion zu setzen", "7ptVgNAs_gId9fabwwyXY": "Doppelklicken Sie mit der Maus, um das Zeichnen zu beenden", "8uvQGTTGGK4YHP6RctT_K": "<PERSON><PERSON><PERSON><PERSON>", "RLJHlTX2QB4puw9-yaKkg": "Lock", "AHFQWLMEP4QcejtHVMHAP": "Ziel / IMEI", "NtWULYpXIIyqfvlHmyJuU": "Das Gerät hat noch keine Standortinformationen.", "rVAoxxyYFdp_tC9Dnyb6M": "Erfolg bewegen!", "Y9r-CsONpxxRmg00jzjLm": "Standardgruppe", "OJAZ-TwNVqi3Bh7hztuLC": "Erinnerung", "cvyH4iJuWCiLfB_Wsn4Nn": "Erfolg löschen", "kUTV5VcObCrB6kdnyfHDS": "<PERSON><PERSON>", "NlOXMw3tnc5FS0jChJJeY": "<PERSON><PERSON> fä<PERSON>g", "bZb0QeKns1R-YrJrCIl_h": "Fällige Tage", "a2soi5XT71DCr2H3iW75d": "Bereits zugeordnet", "NprpRhvbYE9KLFSrs1Ut3": "Stunde", "T3YbKXAK7yNAbyXGGy3E-": "KM", "zCKyujELRDbjx5vAbAD3r": "Das Konto verfügt über Berechtigungen ohne Betrieb", "NHp9pI1Qr5rbpe8r-4Nd4": "<PERSON>te geben Si<PERSON> den Zaunnamen ein", "zhjiZcfXH8sIv6yxSkkwq": "Form", "q_gUoHM-7bSFFC0IuHOmI": "<PERSON><PERSON>", "Csx-q_UsjB2o0oRRZf1qz": "<PERSON>er", "W7-XDEPApmz5QAAGPLqQQ": "Alarm für Geozaun betreten", "UyLRnzSqYp4MB2K4_aOaE": "Alarm für Geozaun verlassen", "Oy3u-IJ6TzbP9vNvGYzw7": "Flottenzaun", "Y1eS06Ae8KWuwsunOTGsV": "Bitte wählen Sie mindestens eine Alertmethode aus", "6dWutEmqMZnfcebUwMWcH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "JFQKJUrmNVUCRlOW-U6Qu": "Löschen", "kAKDzRgzgRbGj7TWa8W8z": "Gruppe hinzufügen", "I8lfTbBiYbCyvuPzjkf_Z": "<PERSON><PERSON> hinzufügen", "CmWMoOrWpZU6sTGtkB4QL": "<PERSON>te geben Si<PERSON> einen Namen ein", "vjdwbqgsFl9Te_vH9kNY5": "<PERSON><PERSON> ass<PERSON>", "z5sQqFDnFSiQzix1HGtIB": "Geo-Zaun abbrechen", "wNtm7LWAqF0ewVQRdPb-J": "Bitte wählen Si<PERSON> ein Gerät aus！", "zeWy2Egw7v6c3sDhGAX9c": "Löschen oder nicht: {0}", "mnCksVnc2zGhj-VRBgGPw": "Charge", "vBpDexpv7D_KNy_aaxmZx": "<PERSON><PERSON><PERSON>", "IjtUG09nKBjgDwMEUXJEd": "Weitere Informationen", "kGPteGn1UwCDzRpDTYOOA": "<PERSON><PERSON><PERSON>, um den Speicherort zu bestätigen, doppelklicken Sie zum Beenden", "LoX_Y5XcmGFXPrKocd3Vy": "Signal: Das letzte Mal, als das Gerät mit der Plattform kommunizierte", "uHD_UGEfA88QbFYjH40Sk": "Standort: Die letzte Satellitenpositionierungszeit des Geräts", "CfXyIfQ7YrDKYGs6IcEjE": "Wenn das Online-Gerät stationär ist, wird es nicht positioniert, kommuniziert aber dennoch mit der Plattform", "I8b_zoiaDScRSfe8AcYJQ": "{0} <PERSON><PERSON><PERSON> löschen oder nicht?", "53WepRb0dgsGBYKlPWo_b": "Der Zaun ist {0} Geräten zugeordnet, löschen oder nicht?", "r07mdOUMExdAaIj36JZgK": "Bitte Zaun auswählen", "fPpUDmYmZBfijNxuYo-dT": "<PERSON>öschen oder nicht?", "LsRzknqAtdhScwNBoSsHK": "Zauninformationen", "Y9bbn5LDAbEo9O9LPdGcH": "Zum Zeichnen eines polygonalen Zauns sind mindestens drei Punk<PERSON> erforderlich", "CUalv8dHu0RQdAaF3Wj-v": "Fehlgeschlagen！ Bitte löschen Sie zuerst den POI (Point of Interest).", "n26EViLKtRWW73EXNwoIe": "Sehenswürdigkeit", "GOXwqzj3woUoTJ5EcWIfb": "Gruppe bearbeiten", "OraWJ32a3r19pXlnEz7LI": "Edit Point", "hnmI2xuLjvPN3UZNk3qVe": "Fehler beim Hochladen des Dateiformats", "zSwW84PPwEdSY7KXU0_NP": "<PERSON><PERSON> ho<PERSON>n", "MWerfQOxskhfsWhfVL7aK": "Charge<PERSON><PERSON>rd<PERSON>ng", "jGphq0Bb5xDW59eNTBOkR": "Fehler löschen", "O8-0tOC041IMU4vRnzpNL": "Link konnte nicht generiert werden", "wue_UE3tLAxmsNy30nm8x": "Link<PERSON><PERSON><PERSON><PERSON><PERSON> teilen", "tKwyVQxLCB3j3YDFNoq3y": "{1} <PERSON><PERSON><PERSON><PERSON> wurden dem {0} Z<PERSON>n ， zugeordnet.", "LseoYpEYEbqOsg-3HO9uM": "Adressinformationen können nicht abgerufen werden", "Zbkw-Z-GkLdk92q2GqQb9": "Startzeit", "tjh3v6c9w4iWKRhEsxCel": "Endzeit", "-L6s88wO8SJ2dBT3LRNqp": "From", "CnHQ3jqzoQUIJi5VNzDUS": "To", "n98s6qScQ72Pfc3ipN7sK": "Stopschild", "kErEgq9bY9npX-B_gR5kg": "Location Point", "ppeXkkkZ-FQrqgexHkFd2": "Drift Location entfernen", "qis-Gzwt4NrDH4wyIX8FM": "Trace beenden", "2vbMWIgM9jMU39l_y0cMD": "Driving Mileage", "Wyr6tYmzD3CWhgVab7Bzs": "Zeitauswahl", "WPDTnaE8GGfKGXTg7ln4o": "Zeitintervall", "90u1wbF4gTwa5j8I-u7dj": "Longitude", "MlqbfDIYF1BFYrrm6l_F3": "Latitude", "kYg8FK3sHxS7WWLlmHxRs": "<PERSON><PERSON><PERSON>", "lPdLzI8-RihRdC19Sj793": "Positionierungsmodus", "SKow9wO-zFrJ-bTY4iiNk": "Position", "Ii_xMJ-aK06I6WjE_7RvK": "Signal", "XbMQZa2UhMAnIgRdfYmBE": "<PERSON><PERSON>", "NZn8Y-nZV3WRZLds93mmP": "<PERSON><PERSON>", "XuTgmO9YYX9ZgzdosA8t3": "Standard", "3DYXD-6GdyKqIdA4QkIPq": "Adressauflösung ...", "USsDC30U686JAi9DNifpb": "Bitte wählen Si<PERSON> ein Gerät aus！", "KKjrKI9Bincd6VUnZIqbE": "Start Time Msg", "N9aI7K4jam3xJAzQDHr7k": "End Time Msg", "Kwn53CWjiBzlFcnJJJGx0": "Die Endzeit darf nicht früher als die Startzeit sein.", "eFcy-BfOhf-h5PVaJ3_zt": "<PERSON><PERSON> von <PERSON> ...", "eZz4PP9ckrYa3h1cjvihA": "<PERSON><PERSON>", "sOTuSCBfF-JISyxB_Hstu": "Verlaufswiedergabe", "D-3CnkxFwEGtcITqH7fbB": "Kilometerstand", "HGjTtiFHYcRbFl8yOYVox": "LBS nicht in Statistik enthalten", "d8OJkUti5rpb0ASvasdB-": "<PERSON><PERSON><PERSON> anzeigen", "c2VXLffkDAgW_W7kge10R": "Statische Zeit", "XbOlArDdJLP3YlKnbKEQ0": "Statische Zeit", "cr0p2Sw1rzg2KIpg4kmxH": "Quick Check", "FgXEzLIfSgW9hXdZU2BkT": "Der Upload-Inhalt ist falsch formatiert.", "n14RoDOGBsNntIwetoWMq": "<PERSON><PERSON><PERSON>, um das Video anzuzeigen", "rMpzwjT1wGJzwTtW99xdT": "<PERSON><PERSON><PERSON>", "8Hy4f3sEGqYcZA0E2Tgwm": "Export", "Oy0sGBs_ICAiweVP0kuh8": "Zeitintervall weniger als 31 Tage!", "4CputoWSHyKi0tf0k_mKf": "Sie können nur die Daten der letzten sechs Monate abfragen, bitte erneut auswählen！", "T9djoLP0IBjXNQVB1szpe": "Reduktion", "fzpQzKWHHxUzOvguAlLA0": "Als Bild speichern", "8zbVKtrFrdYmnIr6smhz5": "Ein", "REZwRP_j2rmLc3pFRLqvS": "Aus", "H4NX8-4EhG5zHLk1ytXkp": "Kraftstoff", "hs24f25MR-Kkr7mTiNgrI": "Temperatur", "n_gGRUBDnHrrFFZNMBWQF": "Spannung", "faw91fOu9AVHonQmNZS1R": "Name", "8jMVMcD_Ztb85usaRh0ky": "<PERSON><PERSON>", "coUL03ioWIVJ-B5LWsN6J": "3 Tage", "7z_8O5fJQFxZ3qGxVSaD3": "Verknüpfung erstellen", "_qETsEONk7wNBovgka8my": "<PERSON><PERSON>", "XsXoMT3CDfSMMxm_H8UGU": "<PERSON><PERSON>fle<PERSON>", "K-2ZfNUf8dV65yB98WRvE": "<PERSON>er<PERSON><PERSON><PERSON>", "bkEROx2ZY0mMX1e5F9fbm": "Adressinformationen können vorübergehend nicht abgerufen werden", "7mowPMr4CB0A0uIRO4d8V": "{0} Geräte wurden erfolgreich modifiziert und {1} -Organe wurden aus folgenden Gründen nicht erfolgreich modifiziert.", "cUE4imnis7N3pb9QDkywH": "Fe<PERSON> beim Löschen", "Yo_d2neO6liIX0B6ZtnX5": "Ist {0} auf {1} gesetzt?", "MCvwqkGr2-dQqS1DK3XwR": "Intervallzeitbereich {0}-{1}, Einheit ({2}) einstellen", "7GDN-cGPKDVJF3Q-qFCvd": "Stapelbearbeitung", "2JODYAuyHeVxEZXdVhZAt": "Aktivitätszeit", "KS-VF_Tm3pzzJDisCGNKE": "Neu hinzugefügte Zeit", "Ipkl7-AfN7m1qHER0Tm7f": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_QtO05JtDYmcbYWbiYs6y": "Das Gerät kann nach dem Löschen nicht wiederhergestellt werden. Wurde es gelöscht?", "Bv_g9A7SENymIKsLJ9ujV": "Administrator", "itTlS_5B7wWxVWLZ2IZjG": "<PERSON><PERSON><PERSON><PERSON>", "2biNeUu6eL5bY-ZYKcvi8": "Autobenutzer", "BLo9bZGWQ0hpd1oDuL3B2": "Verlängerungslink", "CguDuYAHEf6SmLcLCCDLD": "Untergeordneten Alert an<PERSON>", "1teP4Jl_EJz1v5UdgQZAF": "Benachrichtigung", "J9_G307ZKQDjVoHtO7Qss": "<PERSON><PERSON><PERSON>", "AYVWzUbEzW8nQXqBDTyC9": "Konto/Name", "kGWxb26eIdnX_GrUdYd50": "Bewe<PERSON><PERSON> er<PERSON><PERSON>greich", "1F7LiURKsTqo36ZrZkafp": "Bitte wenden Si<PERSON> sich zur Verlängerung an Ihren Diensteanbieter!", "YQKLFb8qkw8_3Cu2O2IZ_": "Kunden transferieren", "9grU7SGwJ1bWmFgjQvuDS": "{0} ist ein Endbenutzer und unterstützt keine Übertragung von Kunden. Bei Fragen wenden Sie sich bitte an Ihren Dienstanbieter", "IG5Mrae_PTMgmeWAW6kX-": "Befehl ausgegeben, wartet auf Geräteantwort", "commandSuccess": "Befehl ausgeführt", "9L9roCS8UNQRrmlWr1zHU": "Herzlichen Glückwunsch! Das Gerät hat den Befehl erfolgreich ausgeführt!", "lRyXdbGCaG68kEvHRl2wf": "Ergebnis：", "AMWltiKoAEZG_1FlswCOk": "Der Offlinebefehl wurde gespeichert. Er wird automatisch gesendet sobald das Gerät online ist.", "hCxLHY28pr4VAUX59yaX3": "Übertragen nach", "BYPkXF0WNPoQnCGOTnh8A": "Arbeitsmodus", "6kV2sMrY3lJRwThT_NSLQ": "Upload-Intervall einstellen", "jmaCB00OAHprnw3-I3hUm": "Demontagealert einstellen", "fAHRmeV6DRHlr6Pd_0dKv": "Zentrumsnummer", "UJhfS6Uu9OWK6sW6ASkHz": "SOS-Nummernverwaltung", "-DQYU_0OifhlsFR-Adxv2": "<PERSON><PERSON><PERSON>", "yUWTv_VHIxw_swMm0qKyv": "Kontrollnummernverwaltung", "DPftk33RF1Cr-9EzPUpV-": "<PERSON><PERSON><PERSON><PERSON>", "YJu4l4OgiSPD6yMd0UctL": "Alertnummernverwaltung", "iSHNRyf7vh0Vtuop0muUV": "Vibrationsalert", "gXSOO777Zyjx828Cmep3Y": "Fahrzeugleistung", "KuJyn-XHuYQq5O7sOtHoa": "Bremse", "pnfK4Vyg6n2wiCxC2iZEz": "<PERSON><PERSON>", "ucbv19YmxM9BgaLpFQAdi": "Auto-Suchmodus", "6zwffX5jH1JpFu917DGAz": "Öl- und Stromkontrolle", "OuaI6fAFAWCRsLdLAG_Jf": "Werk wiederherstellen", "BVLbawNBmWRSi4svvOQ_m": "Smart Tracking", "KRilYPhoZO1sT8aKsGzEB": "Parkalert", "IVhyLA_3ICW8aDDQr-v3v": "Zeitzoneneinstellung", "EwQMEv-4HfvN0bXg4Seja": "Remote Boot", "t2xOtwpmtTfU6sc4426SW": "Befestigung", "FbNlJKmw7P2Um_Vyd3sfR": "Unscharfschalten", "m5sOa6yTHvNgFp7m82xK6": "Telefonhören", "xn499WPrR7JCXsWgImCmt": "Einstellung der Vibrationsalert-Empfindlichkeit", "KQxWomzE-Y7a5sl5e4ZK8": "Vibrationsalert-SMS-Einstellungen", "JFwhNoaUQY8ROls29jwhx": "Vibrationsalert-Rufeinstellungen", "7BUMCa9c_IeOBWspNS9TQ": "Schlafbefehl", "ND5cf9nzQ6aQT96OhgZIC": "Verschiebungsradius", "Lt1mMvTHa_yuzv81mJ9Yz": "Temperaturalert", "lBUwL931fZBbruo7oJk1w": "Plattform läuft bald ab", "A9FNfVrnNX_IL3Sqwj9Su": "<PERSON><PERSON><PERSON> läuft bald ab", "2fOpju8dVPzTsLD9Icbaq": "Benutzer abgelaufen", "COB-45zsnvgfvOuofxPf5": "Ablaufzeit der Plattform", "7_McUY6-mnqlIwzq_o7c6": "Benutzerablaufzeit", "x6j0xlfBVYCgf3-pXoh7Z": "Abgelaufene Abfrage", "XbFegIO6XdtwVCLj3tHjn": "Startdatum", "k3rb7GIYeArL-6QUB2jYR": "Enddatum", "JOHRDjZ4DXNl0KtpQTn5Y": "Zeitabfrage", "r4dYsKXhyAkW9-WKvNGd0": "Es wurden insgesamt {0} übereinstimmende Datensätze gefunden", "V7gpSV-XtOtpWE978XWMe": "SOS-<PERSON>ert", "KSTTK7q9bKSNc36SGzqL2": "Alerttyp", "GHkNMYw76nVM5W4mRICca": "Alerteinstellungen", "YWO2tIL3JS9UWLacM-ECj": "Kraftstoffeinstellung", "xwcKgTp2DvPPU3BRpr4kw": "<PERSON><PERSON><PERSON>", "error10003": "Passwortfehler", "error90010": "Das Gerät ist offline, benutzerdefinierter Befehl fehlgeschlagen!", "error70003": "Der Fernbedienungswert darf nicht leer sein", "error70006": "Unterstützt den Befehl nicht oder hat kein Recht, ihn zu senden", "error20001": "Fahrzeug-ID darf nicht leer sein", "error20012": "Fahrzeug ist nicht aktiviert", "error10012": "Fehler beim alten Passwort", "error10017": "Löschen fehlgeschlagen, bitte Unterbenutzer löschen!", "error10023": "Löschen fehlgeschlagen, der Benutzer hat Gerät", "error20008": "Hinzufügen fehlgeschlagen, IMEI existiert bereits", "error20006": "Bitte geben Sie eine IMEI der Länge 15 ein", "error10019": "Falsches Format des Kontakttelefons", "error10024": "Verkäufe nicht wiederholen", "error120003": "Links freigeben deaktiviert", "error10025": "Die geänderten Geräteinformationen dürfen nicht leer sein", "error2010": "<PERSON><PERSON> ho<PERSON>n", "error20002": "Es existiert keine IMEI-Nummer", "error10081": "Unzureichende Anzahl Verlängerungskarten", "error10082": "<PERSON>in Aufladen für lebenslanges Gerät erforderlich", "error3000": "Die Rolle wurde dem Systemkonto zugewiesen und kann nicht gelöscht werden", "error103": "Das Konto wurde ges<PERSON>rt, bitte kontaktieren Sie Ihren Provider", "error124": "Kann nicht an sich selbst arbeiten", "unArrowServiceTip": "Plattform fällig ist weniger als Benutzer fällig, Bitte erneut auswählen, IMEI wie folgt: ", "editDeviceTips": "Bitte bestätigen Sie, dass das zu ändernde Gerät das gleiche Modell ist und nicht aktiv ist!", "uWFemTPKIbGOyQ17ugfkE": "Letzte Position", "tempSetting": "Temperatureinstellung", "tempSensor": "Temperatursensor", "tempAlert": "Nachdem der Temperatursensor ausgeschaltet wurde, empfängt er keine Temperaturdaten!", "m7PB-o0n69UQe3-RLFtm6": "Fernabschaltung", "d2v88dFMBwc6IBasbVZUB": "Artikelname", "laXMcUYMWZ5Z1GJYdq5_g": "Gültiger Zeitraum", "8E_5DReq4p9vShgeUzwDv": "<PERSON><PERSON> zum Betrieb", "-9EQ9kbkuRXcOGCWQ6cMq": "Wartungsdatum", "zdVunWKu5AhodKvqgP4rI": "Aktualisierungszeit", "0SbtD71xRYUoRVQf2nVLg": "Effektive Frist", "5WAnTiOO5Wp3w-x0SVjSN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_soRxqN9L0mb6hSyXV6Vx": "<PERSON><PERSON><PERSON> nehmen", "npm5kq2r28GlD7S4lIe14": "Erstellungsdatum", "lWW57n7Ua158IiiFvL0jU": "Startzeit der Verlaufswiedergabe", "jZ9YZbQgfeKLiDHeOQF4Y": "Endzeit der Verlaufswiedergabe", "wHLiHQweFr6Dg4PvyCpT4": "Link-Gültigkeitsdauer", "BnOTpdevlmr1HZw_IH-KH": "Link teilen ist nicht aktiviert", "g0lDgo0eVJuPrPB5Z5Vgd": "Nächste Wartungskilometer（KM）", "Rj0tzi6vp9nWZtswv3ipq": "Nächster Wartungstermin", "nwJzmS9Osv1MtnLuWwzIZ": "Aktueller Kilometerstand (KM)", "r9QdHdk_7rac9hv4aw9cl": "<PERSON><PERSON><PERSON>", "9fBvgXknsLqW_Sr8WqrIj": "Hinzufügen", "3QqqxkqFx7OzOMr7KoS0F": "Aktuelle Gesamtkilometer", "7fSVAcN6FNA6tRDv-_R-D": "Letzte Wartungskilometer", "kpJpvXpu9gAqYd8cN-Tcr": "Nächste Wartungskilometer", "dsC8Y9WsQautq4sdheMJf": "Letztes Wartungsdatum", "nqL_foYkFFB3XXif3unHh": "Benachrichtigung ein", "TogUVo2D5tJSTDaOr-n1p": "Der nächste Wartungskilometer darf nicht leer sein", "fidPvoq7zsvnxAuSg5NGM": "Die nächste Wartungszeit darf nicht leer sein", "bxeltfqSrYbvgH34URuDu": "Die nächste Wartungszeit muss größer als die aktuelle Zeit sein", "bsQ5K2AQ9sCuBtOMxmqf_": "Verlängerungskarte", "YodpqD_6tNSUO81daApII": "<PERSON>te zu<PERSON>t das zu ändernde Gerät auswählen!", "pB2SeJU_kVoVlVfQFD-sq": "Ist dieser Auftrag erteilt?", "5xdEeVm0qnz9SxT7MG8qC": "Plattform abgelaufen", "Py6WRg65Hlhr4ZZeN4JTK": "7 Tage", "9hrkM5Xiik9vRyY2TjZC7": "30 Tage", "DwPR-SBjnWtgyoWDDpzr3": "60 Tage", "wcGDlaCZO2gApNapnD1GE": "{0} <PERSON><PERSON>", "zc7x8FJOhr0K527ImZQy8": "Wenn ein Konflikt zwischen der Einstellung des Plattformtemperaturalerts und der Befehlstemperaturalerteinstellung besteht, hat die Plattformeinstellung Vorrang", "mB7Q6tNij2-TEQuj_EQip": "Niedrigtemperaturalert", "ngQ_u9YCVROkTgspYaeDF": "Alertwert: Temperatur", "W5vK5LEvgAc_JHhPcxySK": "Hochtemperaturalert", "YJK4tJOghE3DJ5hCCblYl": "Beispielbeschreibung: Wenn die Temperatur >=20°C ist, wird der Alert ausgelöst, wenn die Temperatur 20°C bis 127°C erreicht;", "fpq5Sc6vhKfXv3uT3-_v0": "Kilometerwartung", "f7IWU1fmCaxD0JjYJZoAR": "Erinnerungsmechanismus: {0} bevor der Kilometerstand den eingestellten Wert erreicht / 3 Tage vor dem Wartungstermin eine Erinnerungsbenachrichtigung;", "PafeQj-F3Eo7YM2eQupv1": "Unterstützt mehrere E-Mails, getrennt durch Semikolons", "Ce2R23rARaUED1OI3Xch5": "<PERSON><PERSON>, E-Mail-Benachrichtigung + Stationsbenachrichtigung, E-Mail-Adress<PERSON> kann in Alert Push - E-Mail geändert werden", "m7syXSguP29TzdRR-jtqL": "Bitte wählen Sie eine CSV-Datei aus", "Nt1bQaoSwAA1lWy-Yki9-": "<PERSON>ä<PERSON>en Sie die Datei", "wjjTfBWo4sFD6dDqMUA8E": "Importanweisungen: 1. Excel-Datei im csv-Format speichern 2. csv-Datei in das System importieren.", "JoV7M-08iOvdSofJL2dXv": "Erfolg ändern", "En636CNcxTxoDjeZPjArx": "Gerätemodell (nach Änderung)", "C2cZ15pSLdgYB6PdIEcHT": "Befehlsverlauf", "oQjvZaVtcYehcHekft4JC": "Terminal nicht unterstützt", "dHJWVOHGgZNJMVJctlIgr": "Das Terminal antwortet erfolgreich", "3ouZgJBDf6-vt--s9KqFJ": "Terminal-Antwort fehlgeschlagen", "cVVOCbNxQLFcv1HwysKUM": "<PERSON>cht gesendet", "qtX4hKNhrIq4HQWKq76qF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VC9bpkEpdi5BGS4bxu-Ve": "wurde ausgestellt", "KV0PCwFh9KzMeYVPkXd9i": "Ausführung erfolgreich", "7055N1pThT9t2nVidaqYf": "Ausführung fehlgeschlagen", "qMnlim32WjTPSVJezsiZS": "<PERSON><PERSON>", "DzuqoOisOoNLlaCC-Ji_y": "Verkaufsdatum", "VZEMI1HZaAQEAOccCxLgK": "Online-Zeit", "qBNdY9Hof9Y2O9F204y0I": "Laufleistungswartungsprotokoll", "2_GOBlwzPbyr5HQDEsUOK": "Spur", "CgoQEhz77jlNp0vrlVxSn": "Benutzer-Fälligkeit darf nicht größer als Plattform-Fälligkeit sein", "4HbPhU8SVH27GO7vwb5Az": "Alertperiode", "F4gONEExeouoNaqxfRgUl": "Ganzer Tag", "YxtnLS-9penDcV00ukAce": "Tag", "1PMQ9WuBMTKpUj5pxY9s5": "<PERSON><PERSON>", "M3BBgNFBGii6WiaU5jQsY": "Alertbenachrichtigung", "gVQwdq1hZqq52wB7i1u1n": "Untergeordnete Alerte empfangen", "M-aGJuBti_0J8Qpbvmqzf": "Stationsinformationen", "mpeSz021INSNFgOa8BCpf": "<PERSON><PERSON>", "1ECHLtFguDsDq_3uOG0yp": "Wartungsdatensatz: Nachdem Sie die Fahrzeugwartungserinnerung in Meine Kunden-Equipment-Details-Alerteinstellungen eingestellt haben, überprüfen Sie den Wartungseinstellungsdatensatz hier oder fügen Sie Gerätewartungseinstellungen über diese Seite hinzu. ", "RE62y37yvH6qImoM4ss42": "Freigabedatensatz: Nachdem Sie den Gerätetrack auf der Fahrzeugüberwachungsseite geteilt haben, können Sie den Freigabedatensatz hier einsehen oder dort einen Freigabelink erstellen.", "_WlC2Ljs_RF5cLhyYJnSi": "<PERSON>euer Freigabelink", "1sW-OzlYc_q1b9n_NcFpi": "Weitere Anweisungen", "vxvA4VAvFjv9MTiMd5EZB": "Befehl prüfen", "enQR7BvU-FbOHOhV869wf": "Softwareversion prüfen", "ZjesfJleICyF8c_POTRz8": "Status prüfen", "GTC0khuQYbxhjkOcU-f6G": "Breiten- und Längengrad prüfen", "BbRsM3ZxO3aXYhzCH6iSU": "Parameterkonfiguration prüfen", "lLcqsvnFC-b1pvBbyIudG": "GPRS-Parameter prüfen", "9B6fVyRW86BNLPYKEXqaz": "<PERSON><PERSON><PERSON>", "6vK3EWj5Tu8CeExT-hBsJ": "SMS-Erinnerungsalert prüfen", "qbSvjPXqpmObBi1yVrlzi": "Bindungsnummer prüfen", "vCi97K5uNUBnZV697Gpjo": "Befehlsname", "j34MkEOAqjyT80854yoTQ": "Anweisungsinhalt", "s6lAnu683jaqbQ7tCaJ2S": "Zeit senden", "8Ch-s1H4v0OW2yx82azFE": "Inhalt der Antwort", "5be1cXISzVRWtU12xrHrV": "Reaktionszeit", "hVknJbW_i4WBVBCdHbYZz": "<PERSON><PERSON><PERSON> senden", "F3JFF-1PIyXW-jnh4bsMd": "Zentrale Telefon", "ZvLlyWA7PnfWeaWbJ9UQS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2izr0ZUzt8xrZhKYzDZh5": "Plattformalert", "c70mJqcGxaOeuqV5V9Z7c": "SMS+Plattformalert", "wVmqyjZhzO8ddRA4vDwhi": "Plattformalert+SMS+Telefon", "DyrTMwE_4K5Uk2TKYFLIh": "Alertwert", "M2L1RdEwKiK8xe739Zd6i": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "K3pwVRJqNEWbwF0S786O8": "Zeitintervallbereich 0-255, 0 bedeutet schließen", "jQC4bDCPKXPp4Lff_4hG6": "Plattformalert+Tel", "eKj89wVWw918dADwmoY6a": "Mobilnummer", "qLAbckbjkA2Jh7bjocw1V": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "u4rNU4FweIl9vmKO8n7Bo": "Möchten Sie die Bindungsnummer wirklich überprüfen ？", "3Z1ZggbT0y84ej1_bi1hx": "<PERSON>öchten Sie die GPRS-Parameter wirklich überprüfen?", "nRNHg0_EYHP_Wc4PiR2ib": "Sind <PERSON> sicher, dass Si<PERSON> die Breiten- und Längenangaben überprüfen möchten?", "K9LhupOLQu06xBR1c8CY7": "Möchten Sie die Parameter wirklich überprüfen？", "z7H1qYX3aJE35pbQU1Mx3": "Sind <PERSON> sic<PERSON>, dass Sie den SMS-Erinnerungsalert überprüfen möchten?", "HTDQgJ7b2SoT9Rw7CdhQU": "Möchten Sie den Status wirklich überprüfen?", "Di-hmtqF6siUZJy2QH6DA": "Möchten Sie die Versionsnummer wirklich überprüfen?", "ktHad5imbbn2sPz06Utbf": "Möchten Sie dieses Gerät wirklich neu starten?", "zU8zLd7T1sJBQIv0bzTu5": "Zündung Aus", "dRqd3njVatWzZR2v-Dejt": "EIN", "ZRPZ-M8Mk6NjGlbu6F7uk": "Parkzeit", "7-bUdwYB1cZvFilxmF6WL": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "AQ97fUpbsZxJQnd6NikCY": "Alerttemperaturwert", "DX4-lrE6fLMHp5wOxwvMr": "Westliche Zeitzone", "7bV8hfRhGldGWdo8Vb1il": "Östliche Zeitzone", "y6VrMMGH4Mh4mO7HsHmBj": "Startzeit", "BOvye1w4YI1Hbo_9heqdM": "Ruhezeit", "Uu6qeV2XLpiwboneINLak": "Intervallformatfehler", "qeJLq1tfQF-5G4t003P0c": "Einstellungen", "fQWV5H2FZT3uLQ0sLLltN": "Bitte Empfindlichkeitsstufe auswählen", "nP1vM9sfXoEhoHMXN3-j7": "Bereichseinstellung", "ePlqFNBNJSFjQI6j-eSUi": "Vibrationspegel", "mCtr-rCplu_IYmqN77Cw9": "Plattform", "SLQRfUeAU4Dvy5O1WNHT0": "Plattform+Nachricht", "tWFKyzzfR_wlbUtuvEIGj": "Plattform+Nachricht+Telefon", "tWFKyzasdfsdfsdfsdfdf": "Plattform+Telefon", "Qjag3m9P6bSba14_Zw745": "Vibrationsmodus", "Bs2brZAj-iy3iAW34SB3D": "Erinnerungsweg", "NXoB1WqRAzDcN0HJsa0aw": "Rückkehrintervall", "tziOOsAmI3dacmVYXP1qI": "<PERSON><PERSON>", "0iPIesWJahFY1C6WIW2Sr": "Zeit des Erwachens", "0tZ4moV7mgZFjX4qqmNw5": "Übungsintervall", "gbhcMXdMMOyETBXIHgDZ-": "Statisches Intervall", "j5J2lzHmNEClnupw7vgF8": "In diesem Modus schaltet das Einstempeln bei der Arbeit die GPS-Positionierung ein und das Einstempeln nach der Arbeit deaktiviert die GPS-Positionierung;", "Es2otsyREhsk7D6vv3TBu": "Arbeitstag", "PdWLpHAYftr8mUT61cQVk": "Samstag", "om5IfTKXo7Qi6fz986E6c": "Sonntag", "CHuzsvL1dgA0SVIzXsUON": "Aktivieren Sie in diesem Modus die GPS-Positionierung nur innerhalb des eingestellten Datums- und Zeitbereichs;", "KeBSto-LPbBMKb7eta5NT": "Zeitgesteuerter Rückkehrmodus", "mtCLx2EVthWETG3lLl1k8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Nfck51t_8ikXK6fKDt2g5": "Wöchentlicher Positioniermodus", "q7c1wxDQihrNW-5hKBXwK": "Echtzeit-Tracking-Modus (Leistungsaufnahme)", "giACddDYdcUy9-wkgaotd": "Intervall-Tracking-Modus (mehr Strom sparen)", "nbB7jDHhjihV246qgHGii": "Echtzeit-Positionierungsmodus", "9YwwBZ2KTfGNGEt_PjDj7": "<PERSON><PERSON>position<PERSON><PERSON><PERSON>", "FbU62YUMNlB98e4figT9t": "Normaler Tracking-Modus (Energiesparmodus)", "IyWI4ui9nSVwNx6hh5l3Q": "Einzel-Weckmodus (sehr stromsparend)", "6wNw-7dzRHALobiqhwNHX": "Zeiterfassungsmodus (mehr Strom sparen)", "1ubUpBg3y15OGlxqErTwT": "Timing-Tracking-<PERSON><PERSON> (sehr stromsparend)", "kppRYbv1HsYqNSpzmnz2_": "Wöchentlicher Tracking-Modus (sehr stromsparend)", "lYy-c9I-wddGRhq3__n-o": "Einzel-Tracking-Modus (sehr stromsparend)", "_pLfdO5dD6EIHGzn3EccU": "Intelligent<PERSON>", "a1n_G5EuFGYkPvjxlUwuQ": "Zeitraum Positionierungsmodus", "ZumqxPha0ETt4s6IWj_pM": "Check-in-Modus", "bFR6ganf7Gh2ntOnWJZKM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "o6e4_Chy0zEchuvkyrCSC": "Überwachen", "XoaIcv8RDjIKhJCCe-Bgg": "Fehler ändern", "a-Eywl004h8c0Z8yaNvPq": "Normaler Tracking-Modus", "351stQZgFO1epbSXb6-g5": "Bitte Intervallzeit eingeben!", "CVeOX4a2fE17TSJnYHWF_": "Bitte geben Sie das richtige Zeitintervall ein!", "PxTrEplTrqAsSWEaCaA5L": "Bitte Uhrzeit wählen!", "OFZLob4ixsZGAbEMkCeGB": "Bitte wählen Sie den nächsten Wartungstermin", "kB5kAAr1kMy-xk7poKZjB": "Bitte geben Sie den nächsten Wartungskilometer ein", "_YGwsF4tX9xp3j5lGqgDk": "Der nächste Wartungskilometer muss größer sein als der letzte Wartungskilometer", "kOzwGA87dUbTPO2J8VAAE": "Der nächste Wartungskilometerstand muss größer sein als der gesamte Wartungskilometerstand", "5xdEeVm0qnz9SxT7MG8qw": "Auslaufend", "5xdEeVm0qnz9SxT7MGrnq": "Abgelaufen", "rPn2PAqmNooNekpD8vHZ_": "Freigabe-Link abgelaufen", "cA4mkzbvSQKVnKohu-NBc": "Verbindungsfehler", "7ddIPjWcWGS0crmj1wera": "Überdrehzahlschwelle", "7ddIPjWcWGS0crmj1tuff": "Höchstens {0} Elemente ankreuzen", "7ddIPjWcWGS0crmj1dwcW": "Tipp: Nach erfolgreicher Ausführung des Befehls wählt das Terminal automatisch die eingestellte Nummer", "aHz9YjNwCHLIwsfvnU8-X": "Normale Benutzer können keine Untergebenen erstellen", "SYFx7Jw39HypqfmpAoSMi": "Wartungsaufzeichnungen sind bereits vorhanden", "ubZxA7UWGmQd_Gn_FpuUS": "<PERSON><PERSON><PERSON>", "9OMax5dpvzrcm_Krk5Qc1": "Ein", "L1vd4W1ECVlMaIZ_IDKvr": "Aus", "rEY8A9V_1uxYDZCM5MwZq": "Gerät nicht aktiviert.", "kB5kAAr1kMy-xk-flusio": "Hauptkraftstofftank", "_YGwsF4tX9xp3j-flusio": "Max", "kOzwGA87dUbTPO-flusio": "Minimum", "5xdEeVm0qnz9Sx-flusio": "Voller Tank", "5xdEeVm0qerterthlusio": "Kraftstoffmengen-Alertwert", "rPn2PAqmNooNek-flusio": "Standardeinstellung", "cA4mkzbvSQKVnK-flusio": "Kraftstofftankform", "qwersfasdfegergewvdfd": "Hilfstank", "retrrtdxsgerhdfgshsi2": "Maximal volle Box", "dfgdfthtrrhfddfglusi3": "Maximal leere Box", "aHz9YjNwCHLIws-flusio": "Standard", "SYFx7Jw39Hypqf-flusio": "Oval", "ubZxA7UWGmQd_G-flusio": "Runde Form", "9OMaeerfdgswertehhrtf": "Erweiterte Zeitabfrage", "9OMax5dpvzrcm_-flusio": "Unregelm<PERSON><PERSON><PERSON>", "-mwXOBzafJJzS5ECa96HK": "Insgesamt {0} -Datensätze", "e83QYflrstV3mHtOKQUA8": "aktualisieren", "2wrWc6_xwJGFFzHae1FBm": "Aufzeichnung", "8_IICEycxVRCZ9oVUc4XO": "Die IMEI-Nummer kann 1 Zeit für die Lebensdauer-Karte ändern.", "NORhEcC5v7cp-BdxgZl5u": "Neue IMEI", "wYeSVrHLh5AYblQHEkeLW": "<PERSON>te geben Si<PERSON> eine neue IMEI ein", "yzdVhW5qve_BhwkYrJnC2": "Bitte das geänderte Modell auswählen", "913JlvNitsiZ1R7_ypSye": "Startdatum", "znQ4cCuYAjv_vrmmN8SV7": "Enddatum", "xSIfVXjSrVDgcrpNE2Y5q": "Vor", "hwx9hX29-Q8iAi0Baajhn": "<PERSON><PERSON>", "6g6wxHvMzcyEZqSUFYY97": "Alte IMEI", "9Z5P7gAyBPa7F9zacoYIW": "<PERSON><PERSON><PERSON>", "5oCJuHcl4bkZHJoS78gHM": "Bitte geben Si<PERSON> eine gültige IMEI ein, die im System existiert, bitte erneut eingeben!", "U-u7gdzttM7kMFNe73MuA": "Bitte geben Sie die Lebenszeit-IMEI ein!", "0rcBbUV3Iju7g4de37PhZ": "Die IMEI wurde ersetzt, bitte erneut eingeben!", "8OxUTZHtyqXfbhaGnWguO": "Lebenslange Ausrüstung kann nicht durch lebenslange Ausrüstung ersetzt werden!", "dN8YQHV0UmJ0biAdQ5eUs": "Die neue IMEI kann erst nach Auswahl des Modells und Import ersetzt werden!", "aJqS-MRCOEzT3vAoobwyA": "<PERSON><PERSON> <PERSON>, dass Sie {0} durch {1} ersetzen? Klicken Sie auf OK", "safweerhgrsgsdgerwetd": "Bitte wählen Sie einen Zeitraum", "rethrtyjsdfsdfgerhrtj": "Bitte wählen Sie den Abfragetyp aus", "5M1oL9E7Oa4VYJrSBCMI9": "Temperatur> = 20 ° C, dann wird ein <PERSON> aus<PERSON>ö<PERSON>, wenn die Temperatur 20 ° C erreicht", "21C7S-yVPUtM_umDE9-D3": "Unterstützt nur die Eingabe von 10 bis 3600 Sekunden", "8EZPAmUvEcgIQlu9Af7lO": "Unterstützt nur die Eingabe von 180 bis 86400 Sekunden", "0Yj_478WQcnoLCTAqhX28": "Das Gerät meldet eine neue Standortinformation neu", "5VyshM83XNAdnIDpGFwQe": "Ob SMS-Erinnerung eingeschaltet ist", "sL8DUa562p1PB4TCqLQN0": "Zeigen Sie die gebundene Nummer an", "bFypuArP-qFt5NBFfwsRw": "Den Verteidigungszustand stornieren", "HJMoeiuNxOViTffIvIAGI": "Verteidigung Nachdem Sie länger als die eingestellte Zeit stationär stehen, kann der Vibrationsalert nach der Festung ausgelöst werden.", "eosV4vPkvxGINYFnMqTOc": "Die Parameter-Set von Gerät umfassen Gerät IMEI, <PERSON><PERSON><PERSON><PERSON> von Intervall, SOS-Nummer, Mittellennummer, Zeitzone, GPRS-Switch, IP-Domänenname und Portnummer usw.", "XoYqBt9yTe2Cumcn88i3-": "Der aktuelle Status des Geräts, einschließlich GPRS-Verbindungsstatus, ob externe Stromversorgung angeschlossen, Spannungswert, GSM-Signalstärke, GPS-Status usw.", "8pCLreWQJI7SM-dCnGDqa": "Die spezifischen Längen- und Breitengraddaten des aktuellen Gerätestandorts", "qDp8wbELHWHCtXR4kIKLr": "Aktuelle Geräteprogramm-Version", "dnRal8YUBaR4lwcct5KZh": "GPRS-Status, Anzahl der verwendeten Satelliten, Satellitensignalebene usw.", "Aiy8_YOvXwvR1y6NH8vzH": "Legen Sie den Timing-Return-Modus ein, der Wochenmodus und der Alertmodus sind nicht effektiv.", "emPLIpp281tgE1Y9-SfqX": "<PERSON>eben Sie für das Zeitintervall 0 ein, was <PERSON><PERSON><PERSON><PERSON>, den Timing-Return-<PERSON><PERSON>.", "qPtdLQjjVdR-hBSU9vYhO": "Legen Sie den WEEP-Modus fest, er sendet automatisch den Timing-Return-Modus, und senden Sie dann den Week-Modus.", "0fmV32e9MeQyY5lqIXLf6": "<PERSON><PERSON><PERSON> <PERSON><PERSON> den Alertmodus ein, es schaltet den Timing-Return-Modus automatisch aus, schaltet den Week-Modus aus und senden Sie den Alertmodus.", "9kMue__kTvle19S0-jw98": "Falsches E-Mail-Format", "d9YwxF2OSgivkz7M8H5Bc": "Die Geräteinformationen werden zurückgegeben und können später in dem Befehlssatz angezeigt werden.", "asfwefsdfsfsdfsdfgrer": "Erfolgreiche Erneuerung", "ULolU9NS8lnR1QkO3rSsm": "Geänderte Ergebnisse", "3j-R_aY3-zIKPRKka1zBg": "Diese E-Mail-Adresse ist an ein anderes Konto gebunden", "bzVAxsJEtQtawIDU5RBt1": "<PERSON><PERSON> e<PERSON>ben", "6nl7HBlbHrOtUvi-N0OPM": "Alle Neuigkeiten", "b0rVRhAL29g8HAgKi6cD6": "Kommende Veranstaltungen", "4FFZ6E8mwSsIR99YCODZG": "Hall<PERSON>", "EG1qollS3y8DsffUEQ11i": "Sie haben {0} <PERSON><PERSON><PERSON><PERSON>, die gerade ablaufen. Um die Deaktivierung der Geräte zu vermeiden, verlängern Si<PERSON> bitte rechtzeitig!", "Y33r2M_1dN1ut_Bp2XdSU": "Warme Erinnerung: Gewöhnliche Benutzer müssen sich an den Dienstanbieter wenden, um das Gerät zu erneuern, und Händlerbenutzer können den Erneuerungspunkt nutzen, um zu erneuern.", "H9Nda-k7XDEYdYb0Oxd01": "Ungefähr ablaufen", "JgHgaEMi5JzduvJ65HHG8": "Zurück", "WKkPZV30ap53zWCZyuwuD": "<PERSON>e haben {0} <PERSON><PERSON><PERSON><PERSON>, die gerade auslaufen, bitte erneuern!", "Ux12pE1OeZhyzEB5Svd4O": "<PERSON><PERSON>", "GWh39-lGjyVpcylYO7u3j": "Insgesamt {0} -Datensätze", "YoxsJOzz0McmdP3PDAcTb": "Ablaufdatum", "6L0b2U18GaqDt7DpA_yiy": "Inhalt", "Hi_cEuIxsZ92-zskwssT7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zUiSwYI9z29DqQ37Yt4om": "Bitte geben Sie den Benutzernamen / Ko<PERSON> ein", "6VlwgEKwHw_dv9rtupqSQ": "Antwortnummer {0}", "htnsA0a3Ra2_fYJ3r-5x6": "Kilometerwartung", "13b7lm7aQ9H9sHFfoIXPB": "Aktuelle Kilometer", "c0nJYvCKZYWjJzvr-X9GM": "Letzte Wartungszeit.", "TNGaQ-bXQxYyhgHYvOsPC": "Letzte Wartungskilometerstand.", "hjO1Vg319X18G9FnlJEPY": "Voreingestellte Wartungszeit.", "KsoQwWQ7fu-RaTUnWvTl1": "Preset Wartung Kilometerstand", "s5SpmyxQe30oLsV-H-g88": "{0} Wartungsanzeige", "DKIGDk2OpzWx0lfNjGC9h": "Erfolgre<PERSON> er<PERSON>uert {0} Geräte", "iVazSwP-tbFP5TcNnyS7q": "Benutzerdefinierte Erinnerungen", "QDYMtaj3f4DzIQ0ZGMOPu": "Antwort auf Feedback", "yJvXIZPZa3JFBQNZpzKNb": "B<PERSON> du sicher, das du das löschen möchtest?", "e3XxVhLcsTxKYpgRDLYn4": "Vor der Erneuerung.", "5IzURNFLb869S2Iil0zBK": "Nach der Erneuerung", "RaCuDe8vPz5OURmKQNTMm": "<PERSON>e haben {1} Ger<PERSON><PERSON> auf {0} als unten erfolgreich erneuert", "7Qr_N6o7eIW6DnbI-fmC0": "Platz", "LxQFx33DbLbDCFMGVbm9r": "Alertgerät", "s5dkA82oFKqgptUbqsic4": "Veröffentlichen", "6X_prYTdd-NdxYrTzYiTv": "Ankündigung bearbeiten.", "S3hMIOyR7bzPA6vDcwblT": "Ankündigung aktualisieren.", "ZgqbT6PS21BDxcwIKFkO2": "<PERSON><PERSON><PERSON>", "O0aHYTeJjn0FH79_1eXvf": "Erstellungsdatum", "CW7ycrIhRhBNT3JiCPuvR": "Kundenname", "DNheNhg94oWEH8ZCjFPxE": "Entspannen Sie erfolgreich!", "mlTAaKUQGc5csKwzYGmsU": "Ankündigungsdetails.", "vmaYyrRFsLx1fdLisRQg5": "Event ist abgelaufen", "mIJPpkv893lCNJ6q9lO7P": "<PERSON><PERSON><PERSON>", "GancrVwpSQ_JP9HG1XU8N": "\"{0}\" wartet darauf, verarbeitet zu werden", "yW2jL_VVgNCa1ZK_CVOme": "In Bezug auf \"{0}\" wurde bearbeitet, danke für das Feedback", "mPnTB8w6f32SrMN4DMi6G": "In Bezug auf \"{0}\" wurde verarbeitet, benutzerdefinierte Reply", "dashloadi3JFBQNZpzKNb": "<PERSON><PERSON><PERSON><PERSON> bald ab", "dashloadiTxKYpgRDLYn4": "Statusstatistik", "dashloadi869S2Iil0zBK": "Online-Statistik", "dashloadiz5OURmKQNTMm": "Gesamtaktivierung", "dashloadiIW6DnbI-fmC0": "<PERSON><PERSON><PERSON>", "dashloadsdfghhdfgdfgd": "<PERSON><PERSON>", "dashloadiLbDCFMGVbm9r": "Vergleich", "dashloadiKqgptUbqsic4": "Versionsankündigung", "dashloadi-NdxYrTzYiTv": "Alle", "dashloadibzPA6vDcwblT": "Normal", "dashloadi1BDxcwIKFkO2": "Abnormal", "dashloadin0FH79_1eXvf": "Ungelesene Nachricht", "dashloadi9GePO6A6jBjr": "Benutzerablauf (optional)", "dashloadihBNT3JiCPuvR": "Anmerkungen (optional)", "dashloadioWEH8ZCjFPxE": "{0}Versions-Update！", "dashloadic5csKwzYGmsU": "Servicestatistik dieses <PERSON>s (einschließlich untergeordneter) ist kleiner oder gleich 30 Tage", "dashloadiLx1fdLisRQg5": "Dienststatistik dies<PERSON> (einschließlich untergeordneter) Plattformdienst ist abgelaufen", "dashloadi3lCNJ6q9lO7P": "IMEI-Nummer/Login-Konto/Kundenname", "toIP_sB0-7C-n32qKpzMk": "Aktualisieren Sie erfolgreich!", "t-pf5RwPxV_OcNP1WidEM": "Kopieren Sie den Share Link Erfolg", "Jj2KZHdeUO7O0GAA9qykl": "Verbindung konnte nicht kopiert werden", "rB3aU56xAS9EVj5XVZ5Yu": "Nachdem der Remote-Startbefehl erfolgreich ausgeführt wird, setzen <PERSON> sich bitte den Arbeitsmodus des Geräts zurück.", "pPGMtc0DrQn1jiEFpVe7Y": "Verfallsdatum", "Nln5aIwzw0Bqh386-rPkg": "<PERSON><PERSON><PERSON><PERSON>", "PBm_ngQE-uvzxq3XBwYOs": "Globales Tracking-System.", "4PUHYIKijDJgBGwb_X6oX": "Support JPG PNG-Format-Bild und die Größe überschreitet nicht mehr als 1MB", "OMFetLPAXXivTkvAOSIff": "Benutzerbild", "Q30JLZF3GiZdfWq1MaCR4": "Grundinformation", "6rP2gYwNfQwClJBEmxBEy": "Telefonnummer", "9sK_TGewQl5zfDn75RpIq": "Kontakt Anschrift", "KP0yfMnOJWx1FBWS-opEk": "Dienstleisterinformationen.", "kA-s0kUkwL45l6iYv-TKh": "Kontakt Nummer", "IokO2UlgeBdvUDTLyX6ln": "Ändern Sie das Kopfportrait erfolgreich", "YPoyixx0lWnv9IoARFDls": "Erfolgreich gespeichert", "vMngAC40PvIZ-niUfbb8U": "Neues Passwort einstellen", "mxMq8Gu5X1vUWmqndRe7g": "<PERSON><PERSON><PERSON>", "MslCKqFFYRY2OyL-Zo_Dk": "Dienstanbieter Zurücksetzen Passwort", "0-v98dDMuY2bdr08YIehg": "Datenschutz-Bestimmungen", "jD1yQ2o2qqCboJHfrm7uS": "Nutzungsbedingungen", "nmDH8KjrgNcL1-NXZoDqu": "Offene API", "Cyllha7ZgGJqvuwHsEqaD": "Konto/IMEI", "prX3JUTouWcIjV3_duH37": "Bitte geben Sie den Bestätigungscode ein", "ZcMHgev4PYIoGliHjPmtn": "Verifizierungs-Schlüssel", "NC9hUZ2SjihaL3Jguqap0": "Einen anderen ändern", "SP0RYsgGSCmrPHOn1LXCF": "Nächs<PERSON>", "BbZrXXe5mpSu5LWgE0htM": "<PERSON><PERSON> kann nicht leer sein", "HV2CNVNoo9AxRsVyBNYPM": "Bestätigungscode kann nicht leer sein", "bgEEvDhpqJetROOyhaAmD": "Das Konto existiert nicht", "caceMSPMiJj6-8Gnn-Mj5": "Bestätigungscodefehler", "q6NfOlwZotvZUoO9xT_Js": "Bestätigungscode ist abgelaufen", "-mkJJAlBTMfaCMMD_cUVA": "Zurück zur Anmeldeseite", "-1NoOoI1-XfLzqLSBP2cC": "Sie können den Dienstanbieter nicht kontaktieren?", "UrmoTz-bEoYVUZudlGkmj": "Abrufen der Methode ersetzen", "eDBwJLmqbZI_jLHPeNwLR": "Das neue Passwort bestätigen", "9o7fPzhzvlOW_AjZTYvQA": "Bitte bestätigen Sie das Passwort", "OV_u3cmw3_nqGdgXQhMq1": "E-Mail kann nicht verwendet werden?", "OWU3uV1ucUTxye3fWEOYn": "Neues Passwort kann nicht leer sein", "CS1umvWqPvy5fyx5vpLev": "Passwortlänge kann nicht weniger als 6 Ziffern sein", "VW5d-hqpDfpW1wxKz7GZB": "Bestätigen Sie das neue Passwort kann nicht leer sein", "7aDPEpfMB28Q6UpQ3KiPe": "Passwörter sind inkonsistent, bitte rufen Sie erneut ein", "AuAJ8pz6FdyRSEDF79UtG": "Bestätigungscode erhalten.", "rNhMvrlEMqTqvgVETayyC": "Erwerben Sie nach {0} Sekunden", "2lSdlLHY13XWUtSy6zJ1R": "Der Bestätigungscode ist abgelaufen", "3CyUohrSLLacv5So4dPGX": "Das Passwort kann nur eine Kombination aus Zahl, <PERSON><PERSON><PERSON>ben, Symbolen sein", "EYoz5FqDoi_qDRmAsXOAW": "Rufen Sie das Passwort erfolgreich ab", "Lv3v6w6vj3VIrTLiLi_uz": "E-Mail-Verifizierung", "rVQn6WUbLJreV967ZLfdy": "Sie müssen es per E-Mail-Bestätigungscode abrufen", "zb1y7CpuMLs3EACUW8OnM": "<PERSON><PERSON> müssen sich an den Dienstanbieter wenden, um das Passwort zurückzusetzen", "Oo_W9j9D9ILdAR5k-vcW6": "Das Konto ist nicht an die E-Mail gebunden und kann nicht authentifiziert werden", "CRyJ__yaI5l27i-XQkH-C": "Alle Rechte vorbehalten", "Atr_RssdPBb1BwK-5pdcT": "Bitte kreuzen Si<PERSON> zu<PERSON>t die Nachricht an!", "CGfq1mj1IRuij49lT_V9r": "Sie haben {0} <PERSON><PERSON><PERSON><PERSON>, die abgelaufen sind, um die Deaktivierung der Geräte zu vermeiden, bitte erneuern!", "zfAaSueFmo1M44F10jQfR": "Benutzer exportieren", "SrGP45dYF4TfIMabJ8GI0": "<PERSON>a", "PSJif2BD7ijRnlbhUCzbL": "NEIN", "Kclu7hitguzQnIENer23F": "Enthält inaktive Geräte", "nlTS_5a2cN5XVp4KtxjsC": "<PERSON><PERSON>uf<PERSON> verwendete Felder", "KcBBwSU5GF3Vi-dyE_STX": "Wählen Sie Alle", "d3qRaIFxfxVC8m9k5uUIK": "Ausstattungsart", "EVbsxypelcsn0TWWQ86fY": "SIM-Kartenstatus.", "9HmB7gxHH1KowJ2BRUtQz": "Ablaufzeit des Plattforms", "b-ZK14jqMcQs1RkyRsi6T": "Benutzerablaufzeit", "gwHls4t8DcXwChxc0iTMw": "G<PERSON><PERSON><PERSON>n.", "kPLQ9bi6YQ7CGlfsj1-Xr": "Gruppenname", "FxPry5C52XBPmrYIZYyL-": "Batterieinformationen", "gBEw5BIt6PcPC9zFkw1pu": "Spannungsinformation", "0RE8zR_4ssG3-vDm1dDFS": "Mitgliedschaft", "G0h5Phv0L2ZYiNkW6qjOD": "Erweiterungsfeld", "7hRo37AgY5tlz6R_c8A8E": "Staatliche Zeit halten", "QGdPkhCM7YopsUXXGEC-s": "Letzte Signalzeit.", "cnyEHrN3GNiGP2f0vJWse": "Letzte Positionierzeit.", "G3TxUVRv5FTSlKpEn1Ql6": "Feldkonfiguration", "0Aqxb_GDZq_HUfv9X1rx-": "Aufgabenliste", "rDoa54vkvUnzRjC8KfzbD": "Aufgabennummer", "u5VGmAhjxmg1wptxMpiCG": "Beinhaltet", "rt7d3vYgqgqlKgp8G0ZFl": "Beinhaltet nicht", "EjLZvKQnhIOeBl97DUMEI": "Felder exportieren.", "b2DWOt8gsb5UtOwfsq-0t": "Taskstatus.", "_ktpLizL1zQbR4S-2a9yS": "<PERSON><PERSON>", "PSkqmsVXAnXwS4IEg_PCw": "Ausführung erfolgreich", "qemv8Jr6YLWEMlEjIqd7u": "Ermordet werden", "ZTusx1IoqVDMV5n8ga3Kr": "Ausführung fehlgeschlagen", "G6xSzxmYbLAVmP3-A9tND": "Ausführung", "dd9U0JHJTew4zoPUYl11x": "Realisieren", "dbJgeT1ZDM7ccrHhrlq5m": "Ausführung", "f2qfydR_3cc6WkgIguaev": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NBv8StIMAV4Ss2MZz_cDl": "Erfolgreicher Download.", "tEmrFBYMHpu78vsHVCymD": "Smart Sleep.", "RhTgJ0vhpibaKqqZuOpiy": "Sicher möchten Sie das Gerät nennen?", "TxRDK1wUxDUn8xNJIkXRd": "Das Gerät tritt in den Offline-Status ein, wenn er länger als 10 Minuten stationär ist, in dem kein Befehl wirksam wird. Wenn sich das Gerät bewegt, wird er aufgeweckt und wieder online gehen.", "8GdL1zWMJptJ8AiAol3Yk": "<PERSON><PERSON><PERSON><PERSON>.", "OT5pi5ls02Yn-qXFicKOX": "Kartennummer", "i_njN3t7VF2mJP1xxkkNN": "SIM-Karteninformationen", "J7GYiYHl6YxxMz6DV14ZI": "GPRS-Funktion.", "nKkMpCeUmGpAEZOHFubgT": "AUS", "JH_oKJbhNVd2y3VDb6oS5": "Fahrzeug-Stop Alarm", "UWZiT2UI0jtd6umy4pP83": "Offline-<PERSON><PERSON>", "1BGMlf50Sr3tsvjmhkYJL": "<PERSON>ch dem <PERSON>, wenn die aktuelle Geschwindigkeit der Geräte-Hochladen der Positionierungsdaten <= statischer Schwelle, bestimmt die Plattform den Status als statisch", "lHr1G3dsLNsKPX2toJVeL": "<PERSON><PERSON> das Gerät offline ist, dr<PERSON>t die Plattform den Offlinealert des Geräts", "NT9rhnVfP_H1_BU9AAWXS": "Der statische Schwellenwert kann nur von 0 bis {0}/H eingestellt werden.", "ewfWEdsf3NsKPX2toJVeL": "Luftfeuchtigkeit", "ETjPg2LN1EYYtVQsyU1Si": "SIM card drain time", "zLKnijHT1o-_DOpaFEyZS": "Basisfelder können nicht leer sein", "4o8gSmZGcrwz70qV4Eg6q": "Ölstand Alert <PERSON>", "vovDYOhKoxvccZkmUQPKg": "ACC-Test", "O_u_DNP0PxptNrO2O0dhC": "Vorwärts- und Rückwärts-Rotationserkennung", "Yhb1FXkSo-2p_oQYUpudn": "Plattformbenachrichtigung.", "CVCBH6Y-uYJ1j-m2sYz_A": "SMS-Benachrichtigung", "erhrhxfWEGsfdgeergdfs": "Kartendaten", "sdfghESDGdgeerREFgdfs": "Nutzungsbedingungen", "aEN_Am4XWjsh0ktkF23iA": "<PERSON><PERSON><PERSON>", "dFkTJxdatpPvSNfTqBOLk": "Bitte erstellen Sie zu<PERSON>t eine Rolle", "fwgdgafafewgrhraqrger": "Überwachungsnummer prüfen", "erfdfhhgtrhrtsrefgbhg": "Anzeige der Monitornummer", "ewrrertrhhfdhhfefgbhg": "Sind <PERSON> sic<PERSON>, dass Sie die Monitornummer überprüfen möchten ？", "fersdgsdfzsefrgfergrd": "Automatische Verteidigung", "wqsdgsdgdhwrthtyfghb2": "<PERSON><PERSON>", "5dibUgEIhQ_nxsvtyH-Gn": "Trackzeitintervall", "HT5-DuSlKusr_PXRq3BKi": "{0} bis {1}", "YjNIXBe35TxkPNmhQXjsl": "Task herunterladen wurde e<PERSON>, wenden Sie sich an die Aufgabenliste, um den Fortschritt zu überprüfen", "Bad9L51R3bTOzbZK96v4M": "<PERSON><PERSON><PERSON><PERSON>", "sdgerhtnzbZKrtr4A445D": "Hinzufügen", "sakFjkCsiFnKnbi32nwdG": "Übergeschwindigkeit", "adasdwasdafadq321asda": "Nachdem die Einstellung des Einstempelns abgeschlossen ist, sendet das Gerät beim Ein- und Ausstempeln die Plattformbenachrichtigung, die in den Nachrichten der Zentralstation angezeigt werden kann. Klicken Sie auf den Gerätenamen, um die Betriebsstunden des Geräts innerhalb des Zeitbereichs anzuzeigen", "asdwasafwsadasda32ssa": "Nach einer langen Änderung wird die vorherige Setup-Zeit überschrieben", "Be2562h253grgsHHJDbRDAF": "<PERSON><PERSON><PERSON>", "BtyuwyfgrWERERRTHDAsdDF": "Schnelle Verzögerung", "BtyjdfghtwsrgGHFEEGRDAF": "<PERSON><PERSON>elle Beschleunigung", "bausguOI4chuJCSUjcnwoo8": "<PERSON><PERSON><PERSON> (Zeiten)", "cveuiHXBbcjNCI65cvehJCI": "<PERSON><PERSON><PERSON> (Zeiten)", "YBGBXHcbeiHBUIbjhcwoi54": "Schnelle Verzögerung (Zeiten)", "wugyrgHVBUJncw6cf4whcxc": "Leerlaufzeit darf nicht leer sein", "FDhxhjhe6gv5_cekhq64cxX": "Überdrehzahl (Zeiten)", "xcjw54cXHDCG3cw_xkklklc": "Overspeed Setting (Times)Overspeed Setting", "ScefhxDXWc654CDCHcnxopc": "Innerhalb einer Stunde", "cxbuhy_cjnbxnX54VE6Vcjc": "Innerhalb von 1 Tag", "cxbujHJXIH5xcbujxbic45v": "Innerhalb von 7 Tagen", "chchguiHGCEW46GFXhcij_X": "Innerhalb von 30 Tagen", "xbcuCBUWCBIz56cv6we_xni": "Innerhalb von 60 Tagen", "vniHCIHznjvoeg5fwhncicm": "Mehr als 60 Tage", "bcxwuGXUG_xnwfd3vhjxwio": "<PERSON> an zu arbeiten", "bcwihn_xnwf5fdcdfoijqdc": "Raus aus der Arbeit", "xujwguycx_xwf5v465cw6xa": "<PERSON><PERSON><PERSON> gehen", "bncwvc543_xwjbdncmjJXjo": "<PERSON><PERSON> spät sein", "cbujBCXIHCXCVW5VBjbzhih": "Check-in-Zeit", "xbucBNXB_CXNVE4V5C5C55X": "Check-in-<PERSON>ort", "cjwcnwf52vcwb_cwekfnbxc": "Einstellungen anzeigen", "xbujqwfdbn_ckeneg2vewrh": "Die Startzeit ist die gleiche wie die Endzeit", "xbjwgbcxufgvdd542x4chxc": "Der eingestellte Zeitraum überschneidet sich, bitte zurücksetzen", "cxbwf_cnev52cwhnicwxcqm": "Erfolgreich eingestellt", "xufvuxinbqx9cn3wr2fvg2f": "Satz fehlgeschlagen", "cbnwjedb_cxwkf6cfqhbcxq": "Arbeitszeit", "xgbuwedfcx_xqwdbq5xdqwh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuwhbc_xbwf52xhxxqwdsxx": "Temperaturanalysetabelle", "bnciwchi8cb2891dhxc129x": "5 Minuten", "xbu2iby827tg29c89x9yd9y": "3 Minuten", "xhi21b4xy89y1201090u1ed": "1 Minuten", "xqigd92x3t82brcvydgdiac": "Alle Status", "cbuwb2x86y129bx119bdh21": "Details zur Spannungsänderung", "zgu2918dfgcx809rc429_cx": "Die Ablaufzeit des Benutzers einiger Geräte ist länger als die Ablaufzeit der Plattform, und die Änderung schlägt fehl. Die Gerätenummer ist", "cnbN8SYBnNXcj38u3vcv9cc": "Detaillierte Adresse", "dxb82y12ec2ycxhn23982x9": "Ausgewähltes Gerät", "sasdfwsdJfioOsadOI_1A": "Bitte wählen Sie die Ablaufzeit des Benutzers", "Ksdojos_sadiokvn15sSW": "Die Ablaufzeit des Benutzers einiger Geräte ist länger als die Ablaufzeit der Plattform, und die Änderung schlägt fehl. Die IMEI lautet:", "Osasd_ojoiasdlkj_wass": "Bitte wählen Sie das Gerätemodell", "aOuinlkvi_5sdAvOsdWOm": "IMEI darf nicht leer sein", "ansiuhWjhd_asd_QdwxDw": "IMEI existiert nicht", "oASdoioijds_sad12Adwd": "Ger<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein", "WGSFGDSgdFnKnbi32nwdG": "<PERSON> kleiner der Wert, desto höher die Genauigkeit. Es wird empfohlen ihn auf zu setzen", "1dfgnfgndgadqGRTH234S": "Anmerkung", "2dfgnfgndgadqGRTH234S": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dfgnfgndgadqGRTH234S": "Auto", "4dfgnfgndgadqGRTH234S": "Motorrad", "5dfgnfgndgadqGRTH234S": "<PERSON>", "6dfgnfgndgadqGRTH234S": "LKW", "7dfgnfgndgadqGRTH234S": "Bus", "8dfgnfgndgadqGRTH234S": "Mischerwagen", "9dfgnfgndgadqGRTH234S": "Taxi", "10dfgnfgndgadqGRTH234": "Polizeiwagen", "11dfgnfgndgadqGRTH234": "Landwirtschaftliche Maschinen", "12dfgnfgndgadqGRTH234": "Boot", "13dfgnfgndgadqGRTH234": "<PERSON>ug", "14dfgnfgndgadqGRTH234": "<PERSON>der", "15dfgnfgndgadqGRTH234": "<PERSON><PERSON>", "werERGEDjty47thwhrw2r": "Zugriffsserver", "hrthwertehhgrjrryyuyu": "<PERSON>te geben Sie die 11-13-stelligen SIM-Kartennummern ein", "tyjwerTEHAWEERerghrt5": "Fehler beim Formatieren der SIM-Karte", "SAF2344asdfgre2425321": "Vertriebshinweis", "asduiunkj_sojdAojdjio": "<PERSON><PERSON> wird em<PERSON><PERSON>, j<PERSON><PERSON> {0} IMEI nicht zu überschreiten", "lkhoasdoi_ahdjkjnihds": "Importieren nach", "asdioi_asdjhsjiohs4ij": "Geben Sie eine IMEI hintereinander ein (es wird empfohlen, jedes <PERSON> nicht mehr als {0} IMEI zu haben)", "asiIojKODiidoOIjiwkiw": "Gerät nicht gefunden", "rerdsbfgrygRG8retEqrg": "Letzte Woche", "trhhfwertdgdfgqWERs45": "<PERSON><PERSON>", "35sfdgwERGRGg534sbvfv": "Letzter Monat", "hu8crhU0O5-9ObaQNtymN": "Schalten Sie die Leerlaufanzeige des Geräts ein", "idlingDesc": "P.d. : wenn du keine falscher alarm empfangen willst, kannst du auch \"ausladen\" schließen", "bzVDxO5OkfMskWoNiFsov": "Standalarm konfigurieren:", "s8x02hV2p9E13TkI1Xr0s": "(1) ACC-Status ist eingeschaltet;", "561Z1QlQSV9ALHXwaOA2y": "(2) Im statischen Zustand überschreitet die statische Zeit den eingestellten Wert (1-60 Minuten);", "cnWw9L3lCRWHXyv4IW7KN": "(3) Der Positionierungsmodus ist nicht 'unpositioniert', und der Wiederherstellungszustand ist 'stationär', wenn das Fahrzeug nicht positioniert ist;", "86pBnkS4xC_Am4khna2WU": "Stationäre Definition: Die Geschwindigkeit ist kleiner oder gleich k km/h (K ist der stationäre Schwellenwert), im Allgemeinen beträgt der Standardwert 5 km/h;", "aasduii_15jdiusdiojAw": "Tankrate", "asdwfhjkxohuUIjudoiuw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oiuisauih_soIasdiKdiw": "Bereichszoom wiederherstellen", "PjjiIkjh_1iojIjkdklss": "Reduktion", "kjhOojolKJkjuh_alk2Wd": "Als Bild speichern", "asd_ihjk123_asdjsajhd": "<PERSON><PERSON> passenden <PERSON>", "xgwuedhcuiwhwdhwciqiu": "Die Leerlaufzeit muss eine positive ganze Zahl sein", "uiwsh872y89cb781c289s": "Ungültiger Parameter", "knKLyHJUTw7crKa9duKC9": "Der maximale Zeitraum darf einen Monat nicht überschreiten", "asduiijoh_jkakjsdui15": "Breiten-und Längengrad", "9VVTIU-Eu0k8W5Q_IiKoj": "Optionen", "Td5X-QbKaFt31TSs9FTV2": "<PERSON><PERSON>", "JBp30yKusF3Jvw8cQVZZj": "Satellitenpositionierung + LBS", "y6P82AgvLwCA3gRisElPs": "Satellitenpositionierung + LBS + WIFI", "ewr2334qRTEWrg34534t5": "<PERSON><PERSON>", "ewrasf52334qRTEW425321": "Einstellen/Entfernen", "asdiOIosdj_564Hjoijid": "Untergeordnete Kunden hinzufügen", "hk-j6yAbxH519LUQTapg5": "<PERSON><PERSON>en Si<PERSON> weitere Informationen ein (optional)", "2kiLagD1hB6MjdguFOHeh": "Historische Suche", "znIORRJA2sCWxEuuJGAf9": "<PERSON><PERSON>", "nEXvu2BRLQvSN2q4zNh16": "<PERSON><PERSON><PERSON>", "PDD2x6KmulIA28JPPiWmf": "Entfalten", "T2Sz03hEpYKf8z_mXsC9e": "Erfolgreiches Teilen", "wUttprQVTKne9Pjj_eas4": "Verknüpfung", "Todi_jvRP_qH1l9la6K-I": "Ihre Freunde können Ihren Standort in Echtzeit über diesen Link überprüfen", "i_FwdBnrmb7MzNMyx9Md2": "Erkennungsmethode", "7Lg2fzoMMZD9T8t0xpX_P": "Kapazitiv", "nyTzyI4JkbFylgZL_8s6v": "Ultraschalltyp", "qPY1Oqb4GFE6p3lvhoJef": "China", "rvZdqRnVvEGgrInYrcao_": "Ausrüstungskapital", "98zGSk76EGdJ2iIIoKbxe": "Gesamt", "dfsf253663wreqQTRH223": "Bitte geben Sie den korrekten Verschiebungsradius ein", "iCfNkRWDcS38BMFKnNEe6": "Der Exportzeitraum darf 7 Tage im Voraus nicht überschreiten", "sdfWET34N6n345knk2tt2": "Schrittzählermodus", "wetdsfgT45626n345knk2": "Schrittintervall", "WE65fgT45626n345knsdf": "Bitte geben Sie den korrekten Intervallbereich ein!", "dsSFGSDFh753A6n354Y45": "In diesem Modus aktualisiert das Gerät die Positionsdaten gemäß dem eingestellten Schrittintervall.", "weafaE354SF234AFasdfw": "Fatigue Driving Alert", "sdf2345afaHHF234AFasf": "（1）<PERSON><PERSON> dem Einschalten, wenn das Gerät ACC kontinuierlich eingeschaltet ist, überschreitet die Plattform weiterhin den Alertwert und den Alert", "safw36g346347DFGdfg34": "（2）Bestätigen Sie vor dem Öffnen, ob das Gerät an die ACC-Leitung angeschlossen und die Verkabelung korrekt ist.", "rzPd23XcIb6rhIjDQxfQI": "<PERSON><PERSON><PERSON>, um Gruppen zum Sortieren zu ziehen und abzulegen", "nle_9j8akFLmPCwbA6Tvt": "Drag & Drop ist im Bearbeitungszustand verboten", "JnbwpzQ1es7Knnztzs8BF": "Update fehlgeschlagen", "jCngDisNEtHDk2AbrnGT0": "Übertragung fehlgeschlagen", "_pPqRg2tUY4Ks1XYYQ22Y": "Bitte bestätigen Sie, dass sich der Stapel von Geräten unter demselben Konto befindet", "gxywuyxyudeytgqdytgxq": "<PERSON>ä<PERSON><PERSON> in 7 Tagen ab", "xbgwdygfcwygxqgbcdgyc": "Läuft innerhalb von 30 Tagen ab", "xh27t6gd2ftx281xg1yd1": "Veraltete Gerätestatusdaten werden stündlich aktualisiert", "xg72stb27826td26d1276": "Abgelaufen", "asfwe87345a99T34g36kd": "Spurfarbe", "xg27td76gsdgb198ys1y8": "Ihr Passwort ist zu ein<PERSON>ch, es wird empfohlen, das Passwort zu ändern!", "xh278dgtcfg7xchgx1h8x": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hinz<PERSON>", "EG4bzTy443aMfL9yHir_p": "Zusammenbruchsbedingungen", "XYgitqAwgNUZ_WMaw417U": "<PERSON><PERSON><PERSON> Bed<PERSON>ungen", "hjYHD87gdSZHBXCH8FJ9H": "Passwort merken", "cbuuwxgh7TX67XFGGCHCH": "Passwort vergessen?", "HX8UYguGBXCUYGWYGFHYU": "Erfahrung", "EsLrJ7laPcMXBxHwm2UoS": "Benutzeränderungszeit synchronisieren?", "xhq87s8s1ds1hdh1dhddh": "Verwaltungsregionen", "GSAGuew823hdhdusuxjyy": "Zauntyp", "hd823d5ddchf28H8xhd89": "Eingangs- und Ausgangszaunstatistik", "dghd72f22ffdhdydyud2h": "<PERSON><PERSON><PERSON> der Fahrzeuge, die den Zaun betreten", "cbchd72hfcbchjxzjzjjj": "Anzahl der Fahrzeuge aus dem Zaun", "mvndjdbxjdjdhchd8dhjd": "Durchschnittliche Aufenthaltsdauer", "Bhxchjsd83947fhdhsjjm": "Einzelheiten zum Ein- und Ausgangszaun", "cn8c287y2gdhgdhxhxhhh": "Name/IMEI", "vnNDHhchc87yffggvh28f": "Bleibe >=", "MXDNihc837yhv8vhjcjjl": "Zurechenbarer Kunde", "fn3uyaBGUGYBCX87bxhui": "Eintrittszeit", "vmveioujf89c_xhje8hjm": "Zeit aus dem Zaun", "vmbmbiu4hbHCX8ycbchjv": "Dauer des Aufenthalts", "MXCghc87f2hgvhjdsjsus": "Spur anzeigen", "cnc8c2hgJHXHihxuif389": "Zaunalertdetails", "sgeert4645FASRG23XFGg": "Statische Zeit muss eine positive G<PERSON><PERSON><PERSON> sein", "eq9r0DyDWJue8aasqqeSH": "Benutzerhandbuch", "asdf45DFfg325sdg5FAS1": "Zeitüberschreitung bei der Befehlsantwort, das System sendet den Befehl später erneut.", "afeSR5SDG4g45gs23SGWS": "Der Befehl wurde erfolgreich ausgeführt", "sdfwgrt457226EWT435fh": "Sie können die Geräteinformationen ändern, indem Si<PERSON> ein Formular hochladen. Die Formulardatei muss im Header-Format der Vorlagendatei eingegeben werden.", "WETNK235asgkn34Edkjgn": "Die folgenden Felder sind für Importänderungen verfügbar:", "gfh3345sdg3425SDG2864": "1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (IMEI) - <PERSON><PERSON><PERSON><PERSON>, 15-stelli<PERSON> <PERSON><PERSON>", "sg36SER676sd2YR25745F": "2: <PERSON><PERSON>-<PERSON><PERSON><PERSON>mer - optional, 4-20 <PERSON><PERSON><PERSON>, Buchstaben", "58FFRTJhrh565RTTRJ353": "3: <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>chen - optional", "YFJ45345fhf5674RST345": "4: <PERSON><PERSON><PERSON><PERSON><PERSON> - optional", "dsfg64YRrtjty56645asR": "5: <PERSON><PERSON><PERSON><PERSON>hin<PERSON><PERSON> - optional, nur für Händler sichtbar", "fhj25ERT765RT3453rgrt": "6: Kontakt - optional", "as357832WERU2458sgery": "(xls、xlsx、csv，nicht mehr als 5 MB)", "erg346RY455Yt33462rgg": "Ein Teil der Informationen wurde erfolgreich geändert, und die Liste der Fehler lautet wie folgt:", "dXYpGu45Rm1EWLEqXyc1N": "offene Plattform", "g2345sdfg6t3300462rgg": "Kraftstoffstrom", "Yhr9Y0kuM8ANBfaRN3_NQ": "<PERSON><PERSON><PERSON>", "kM8fJsLkgzgRDpq2T_SUx": "Route", "4PkVpEbTVm-b0kGGM-f4S": "Fahrzeit", "6TcxQMYvgXAQ_JSBxXWMD": "Unterstützte Geräteelemente", "6TcxQMYvgXAQ_JSBxXWa1": "Versatzabstand", "6TcxQMYvgXAQ_JSBxXWa2": "<PERSON><PERSON><PERSON><PERSON><PERSON>eit", "6TcxQMYvgXAQ_JSBxXWa3": "Einzelalert", "6TcxQMYvgXAQ_JSBxXWa4": "Intervall {0} Minuten", "6TcxQMYvgXAQ_JSBxXWa5": "Bitte wählen Sie die Alertzeile, die gelöscht werden soll", "6TcxQMYvgXAQ_JSBxXWa6": "<PERSON><PERSON><PERSON><PERSON>", "6TcxQMYvgXAQ_JSBxXWa7": "Begrenzung der Liniengeschwindigkeit", "6TcxQMYvgXAQ_JSBxXWa8": "Bitte wählen Si<PERSON> eine Zeile aus", "6TcxQMYvgXAQ_JSBxXWa9": "Bitte Zeit auswählen", "6TcxQMYvgXAQ_JSBxXWb1": "Bitte wählen Sie Versatzabstand", "6TcxQMYvgXAQ_JSBxXWb3": "Bitte wählen Sie den Weckweg", "6TcxQMYvgXAQ_JSBxXWb4": "Bitte geben Sie die Überdrehzahl-Alertschwelle ein", "6TcxQMYvgXAQ_JSBxXWb5": "<PERSON><PERSON> nur e<PERSON>", "oMUEw9fns5Q_1nPpGRnbu": "Linienstatistik", "luet6c7PKCXPFY01x20CW": "Bitte wählen Sie Alerttypen aus", "luet6c7PKCXPFY01x2001": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "luet6c7PKCXPFY01x2002": "Alert bei Leitungsüberschreitung", "42iJdaFze5K4luDvSkt2k": "Benutzer gehören", "42iJdaFze5K4luDvSkt21": "Standort starten", "42iJdaFze5K4luDvSkt22": "Standort beenden", "wegasqASDE32446sgwe01": "<PERSON><PERSON>", "wegasqASDE32446sgwe02": "Werkzeuge", "wegasqASDE32446sgwe03": "Ra<PERSON>enprü<PERSON>ng", "wegasqASDE32446sgwe04": "Regionaler Autocheck", "wegasqASDE32446sgwe05": "Navigation", "wegasqASDE32446sgwe06": "Regionalwagen", "wegasqASDE32446sgwe07": "Linienverwaltung", "wegasqASDE32446sgwe08": "<PERSON>te geben Si<PERSON> einen Liniennamen ein", "wegasqASDE32446sgwe09": "<PERSON><PERSON><PERSON>", "wegasqASDE32446sgwe10": "Die Leitung wurde auf Alert e<PERSON>, und die Alerteinstellung dieser Leitung wird synchron gelöscht, wenn sie gelöscht wird. Soll mit dem Löschen fortgefahren werden?", "wegasqASDE32446sgwe11": "Leitungseinstellungen", "wegasqASDE32446sgwe12": "Leitungsalerteinstellungen", "wegasqASDE32446sgwe13": "<PERSON><PERSON><PERSON>", "wegasqASDE32446sgwe14": "Flugbahnzeichnung", "wegasqASDE32446sgwe15": "Navigationszeichnung", "wegasqASDE32446sgwe16": "Klicken Sie auf die Karte, um die Route zu markieren (klicken Sie, um den Standort zu bestätigen, doppelklicken Sie, um zu beenden)", "wegasqASDE32446sgwe17": "Selbstnummer", "wegasqASDE32446sgwe18": "Linienbreite", "wegasqASDE32446sgwe19": "Routenfarbe", "wegasqASDE32446sgwe20": "Startpunkt", "wegasqASDE32446sgwe21": "<PERSON><PERSON>", "wegasqASDE32446sgwe22": "Zurückverfolgen", "wegasqASDE32446sgwe23": "<PERSON><PERSON>ut auswählen", "wegasqASDE32446sgwe24": "<PERSON><PERSON><PERSON>en Sie eine Geräteroute > >", "wegasqASDE32446sgwe25": "Wählen Sie eine Geräteroute", "wegasqASDE32446sgwe26": "Der maximale Zeitraum darf {0} Tage nicht überschreiten", "wegasqASDE32446sgwe27": "Navigationslinie", "wegasqASDE32446sgwe28": "<PERSON><PERSON><PERSON>, um den Startpunkt auf der Karte zu markieren", "wegasqASDE32446sgwe29": "Markier<PERSON> den Endpunkt, nachdem du auf die Karte geklickt hast", "wegasqASDE32446sgwe30": "Wegpunkt hinzufügen", "wegasqASDE32446sgwe31": "Strategie", "wegasqASDE32446sgwe32": "Schnellste Route", "wegasqASDE32446sgwe33": "Kürzeste Route", "wegasqASDE32446sgwe34": "Hohe Geschwindigkeit vermeiden", "wegasqASDE32446sgwe35": "Allgemein", "wegasqASDE32446sgwe36": "Neu navigieren", "wegasqASDE32446sgwe37": "Navigationsergebnisse", "wegasqASDE32446sgwe38": "Wegpunkte", "wegasqASDE32446sgwe39": "Gesamtkilometerstand", "wegasqASDE32446sgwe40": "Bitte Anfangs- oder Endpunkt markieren", "wegasqASDE32446sgwe41": "<PERSON><PERSON><PERSON> s<PERSON>iche<PERSON>", "wegasqASDE32446sgwe42": "K<PERSON><PERSON> und ziehen Sie den ausgewählten Bereich", "UVefl_f1h0ZTPQmWvaySJ": "<PERSON><PERSON><PERSON><PERSON>, ob der Leitungsalert nur während dieser Zeit ausgelöst werden soll", "pdMyZPIg2I7k3u3ByYLrU": "Wenn die Position des Fahrzeugs vom effektiven Bereich der eingestellten festen Linie abweicht, wird ein Linienabweichungsalert generiert.", "HhZrDdgis2Hm5-sP9EurB": "Leitungsname", "-YUdN9jL-AktN-FaK-AY6": "Unterstützte Geräteelemente", "t47OvSwHgN1jrekCjlFgG": "Protokoll", "L0DK_-dPWIgIwVk29nx2U": "Ort", "OETskCr0ggPlXNLTOD8cX": "<PERSON>te wenden <PERSON> sich an den Verkäufer, um das Passwort zu ändern", "cqY_Nktaga5MCpKzhZlzJ": "Server", "GCZMAUxDiNQjGiQlcWsgx": "Bilder", "HbdiV2RLW3mpJDkZ_KYFG": "Artikel-Nr.", "P6hV_PyvDRj6HaDyYdaU0": "<PERSON><PERSON><PERSON>waltung", "2S0dsK4yqAlGMmJh4k9PL": "<PERSON><PERSON> nicht leer sein", "WnpPKREGrpW2LdDnNXIzi": "<PERSON><PERSON> zu groß", "B7o8-BkLXrQP-C2VC1vRu": "Erfolgreich hochgeladen", "PzBG2cs4d9h1G-yy4y-JY": "Hochladen fehlgeschlagen", "6e8OYOZeRaCVXbbzz93ij": "protokollieren...", "oo6OKdBR0WdWtwR9ww0BI": "Finden Sie sofort die globale Ortungsdienstplattform", "J_O90-QzEYhF0wwpP_1OG": "Client-Downloads", "0no1-wPECvW4oaBpA3PUH": "Autofinanzierung", "gMBUxLUUq5HkqYp-IZMMw": "Der Benutzername darf nicht leer sein", "SlzCfc3ZLA2qtLCdh9rQ0": "Das Passwort darf nicht leer sein", "LoUsU3fXwcYRpNz6nk2Mb": "Benutzername oder Passwort falsch", "EvTOrBlsYbI6MLRmnjEby": "Vereinfachtes Chinesisch", "Q_JRX2AWfBfpvq0i7WSSf": "Basisplattform", "sfwA5_FWQER4kGGM23f01": "Gesamtmenge", "sfwA5_FWQER4kGGM23f02": "Unterstütztes Gerät", "sfwA5_FWQER4kGGM23f03": "<PERSON><PERSON><PERSON>", "sfwA5_FWQER4kGGM23f04": "Gesammelte Aktivierung in den letzten {0} Tagen", "sfwA5_FWQER4kGGM23f05": "Gesammelte Aktivierung in den letzten Jahren", "sfwA5_FWQER4kGGM23f06": "<PERSON>s können maximal {0} ausgewählt werden", "sfwA5_FWQER4kGGM23f07": "Gerätestatistik", "sfwA5_FWQER4kGGM23f08": "<PERSON><PERSON>", "sfwA5_FWQER4kGGM23f09": "Geräteanalyse", "sfwA5_FWQER4kGGM23f10": "Aktivieren", "sfwA5_FWQER4kGGM23f11": "Modellstatistik", "sfwA5_FWQER4kGGM23f12": "Alertstatistik", "sfwA5_FWQER4kGGM23f13": "Feldsatz", "sfwA5_FWQER4kGGM23f14": "Kundenstatistik", "sfwA5_FWQER4kGGM23f15": "Wachstumsbetrag", "sfwA5_FWQER4kGGM23f16": "Lebendigkeit", "sfwA5_FWQER4kGGM23f17": "Gesamtzahl untergeordneter Kunden (ohne mit IMEI angemeldete Benutzer)", "sfwA5_FWQER4kGGM23f18": "Monatliche Aktivität, <PERSON><PERSON><PERSON> der Kunden, die sich in diesem Monat angemeldet haben (Deduplizierung, nicht eingeschlossene Benutzer melden sich mit IMEI an)", "sfwA5_FWQER4kGGM23f19": "m", "sfwA5_FWQER4kGGM23f20": "<PERSON><PERSON><PERSON>", "sfwA5_FWQER4kGGM23f21": "Einheit: Zehntausend", "6CM5WFkktUM9AyRxu4XAm": "Der Dienstablauf umfasst den Plattformablauf und den Benutzerablauf", "HqKkLvYhwnihOKgZ5D5gq": "Dienst abgelaufen", "Ja6QviJTdBDcOxl2ngWXG": "Batch-Abfrage", "_HKPHYTEishptFq7vFBk1": "Aufstellen", "iLDn-CSjPBQCrfCiJcKgp": "<PERSON><PERSON><PERSON> Si<PERSON> eine Datenzeile ein", "VLNvCttQNLcWxHSN-fPOl": "Eine einzelne Abfrage überschreitet nicht 1000 Datenelemente", "k7qAMHi43Xjw2PYIQ38hq": "Der maximale Zeitraum darf eine Woche nicht überschreiten", "jaGWdGPfszdjZvW57NUiJ": "Das eingegebene Zeitintervall darf 7 Tage nicht überschreiten", "pvqy81ufHUkt_cJ8QuDoR": "Passwort bestätigen", "453T_t2i6SJoDBBAy_R6B": "<PERSON><PERSON><PERSON> zum Login", "dJBhX5e3-fFcp5QbouP5w": "<PERSON>ses <PERSON> ist nicht an eine E-Mail-Adresse gebunden, daher kann keine E-Mail-Verifizierung durchgeführt werden!", "jDSJ8u2HnhtninUCX6vMB": "Passwort wurde aktualisiert", "qsvd2Se3tlv5Dsb9Hya01": "Einklappen", "qsvd2Se3tlv5Dsb9Hya02": "Entfalten", "QBfoK7OejhnKA31m5cUb_": "Zielgerät", "dBc3sWmATHCVdJB5RtzRA": "IMEI/Fahrzeugname", "asdfwemATHCVdJB5Rtz01": "Unterstützt nur die Übertragung der Punktekarte des aktuell eingeloggten Kontos an den Zielkunden", "asdfwemATHCVdJB5Rtz02": "Punktekarten für Zielkunden neu generieren", "asdfwemATHCVdJB5Rtz03": "Punktekarten des Zielkunden recyceln", "asdfwemATHCVdJB5Rtz04": "Das Kartenguthaben des aktuell eingeloggten Kontos", "swefqrerggARxHSNAfP01": "Positions-Upload-Intervall", "swefqrerggARxHSNAfP02": "Datums-Upload-Intervall", "swefqrerggARxHSNAfP03": "Heartbeat-Intervall", "swefqrerggARxHSNAfP04": "Müdigkeitsfahrzeitbegrenzung", "asdWdGPfszdjZvWs765sg": "Satellitenortung+WLAN", "uAtFq7CLH25vyR_9qCWOx": "Vermögensanalyse", "owditLvP75QFCxudedrBl": "Bitte geben Sie die Offline-Dauer ein", "OZTi4YTKLZz8u6-YnTnG8": "Die Abfrage der Offlinezeit darf 1000 Tage nicht überschreiten", "PopB3vD--FyqFAitPbG-9": "<PERSON><PERSON> geschlossen, die aktuelle Geschwindigkeit des Geräts in den hochgeladenen Positionsdaten <= {0}/h, bestimmt die Plattform den Zustand als stationär", "iwdChV3IahUyambbg9AFn": "Wenn diese Option aktiviert ist und die aktuelle Geschwindigkeit des Geräts in den hochgeladenen Positionierungsdaten <= der statische Schwellenwert ist, bestimmt die Plattform, dass der Zustand statisch ist", "u7_yN4XM3aiplwBPPyKQJ": "Vibrationsalert aktivieren", "HH3gLagydJHVlK2reit8q": "Vib<PERSON><PERSON><PERSON> auss<PERSON>", "PxYMk1w1YywfN127GyF_L": "Zustand des Autos", "avn4Z2zY-8AmiFArM_Kzi": "Reichweite", "1FnxxFoT2tdJpyAUbkQ8d": "Ausrüstung", "vZ_NmsWGw1TljsTvgtYSc": "Lades<PERSON><PERSON>", "gmiIB_fgQPFeHSPgUZ1OX": "Unwirksam", "sjqasBHO4-7v_oE_oQhzf": "<PERSON><PERSON>", "rr1RNSaQZHRkg3YmwbXN3": "Wechselstrom wird geladen", "3NqztzkemfaYubkgd94tg": "Laden mit Gleichstrom läuft", "gW9Sr4E4DO3vcWEXwSANp": "Aufladen abgeschlossen", "C3mHbV6ctjTFoPxWohWcP": "<PERSON><PERSON><PERSON> die Fußbremse", "eW5vpowqQzG4FDKqffwhd": "Fußbremse treten", "Y8SpiwQuS5O9oxoBoy2kL": "Fußhandbremse lösen", "E3uUXB0kIhb1zEAcQGLUI": "Handbremse anziehen", "RDwtFG4WvXrnr1gJn4FEf": "Zeit zu erhalten", "jAwo7Dt5bMPQddJLedM5N": "FIN", "xWvddkQeFsyOfJIBomA-W": "Fahrzeugleistung", "NmYX0e6R4ef5q9at8zVBm": "Gesamtbatteriespannung", "bgQGnhJJGfoSv7R2bHoep": "Lade- und Entladeleistung", "IFI8p5520lb5gVh9NKX4U": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "QVF1XS6JNRbW6dAeOGSpE": "Motortemperatur", "_HAw3HjMpxf_5Li2ijxzG": "Fußbremsstatus", "8BSkuJhUJVmpC-FyYv_tP": "Handbremse stu=atus", "dPrshwoE0R9HYtR_pqiyB": "Ausrüstung", "GFHU5hpHTATv03VLlUraj": "Informationen zum Fahrzeugzustand", "asdf2353WEEEW34463E01": "Lange Zeit keine Operation, es wurde automatisch geschlossen!", "asdf2353WEEEW34463E03": "Erfassungszentrum", "asdf2353WEEEW34463E04": "Live-Video", "asdf2353WEEEW34463E05": "<PERSON>s gibt kein Video zum Abspielen, bitte wählen Si<PERSON> zu<PERSON>t das Gerät aus.", "asdf2353WEEEW34463E06": "Das Gerät antwortet nicht, bitte überprüfen Sie, ob das Gerät online ist.", "asdf2353WEEEW34463E07": "<PERSON><PERSON><PERSON> get<PERSON>, bitte neu verbinden.", "asdf2353WEEEW34463E08": "Warten auf Timeout der Geräteverbindung, Warteschlange entfernen", "asdf2353WEEEW34463E09": "TF-Karte", "asdf2353WEEEW34463E10": "Kann nicht normal aufnehmen, schlagen <PERSON> vor, die TF-Karte zu formatieren und versuchen Si<PERSON> es erneut.", "asdf2353WEEEW34463E11": "Kontakt fehlgeschlagen.", "asdf2353WEEEW34463E12": "Haben {0} Minuten gespielt, automatisch beendet.", "KqFWT6QtVOAQ7hc3cr84X": "Bitte wählen Si<PERSON> ein Gerät aus oder suchen Si<PERSON> danach", "QK_2GJFOQAXQpT2t--zKK": "Bildname", "yC-UA-x1iWn-_8MWhMRUJ": "Vorschau", "UTh6HJ0cZkBaGYhmxiqzi": "<PERSON>te wählen Si<PERSON> ein Datum aus.", "hDsjEhjGMV2q1OhZLj51s": "Mit Video", "BVvJFDI4tb81rTca8ggnz": "<PERSON><PERSON>", "nuxEh7ICQm1xi9uibTsEn": "<PERSON>hr Browser unterstützt kein <PERSON>, bitte aktualisieren Sie Ihren Browser", "n4CXnjUAtGwof4KxtLIs7": "Erfassen", "rbxQKc5qyK61DZUKq4R2J": "Erfasste Capture-Bilder können in den 'Capture Statistics' angezeigt werden", "G4YqkP4TlCeqrV4kaShwj": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DF5rkRH7xPSGfwVqhIZpx": "<PERSON><PERSON><PERSON> zu", "CKBlMeR7SBptjZx9In-Di": "Das Gerät ist offline, bitte versuchen Sie es später erneut", "9Gz9xO-UIA7OhYqouSYlk": "Gerät nicht aktiviert.", "bREKx_fI1e13-UqxZSRal": "Das Gerät ist offline.", "xb_MxCHFkbfiDQK87uTTx": "Das Gerät antwortet nicht, bitte versuchen Sie es später erneut.", "K0DCR9FMFQUUR12RWd7nJ": "Die Abfrage war erfolgreich, bitte wählen Sie die Wiedergabezeit aus.", "Ai9BqK4KDDGgpUlIM0L3Z": "<PERSON><PERSON> an diesem Datum.", "15zeVzw38Aza0pV2JpH8a": "Erfolgreiche Ausführung, Fotos können im 'Capture Center' angesehen werden.", "2UV-WqfP4CJZmmLPAGuny": "<PERSON><PERSON><PERSON><PERSON><PERSON> endet, das Gerät wird getrennt.", "R4xVH6Gojtza6L_bDXWmZ": "Zeitüberschreitung beim Videoabruf, bitte versuchen Sie es später erneut", "RYTm5aEvo7HeIs2bpdEIN": "Vollbild", "o9T1vRXeXagCVmJ2L7HXh": "<PERSON><PERSON><PERSON><PERSON> beenden", "ixY_KDMtIVUvWecVMQTWg": "Lautstärke", "P0UwTpyNrAmeEFn-ZtpL8": "Slience", "0_iZ3QPJnrJ7wWOLfcFt4": "<PERSON><PERSON><PERSON>", "xN4IbwPtNSUXm1NMMrCJr": "Pause", "gDT1meCgWso2DTOwunb0f": "Das Gerät ist offline", "8QHejHRn_K6ApxX1HI_x2": "Bitte Kundennamen auswählen oder eingeben", "sEc-d5mjOt11-Bl13FHEi": "<PERSON>te wählen oder geben Si<PERSON> einen Gerätenamen ein", "hoaQubz4oNzQmOYis2iw1": "Legen Sie das Wiedergabedatum basierend auf der Zeitzone fest, in der sich das Gerät befindet, um zu verhindern, dass das Wiedergabedatum nicht mit dem Abfragedatum übereinstimmt. (Kann Befehle senden, um die Zeitzone des Geräts zu bestimmen.)", "-LMYZ7kqXRKWwUyYD7GOF": "Automatische Erfassung", "l4A6SV00MzaTtBzrPfqK9": "Fotointervall unterstützt nur {0}-{1} Minuten", "7x1-CyvyQU47Ks-9Mscic": "Bitte geben Sie das Fotointervall ein", "FlBzcCTYSxWGdVm3pcxQP": "Relativ niedrig", "wyDuKJCKh-KmMMTnY_bBB": "Nr. {0}", "94nNNXBShqMfNNc3dNGHx": "Datumslimit", "xAyYnNoSfX2pbPVT9h1pC": "Fotointervall", "8-ai5B94OPd5_wwtjQyZu": "Automatische Fotoaufnahme nur ausführen, wenn ACC eingeschaltet ist", "IXsWeOfIA5805yY47UJGz": "Bildqualität", "wf2tZAJzkmSHhwNlwULgH": "<PERSON><PERSON><PERSON>", "_Vr87IrKGdP_Z0Ir0boqS": "Bitte geben Sie das Datum des Fotos ein", "d_0qVpLU434hyMGjCIT_p": "Zeitlimit", "IYUaUWFilxVZo9ESIhEy7": "Automatischen Aufnahmemodus des Geräts abbrechen", "r2u1VZH0-LuLyUjjtQp20": "Automatische Aufnahme abbrechen", "alzrqMaGjzgsumqGYvejz": "Statistik erfassen", "F7bv-5zI8V1rO9ndXVOoV": "Zahlungsfrist", "uOMU5cwmKPl8Qugv8cZpi": "Gerätedienstanbieter", "LvOf_dbnglLtOVFdtEwov": "<PERSON><PERSON><PERSON><PERSON>", "lYoEvNPHjrc6x3Ct4fh1n": "Zahlungsmethode", "qzwEjrvjWERzbbwcvI5rK": "Pakettyp", "SY_o_6OPrbX3-RA03j02e": "Aufladen", "AnLVb2XOdBa6NjOeYULb7": "Aufzeichnungspaket", "WZK3OsRZ4KO9I6X8u1mLu": "Wechat", "Xl9OJ9sjrHo_ukN8urbT6": "Alipay", "HSHNrVZVhYcCKzEXB2O_A": "<PERSON><PERSON><PERSON>", "O3QtNyUozWoUEwHSnRrzJ": "<PERSON><PERSON>", "ufxwjQ0al34EVQsfU2cQz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "9IGNKCfQfiGSnZ0ojOGrl": "Bestellnummer", "a9z-zLegU7yCzTuKkNtOC": "Zahlungsauftragsnummer", "wyF5AfveJkdrdwRnGU330": "Bestellnummer", "uRcRfIURR21OtZb2XaJIz": "Auftragserstellungszeit", "uigvo2391weT6pPqyfxYg": "Paketname", "L8rvTj6RbNqMjBCUHrP4w": "Betrag", "Vho8Z0XiLBfL9q77Vbq7X": "Seriennummer", "LTht5hSzFVGNGPsD-aWdO": "Paketdetails", "XuMA6V1Wxn2UZv7ETqtnL": "Mehrwertdienst", "66mGM-OMMfoNzSaI85F5O": "<PERSON><PERSON>", "UOIe7iu7who6AF5Ea9nuE": "Pakettyp", "QIJcJzpIK9rdXE4EIl86s": "Benutzerdefinierte Zeit", "FEbdGHU9DzOupQt0oPGxr": "Gemeinsames Gerät", "vCx8UWyPG3dwdwKY7yN7I": "Basispaket", "yFydQode-qpYNF6prCBa8": "Kostenpflichtiges Paket", "PxiKJqRk2NZgR5rrcfbxV": "Bestellin<PERSON>en", "tnVXZ-wvzIO2qu-BR6Raa": "Gesamtbetrag", "xhBOcDdGN68_xdqLGD_AK": "Bestellnummer", "w6f4z-ArVpDgonjSek29T": "Zahlungsfrist", "ZolzOIth0AcW8J0-AYx8m": "Zahlungsmethode", "i9LCQ9tWnpPpxkzaSPAkE": "Bestellmenge", "amountReceivable": "Betrag Von der bank", "jq-noYxqRQlR1lpP_fKaN": "Zahlungsbetrag", "BKMFu69paMH0rP0zUnz5x": "Verlängerungspaket", "FBmAMXGiheGz_xtB50egU": "Paketgültigkeit", "fifkznhs7I8Fg-SaLDuGt": "Gültig bis", "VFMYa1yNoRQsy2FXnykHa": "Paketinformationen", "Fop3dk-nX2QIqUnQMVv0G": "Pakettyp", "UzzQQv1w_yEq7V-Ar-RNi": "<PERSON><PERSON>", "ocMzF-bOfCsBqkM2MeBHt": "Anti-Diebstahl-Aufzeichnungsdienst", "9Z_0oEj77LZJQDoY7KtCa": "Tracking-<PERSON><PERSON>", "jSK9Ol1Oa90s46x2ts1w_": "Positionierungszeit (Upload-Intervall)", "ryqVCbe_EynBQLBPkT6ug": "Elektrozaundienst", "W7OC5YBgi9SJwShHGe-eO": "Statistikdienst", "ZlYns25c8Tujl-vP1nuEn": "Plattformalertdienst", "rijikLyLIhPpZYsAQZHpb": "<PERSON><PERSON>", "883ApjJAhE8Rjotd5jXrT": "Paket-ID", "jyVu4QMd46VDBzTPBAuAT": "Paketkategorie", "84HZrvlRdBMYzNbvpub__": "Paketebene", "dUxii2qTpMb8i9Lfp0kEo": "Paketinhaltsparameter", "8NNHQ_xsjOjWx2o4u9cKW": "Paketpreis", "hGZVKjFNvq1TAvLwxkAgq": "Paketmonat", "_CXEjvnybYTxiZPXdJknx": "Paketstatus", "cCQXQbp5AVce12lU6a937": "Paketinhalt", "nsqSz47S_fmKY2einyuul": "Diensttyp", "uRDsAdnGtIGFDejyo0j0w": "Hierarchisch", "QGJEx6AV2WGlBkvg2VPIG": "Überlagerungstyp", "z7PZM_aLZkhX-rRa1FZ0l": "Standardausgabe", "KoVO0P4i-qT6lUsN12fFK": "Member Edition", "ZSaRleSUdarb4RX1awLBT": "Platinum Edition", "sQ6JBdba8e6qBU57-Ik_h": "Permanent", "UGsdURGj2-A8w_d-tIoPp": "<PERSON><PERSON>", "k2Xz_evCamP4Y6Am33-Xj": "Unbezahlt aktiviert", "Z4hoIjz3ox3rSFufsljh3": "Erfahrungszeitraum", "2We31Z8Jl4hCBRM_pd5BT": "Erfahrung läuft ab", "yrpPHud6Y6vUqEW1LzSUh": "Nicht aktiviert", "mgwsOBjaQLIs7QogPPWZP": "Mitgliedschaftspaketdienst", "mp6yVv2_LUo1Lr6d2St01": "Geb<PERSON><PERSON>lt", "_ik2yJTjlR7Rdqa0Bwweq": "Anti-Diebstahl-Aufzeichnung ({0} Minuten)", "M1I3R0AnDHCWu1mZ1OsVP": "Verfolgen (innerhalb von {0} Tagen verfolgen)", "Nw2KKVqus1cw69F_aMpji": "Positionierungszeit ({0} Sekunden - Positionierung)", "l7rj88qG_-sQlNjsAGFZ_": "Normale Ausgabe", "uiY_pCNl4iy2jsY8bSHQv": "Gold-Edition", "WkEFol678sA7CJ17KAlFb": "Grundlegende Dienste", "3nuuynf0P_SrOx0JqRKPW": "Stapelsuche", "70ERCDxsXgDD3RuW2XD6q": "Herzschlagzeit", "D4fkMsn4E25Wnmp2AAm_l": "<PERSON><PERSON> er<PERSON>", "OD6TR38aI88Bu7uB8uTwq": "Erkennung fehlgeschlagen", "BHbhSwWp8seqYBF5anqUk": "<PERSON><PERSON><PERSON><PERSON>ng bestanden", "tT2sRSRlwvHp-8CBIm7bt": "GPS-Zeit", "N0OhAZYIH0g9tcRa8qeD4": "Bitte 15-stellige IMEI eingeben", "W9AT_wWgNGQiHIiUa0xzP": "Unterstützt nur Eingabe {0}-{1}.", "sa8NRVeTVfugiuRw61wuE": "Weckzeit", "M_TMom29kkT90sW_GtNrB": "Tracking-Zeit", "AKhshdLe4CcX5kgQ6wQbA": "Zeiterfassungsmodus", "BLlJDrVJyhhSzQrSOxyh5": "Geschichte", "3xEp9e-_z1918x8ohGTow": "Auswählen", "sAgQ82QEkN8XhiaQYF-FX": "um den Startpunkt auf der Karte zu markieren,", "x5p9J9L802EOSHMvXgfyi": "um den Endpunkt auf der Karte zu markieren.", "NC0GvIUNGMIckA487qi_W": "Kontostand", "xsV0NNfyQJJhMNnQpfXJr": "Details des Sachkontos", "OQQDG7tWbK4xJ_SxAn7mM": "Dienstanbieterverwaltung", "-qO1k-kyMJ78r-k7hvTFh": "NEIN. ", "RugUrOHMBSp8lZCLkCZOT": "Bitte geben Sie die Hauptbuchnummer ein", "tf9xHu3qD81AQzCR4i2Bh": "Höhe der Einnahmen und Ausgaben", "f8gnN4SjblDXQUgvZtnei": "Bitte geben Sie die IMEI-Nummer des Geräts ein", "lcNVANxOy5jOngZ8n6BSU": "Hauptbuchnummer", "6zhGiJhw5vrsMXx3jSew5": "<PERSON><PERSON><PERSON> aufladen", "y8ZOkzHwx2mVGslRNjFSo": "Aktuelle Einnahmen", "K5b1Or2vbenyxUOLm6DzC": "Neue Dienstanbieter", "xgzOVj5OyHFE7FtwzEISW": "Dienstanbieter-Splitting zu<PERSON>t", "vyx_f6IfvRThEn7vSa7uh": "Dienstanbieterebene", "UBGf6jO1VlyC5cZG6G_Vv": "Übergeordneter Dienstanbieter", "KgSLgX2QXc5P4ebGJhoCr": "Abrechnungsteilungsverhältnis", "5TuLCfhzC3c4Fhk9bFXFe": "Kumu<PERSON><PERSON>", "jG2Um06j_7e9GgAO6u6x6": "Kontostand", "S3i2lNWXrpS54EiZ_zVr5": "Buchungsbetrag", "fkWmt4XVNoCEOqcqM53Vu": "Gesamtaufladung", "llSrRUSNQCGG3Tzw4CSoM": "Bitte wählen Sie den Dienstanbieter aus", "JNNF3uGWW4u7fYgJtmHcL": "Dienstanbieter hinzufügen", "-bWCf0xmzSKqssy9O6C-8": "Beschreibung: Aktuell zuweisbares Verhältnis {0} %, Dienstanbieter: {1} %.", "cQCrS5O1FtY3f7CeXf9Ul": "Betrag der Bestellung nach Abzug der Bearbeitungsgebühr.", "XXlgxn8xHhDCvDo0nhRaI": "Bitte wählen Sie einen Händler-Benutzer aus", "SnUXBvZwZ_rci24Ivvgrf": "HD-Aufnahme", "rYEVE8FCA2SXW955yf98B": "Diebstahlsicherung", "QNLyVdYTL-D09KKJszyHr": "Mitgliederpaket", "k7tmRHnc8k29NMS7xFv1O": "Kraftstoff- und Stromabschaltung verlassen", "GYlfTrb-v_i175rZ_TsnY": "Haftungsausschluss", "nlPyvAgAv_1BNyr-98ope": "Das Abschalten von Kraftstoff und Strom kann zu Unfällen führen, bitte verwenden Sie die Funktion vernünftigerweise unter den Bedingungen, die den Sicherheitsnormen entsprechen! Alle Folgen gehen zu Lasten des Benutzers!", "YELMd4qb9ZvpmW11-5PX1": "Konfigurationselement", "RsEwptNDs5Yk1gsTLvob2": "Ob Untergebene eingeschlossen werden sollen", "Z_54Oo1YxTWx_x4DiIaBC": "Zeit hinzufügen", "dDAKOU8dHan4D9HlS0Qwu": "Aus dem Zaun Strom und Kraftstoff aus", "mL45YNy1DNVrfOdSVUscD": "Konfiguration", "9m4QL0-j7BiH2hLI--u94": "Ich stimme zu", "vSvvdtZaIL_oUd7hfGj-b": "Paketänderung", "UCE4NbQTRf2L6QFdVr56w": "Geräteimport", "ULguhh46biHAPnWL1-7As": "Datensatz ändern", "ZeQ1BBuCptskLmr-Jj8QS": "Aufzeichnungs-Overlay-Paket", "hHj3x3eBVUf2fJQt7aHYK": "<PERSON><PERSON>", "PSBfdFXVla4Vk5sigF3Ct": "Formular-Upload", "urtoz-lPdO_PHQIlaz5Zs": "<PERSON><PERSON> klicken", "1926HGNuX84IOWFR5BjL5": "Nur XLS-, XLSX-, CSV-Dateien können hochgeladen werden und nicht mehr als 3 MB.", "C1iN37-gSdpTsqWkrHJ9e": "Konto importieren", "A_9Rad7zVPj2GBmUGpEVZ": "Bitte Paketinhalt auswählen.", "y6fr8Bq1SUa2Y4t3QsKh4": "<PERSON>te geben Si<PERSON> die Chargennummer ein.", "RVpUIj1EBBJ1awwP0KoTR": "Bitte geben Sie das Betriebskonto ein.", "Q01UqFAPK5CgQBT6Va9Ur": "Leistungsmodus", "x76HSqPas266y9PL13L2c": "<PERSON><PERSON>", "IudF8SqtFP2jmoBEgx5RC": "Energiesparmodus", "_VJb7sn4Wp1sg_MpUMqqx": "<PERSON><PERSON><PERSON><PERSON> auss<PERSON>", "E19JqAWWpVfDPfQNztbi6": "Kanalnummer", "76VJ_0CYltD6DDiTNyZxf": "Bild", "8j-cIxRn0mxuGcQu-J6U-": "Video", "J-XTISs_rbWSjK4D-RUnI": "<PERSON><PERSON><PERSON><PERSON>", "P3xgcENq9CpwWHOJDCodc": "IMEI/Gerätename", "jyUQCyhg9_HqCbJnmTNj4": "Ereignisname", "4IqR47LAiISMhbMm7IhgP": "Die Ausführung war erfolgreich, bitte sehen <PERSON> sich das Bild an.", "RHSs8Xwa_q8HNTZN6MyTw": "ausgewählt:{0}", "0D8w0x3Z3-4C-ei6GzRO2": "<PERSON>ird ho<PERSON>, bitte versuchen Sie es später erneut.", "jpXZlwrJaiDcEaQiwy10A": "Wählen Sie mindestens eine Kanalnummer aus", "NowXQsGP0xEGBs1WjE9_Q": "<PERSON><PERSON> doppelten Dienstanbieter auswählen", "cQosMBRHsqt_530cSmbwx": "Das eingegebene Zeitintervall darf {0} Monate nicht überschreiten!", "xOwwQNdPzsClyM2sZEHsx": "Bitte wählen Sie das Gerät aus und überprüfen Sie das Datum", "5x5ttmnhGQm_xfx8-fUMW": "Die Abfrage war erfolgreich, bitte wählen Sie den Wiedergabekanal und die Zeit aus.", "IKa5v1S4C9m3JqKSNCGDm": "Der Video- und Audiokanal ist belegt, kann nicht bedient werden.", "PeLZGwiFPA7OD53c77fpj": "<PERSON><PERSON><PERSON>, auf {0} zu hören?", "xEKN7mlzVzK9GQt_b-Min": "Überwachung beenden", "TE2fBaGlpdBY0lKWjOTKq": "Überwachung starten", "5D4MnWs_OiIq-I_QsGAyX": "<PERSON><PERSON>", "VgbN0QpFxOoOVebTMifEb": "Verbinde......", "-rwIcMW4enfFNVkLmJOOF": "Überwachung:", "Z2KhLcz1RQv-0BG_4cUod": "Verbindung fehlgeschlagen, bitte versuchen Sie es erneut", "-uah4lHNi9qpmlWruM9Tm": "Überwachung beenden", "SW8Lee_dtDjszssgsNc4W": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZrCxLSKRGicXpczHTBMUR": "Bitte wählen Sie den Überwachungskanal aus.", "Mwu8g570bWTiEqGKSZBKM": "{0}Video ist geschlossen.", "6zg6_aIDxHLfIYIsnh7wJ": "<PERSON><PERSON> kein <PERSON>", "_SP67dHXDs5attlodtzd4": "Nachdem Sie den Kanal ausgewählt haben, klicken Sie zum Abspielen auf die Wiedergabeschaltfläche", "-iSrrSkshPlUsGVHwnJlz": "Sind <PERSON> sic<PERSON>, dass Sie das Gerät sofort orten möchten?", "BCInOUOWDZqvRtmRgoaIa": "Echtzeitmodus", "rksCtLkboMzqxSXtG_Njp": "Ultra-langer Standby-Modus", "_d7RyxRhjvrOS12pZe1Rv": "Sprachübertragung", "Y-ouNNO1zuT0O25-wmQC2": "<PERSON><PERSON><PERSON>len nicht mehr als {0} Wörter.", "BgThYyBBQxKdbvTTxaqEu": "Sind Si<PERSON> sicher, die Sprachansage zu senden?", "-oHWPszUGv_bgmY41k0OM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rcVq-K-FhY0x_XGWgWQyH": "Absolutdruck im Ansaugkrümmer.", "WB2u4Gtzp_x_qB5aUdVA-": "Restölvolumen", "D5xhxtuMiaCdB6ZtyfA-I": "Zylinder 1 Frühzündungswinkel", "wiVXwMdYifHIIDTaYy7Uk": "Umgebungstemperatur", "sxKO1EpE7584f885OstmP": "Atmosphärendruck", "hVS2wfr7_gPvVf2ZXdUov": "Ansauglufttemperatur", "nIvuguTEb64P4DeUO6Kyx": "Motorkühlmitteltemperatur", "wL1nAP2XF8cqzzAea1l-M": "Langfristige Kraftstoffkorrektur", "ir8dRcv6tPQQT3b4fDUhJ": "Berechneter Motorlastwert", "pmSiApNt_l-CLgU_V9-EN": "Absolute Gaspedalstellung D", "Jvzgr6i4iCYe861FOePqW": "<PERSON><PERSON><PERSON>", "vOB_ErOfnohph6BFBeJa8": "Fehlercodestatus", "OGYkaAzV3zLdQVPWtr3aM": "<PERSON>cht beleuchtet", "6l5PUoCfEha4agcOU7c3c": "Aufleuchten", "VXAStgxYyFeoijgMgkPGh": "Volume Seeking Pet", "XqVd3m4oejVwBud-SvnxK": "Lichtsuchendes Haustier", "2247BJLuV1ixtjGA1O7gV": "<PERSON>utre<PERSON>nder <PERSON>", "vF3kO35MlJqcY7R7BojED": "Allgemeines Paket", "Bpdy-V0IcCvHQ_Agega0T": "<PERSON><PERSON><PERSON>", "1W2i1KY0Bfz_8olXlC2d6": "Agentenpreis (Yuan)", "zUwkOXwA9wIf2S0kzBT7x": "Verkaufspreis (Yuan)", "pWM8WfOGzZzcyBJMF4w4Q": "Verkaufsrabatt", "-sHvr4dqzhBg8zhkGisc4": "Grundlegende Dienstklasse", "SlQAbSeLcvKG64T_WCQ4v": "Kostenlose Geschenkklasse", "WYZoPhp_aCouZhtTNRx_v": "Plattformdienste", "ulJ0IYNw9yhO3xtOejMkq": "Plattformdienste", "BxnsRGI8yOGF3AIgmEDPA": "Anti-Diebstahl-Aufzeichnung", "S5BLPa5cR1lfkPtX8C9ra": "HD-Aufnahme", "cu1pVC4m4_ExdeETrqlaH": "Nach oben", "_7CU8hvXPxWIHZ2Hw09lG": "<PERSON>ter", "wgxerTVV3ubJSwGQbIRXK": "Richtpreis", "eGPJZha0zDcNjnOUxMl5f": "Verkaufspreis", "hqZrtrvlMrwFhVBJDoK9d": "Richtpreis (Yuan)", "biaeNmEm5PyRHVooq6ijB": "Neuer Agent", "woHbo-mjkIJGvCCXfPGdW": "Agent bear<PERSON>ten", "krRoZPXODjW95IxZY3Jxr": "Agentenpreis", "EoOkU9Au2oolyDpxoABEO": "Paketbeschreibung", "0gTRzqUCLGERAfFhL-8y3": "Standardpaket", "iqmW63hnN6IZmBcr4Bx_N": "Alle Geräte", "rY_cnjuM0KhLnfuxRdY7Y": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VLVXHDOnSopPJux0MHDsU": "kabelgebundenes Gerät", "Cc1uwqoUoGjbyyWsaRu8q": "<PERSON><PERSON> kop<PERSON>en", "vl7XPni0Dn5MHXeBkgUvR": "Einstandspreis", "Oe4zmx4CPyMN2Y_uT0kXj": "<PERSON><PERSON><PERSON>", "hjX3McS9BgQRZc-zoimJw": "Bedienungstipps: Nach dem Zurücksetzen werden die Testdaten wie Aktivierungszeit und Track des Geräts gelöscht, und der Gerätestatus wird auf nicht online oder offline aktiviert zurückgesetzt", "0rxp9_tAb1nkVwt7ZfEG9": "Paketeigenschaften", "aoalRCCBuSPYz5q4mvZI7": "<PERSON><PERSON><PERSON><PERSON>", "jDJyz2pFt82shF84u9X7N": "<PERSON><PERSON><PERSON><PERSON>", "V0p40frI_DrTg5_KB9uQB": "Dienstgültigkeit", "F3tP2-szQjwBbJ2_Wgdky": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbcDEN7NWWYIeiIQTs0Ps": "<PERSON><PERSON><PERSON>", "j-09UjLIz8t7ucYKWSTdZ": "Bestätigen Sie, die Gerätenummer zu lösen?", "ovK_RRKbTaBSEHhgL_kRy": "Mehrwertfunktionen", "mMGDudnh3-MYiKN3ZmVZQ": "G<PERSON>lt<PERSON>keitsdauer", "iIRxRd4XquTPvNwkbbPAt": "Gültige Regeln", "YEsjuadygPpzDlYbOQdfC": "<PERSON><PERSON>", "ygl1CCzlGB6pYQi5oJgMg": "Effektiver Typ", "eIhE-MpijfPx1_YivOWfn": "<PERSON><PERSON><PERSON>", "N5usFqbTBj_x-AGRsPR8s": "Kostenabgrenzung von Mehrwertdiensten", "a9_Kks1xUEIPs5523_ebG": "Paketergänzungen", "CTqygomw_FwACb306Vgef": "<PERSON><PERSON> bear<PERSON>ten", "j_nyP6IyvoRo9kejtwukP": "Yuan", "ZVF3com1sFG-XrZC-livm": "Dienstkategorie verfolgen", "F6SGMDU-K-qsjW7cV9B-3": "Aktivierung des Plattformdienstes", "POuxG3He6OPD_8Jxozz0m": "Betriebsstunden der Bühne", "KeHIZrek2q3vBPe4Cp8Da": "Aufzeichnungsstunden der Diebstahlsicherung", "QKAK9kLAa86Xy4nOgZf-G": "HD-Aufnahmestunden", "iPiBIJga70QxIIRCBldDv": "Gültig im selben Monat", "FXJLzQWH0isx1K681LHvq": "Gültig ab nächsten Monat", "tf0jk5t-3-we9pamkVaB8": "Einstandspreis (Yuan)", "o7LB26olpIL6MYrh3AEkB": "<PERSON><PERSON>", "Lf5yEIWEaqmGp-MdVGn6K": "Bitte geben Sie den Verkaufspreis ein", "_0RCRKXXXIzu2xxfKtnTl": "Bitte Verkaufsrabatt eingeben", "ikXI1IxXKIQp8u1h9AA07": "Verkaufspreis X Rabatt darf nicht niedriger sein als der Maklerpreis", "RB650X0GgnbB2n4QFIvyn": "Bitte Paketnamen eingeben", "O2O9o9Gdf7kujPQNuGDx0": "Bitte wählen Sie die Mehrwertfunktion aus", "hMbxn5NOJ97gDiey6VkNK": "Bitte Richtpreis eingeben", "AsjA-juIl1c-vrm5bkDZe": "Der Einstandspreis darf nicht höher sein als der Richtpreis", "hqIAi-yNhcVJJes_Wii8D": "Das Standardpaket darf nicht aus den Regalen genommen werden", "1tG8AEnd8XObHHyr_LJWO": "Das Paket ist abgelaufen und darf nicht ins Regal gestellt werden", "Zt6qNQ4s8YR-9ONkDbjz4": "<PERSON><PERSON><PERSON><PERSON> (Menge) muss eine G<PERSON>hl sein", "HcbviLThyjLIgx7yQhJIo": "Kosten müssen größer oder gleich 0 sein", "PnNbXwUM5AIUvXD55zozb": "Der Verkaufspreis unterstützt bis zu zwei Dezimalstellen", "8SZSVQ78DZJPp5CIkPmPA": "Verkaufsrabatt darf nicht größer als 100 % sein", "M_FhAqKNUJRMUlAI_5Vg7": "Der Richtpreis unterstützt bis zu zwei Dezimalstellen", "p0b0S03PgYUBMU8N6cMsY": "Sonstiges", "eWUjPc_p2SoSuyirGqpJC": "{0} Minuten verbleiben", "KDjF1mSArjb_8e-G98TE1": "Gesamt {0} Minuten", "3bRqkRhUHfGo7-5bj1NSp": "innerhalb von {0} Tagen", "qPN0DtMhqG7A1BZBT1cLO": "Bindung erfolg<PERSON><PERSON> l<PERSON>", "eabaCuk_HwzDEiN7IPbvs": "unbegrenzte Aufnahme", "7o9hFnRaSRSx3gBUxUS7c": "Autoverfolgungsmodus", "yvNR-W0Zy8b780JbYTcDz": "Wenn 000 eingestellt ist, bleibt das Gerät eingeschaltet, bis die Batterie leer ist.", "2akZqOLanUYhOK--8J8k9": "Einheit für die Aufzeichnungsdauer", "UQEVNmYnChMFTjlzBF8HL": "Fluss (M)", "68DfXrUSOzBjsZopo9k6K": "<PERSON><PERSON> (Minuten)", "j8lyKEC7rR2Sp2J66aMSE": "Stücke (n)", "thL7pcDqxtuMTgDoCxlBT": "<PERSON><PERSON><PERSON>", "WZSRsRw77pZ671WKh_pPu": "Diebstahlsicherungsaufzeichnung: 1 Minute = 0,125 Mio. = 6 Stück; HD-Aufnahme: 1 Minute = 0,385 M = 6 Stück;", "wl6K0SUQd6ZY6YvzFM9u-": "Leistungsmenge vor dem Laden", "BumVkHW3JAn_PRo1yQcpB": "Leistungsmenge nach dem Laden", "eimmiPhZYS-8rwVnb6O_F": "<PERSON><PERSON><PERSON>", "HUdLf5Qxok84Kaszyp_fp": "<PERSON><PERSON><PERSON>", "FNSsRfB5Bxlf2Olm2Bw5M": "Das Fahrzeug ist derzeit ausgeschaltet und das Aufwecken des Geräts aus der Ferne kann dazu führen, dass das Fahrzeug die Stromversorgung verliert! ! Möchten Sie das Gerät wirklich aktivieren?", "0re2Dz1xGLdRIiOtLWBC7": "Gegensprechanlage", "jYDwCZu9Zq3SmUDyx02I6": "Verbindung ist unter<PERSON>chen, bitte versuchen Sie es erneut.", "kT3Em9RLb40yUyQU2evbW": "Ende der Gegensprechanlage", "J25nBN1alltybJIfS_Oog": "Verbindung fehlgeschlagen, bitte schalten Si<PERSON> zu<PERSON>t das Mikrofon ein.", "3mmuWWCL0vQVg_OKx7s83": "Gegensprechanlage wurde abgebrochen.", "Q3niut5rzhCbc19r8z6IE": "Die Gegensprechanlage ist beendet.", "Pg7k0mGxvOxvRWARJpzfg": "Bestimmen Sie die Sprachsprechanlage mit {0}", "XNuroHgKla6Y8FamER4aN": "Überwachung wurde abgebrochen.", "jiTHwcnpRb8phd5923CyG": "Der Monitor ist vorbei.", "3QLWf5J9RxGxyAS6uvvn3": "SOS-Alertmeldungen für dieses Gerät nach dem Einschalten aktivieren.", "4RrCaePSVwyb6dIgHJAqX": "Autotür", "7zhRC0Dt31JaorstFzV6V": "Bitte bestätigen Sie, dass dasGerät diese Funktionen unterstützt", "LTRnQg4MJhykYTWC-gy9o": "Status der Kabinentür", "ABmfMli4oBWSNTfpwhgCE": "<PERSON><PERSON><PERSON>", "BqNwXl-ubCc-XQqV9O_ai": "Details eingeben", "F6x4nIih9qd4aJ38EvJwq": "Zusammenfassung eingeben", "FiAchNAkfO6rQ5HjVgU9H": "Treiberverwaltung", "6IiHBsPsmt2DsTs-jU5T1": "Treibername", "l4zTYCxKwWS524zpXPvDO": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UeGir6yBHf-qmk0S1MGlw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OXT0B09os-9cWGS3qpQlU": "Fahrernummer", "8MPbkjc_ZQug88ZNTfODw": "Punch-in-Methode", "lDQIuVzHPDvREIDtlgET2": "Status eingeben", "yfUqcxxldbfEsjLVNkxej": "Kartenabhebungsmethode", "ZyeVJa2gqbhhQO9I71t64": "Status der Kartenabhebung", "ji8RZWQkmXTT7ATPAAy_2": "<PERSON><PERSON><PERSON><PERSON>", "Pg8DZ2SFsTy8TojEeHX1B": "Standort ausstanzen", "0ikzdX00_UnFach5SBJXF": "Position einstanzen", "HIwKcqVBFF874LvZ_WGam": "Punch-Out-Zeit", "MToDC9TiRnmBcdPXl4FwO": "Namensnennung", "LnK9XV16hgzXMb2YOEzT1": "<PERSON>u binden", "rdH_UWyNxXZODhFqxxUmP": "<PERSON><PERSON><PERSON>", "0Dt9UrCF2o2oZXyw4vW13": "Bitte stellen Si<PERSON> sicher, dass die Fahrerkartennummer korrekt ist und über Befehle auf dem Gerät eingestellt wurde", "1leGeFJqaq67Fn4tiH1zx": "Fahrerkarte binden", "Srl9p3JczfeWAbfvhn6Ly": "Fahrerkarte entbinden", "UGF865b9yIhFrTCVyLWxX": "Es wurden insgesamt {0} Datenelemente ausgewählt, von denen {1} ungebunden und {2} gebunden ist. (<PERSON>uerst muss die erneute Bindung aufgehoben werden)", "hHaTIsbL4r6vms19pugun": "Treiberinformationen löschen", "dJ3KpaxrLmH_3rhK9unJv": "Einstellung der RFID-Nummer", "thiOWyWe1XmakaJ3GiOa8": "RFID-N<PERSON>mer löschen", "AxP3mfFKmnFaXGqh5fIhI": "Eine Zeile stellt eine RFID-N<PERSON><PERSON> dar, unterstützt bis zu {0} Nummern und die maximale Länge einer einzelnen Nummer beträgt {1}.", "rfidTip": "Die zeile steht für eine rfid-nummer. Die anzahl der nummern reicht maximal an {0}.", "d3bW-I2JF9X4k29xNZGGb": "RFID-Nummer abfragen", "_k-zAyJ8HPGbJdM17dzPH": "Unbekannter Treiber", "QO0NEmAJYU5fQSmklCOJy": "Systemabgleich", "PZufwLgOnX4fpRaD3iCSU": "Normales Punch-In", "9QOX0uIsKTjJBbo44I-Bb": "Die RFID-Nummer unterstützt bis zu {0} Nummern!", "-leT9C--bOJXbur1tGMLg": "Die RFID-Nummer ist ungültig", "FVu9irj9xPTNFGBGvlobc": "Berichtstyp", "COZQ6iZB1zS4aElBMs78o": "Weg", "qXO0YkCGN6h8FYEqKl3p_": "Historischer Bericht", "cGBoFIfJ2d5YNsf38ePbg": "Build-Zeit", "jZyH20rj2wEEEQcsmGcYk": "Zeitintervall", "BpQY6g4_DbX8RhgZMVPQ4": "Aufgabe hinzufügen", "qH0wuLIULJgKD37_nfLrZ": "Liste", "8hALcKt0MpF_SQGoBpb4K": "Format", "Mx9jIE1yFOacxR3pPnIFY": "E-Mail-Push", "bjPw4KOPuYjc6NUfC-SyH": "Einzelaufgabe", "9AYF2wxsGFHQUgIvy034D": "Zeitgesteuerte Aufgabe", "AdhpSaYYkylLxIlRrLNHr": "Jeden Tag", "n2QHPhR_EzYSzaNPQ6NmF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "9KLn6bfLtRc3MQgbsjyEo": "<PERSON><PERSON><PERSON>", "ieZ8uvcsbtzbKohADR8W1": "Gerät ausgewählt ({0}）)", "RyPBlexis_W5GIRM2muRj": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "G74wdj_2KXBhhyV7zDJdr": "Letzte Stunde", "TRBbE8yn5-eXAAnReeJJb": "Letzte drei Tage", "HS2zeNZLmT6EroeI89wkw": "Noch nicht gestartet", "iit63cuu_F-O9XEnoFvcn": "ACC-Zündung", "XUnTcb8HIcoLV6jX_-bHw": "{0} Minuten", "zqbPU0UwwDMJdobNznkdn": "Bitte wählen Si<PERSON> einen Berichtstyp aus", "er_Ap5667cZtQW4X0jKfo": "<PERSON>te geben Si<PERSON> die <PERSON>uer ein", "MJMdS_gSDGYTeKt7KPuKb": "Bitte geben Sie die E-Mail-Adresse ein", "VX7OSMnRmFVq9SD6yTT8c": "Nicht durchgeführt", "gksm18T7sI0BjTgYSs3xr": "Name darf nicht länger als {0} <PERSON><PERSON><PERSON> sein", "usermanagement123asdd": "G<PERSON><PERSON><PERSON><PERSON>", "s0ozWWyNHgC_hIgeFR28o": "Bestelldetails", "8nrk8LIy6KzpJQv8D1o0w": "<PERSON><PERSON><PERSON><PERSON>", "nJDVObsHH5ogCl6MxIO4C": "Verbundener Dienstanbieter", "daGXDH-K0OYEPY_EUdT-c": "Hardwaregerät", "qdU58CUu3U1NuSOt3DBVU": "Freunde verknüpfen", "t22DN7yhHaAkxCW69UYsh": "Letzte Online-Zeit", "D-b7dWflNTTmQ5ISj_Fcy": "Deaktivieren", "fuKtObxB_3LepAmhP3yf_": "Ablaufzeit des Dienstes", "629CyKaKNeUB9bKRG0Pay": "Bindungszeit", "4hM8uJZMOB7wXdwjRHa9p": "Sind <PERSON> sicher, dass Sie dieses Konto deaktivieren? Nach der Deaktivierung kann sich dieses Konto nicht bei der App anmelden.", "yk5sO0QMXGD7l_MxoTXYA": "Mobiler Ortungsdienst", "3aj3cEG2NB2VfffUFAdC_": "Bitte geben Sie IMEI ein", "M3jEXD8nRHgxmpsbQKAuz": "Bitte Telefonnummer eingeben", "xzJrxRX1dNU-Rb_0eqU-7": "Die Mobiltelefonnummer unterstützt nur die Eingabe von Zahlen", "gi1Rx6yaRf1mw_Ma10Og7": "Datumswiederherstellungsklasse", "0DzF5hCURmG4E1XvrInUx": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zTlleNA-rGV7NcSvnohCO": "<PERSON><PERSON> e<PERSON><PERSON>en", "j3-S-EHwypDM9f764RfWK": "Auszahlungen", "UpktOagmkL4g9LVa3s4DI": "Wenn der Benutzer die Abhebungszahlung nach der Erstellung des Abhebungsauftrags nicht abschließt, wird der Abzugsbetrag eingefroren und kann nicht verwendet werden", "HdBEUEdRZtTN4ZfrwWDWj": "Auszahlungsdetails", "cBvWd-2OWDLK-phHepxX-": "Bitte geben Sie Ihre Auszahlungsnummer ein", "3tBnaH8FayAwn1TmLrMRE": "Auszahlungsnummer", "-2qRzHpb3yJe-NXYwm9Q7": "Empfangskonto", "4ox8EnAHNJ1ge4l1vollX": "Zahlungsempfänger", "CqTkd8dVrd22g3nDNVCoy": "Auszahlungsbetrag", "0RwYjMyWnVMT6CIWbTvc2": "Anwendungszeit", "YgZRMzhnb44UD16opnREb": "Auszahlungsüberprüfung", "IhERyASJM8HWY4YSRVoeO": "Prüfung", "0GEWe45NcNov7FwWFSMVW": "Gesamtzahl der Bargeldabhebungen", "L6xr2tfBSDAIDoOf-18gW": "Authentifizierung mit echtem Namen", "LSUmKyLB2Gn7aIkH7L-hV": "senden", "Q0SSVtTXVVVneQs56sfNV": "Um die Sicherheit des Fondskontos zu gewährleisten, führen Sie bitte die Echtnamen-Authentifizierung des Dienstanbieters durch. Wenn Sie keinen echten Namen haben, können Sie die Plattform nicht betreiben! Die Informationen werden automatisch von ausgefüllt Wenn Sie bei der Systemerkennung feststellen, dass die Informationen falsch sind, können Sie sie manuell ändern!", "ExBfBD38BcFKpPdnaMMHJ": "<PERSON><PERSON>laden einer Geschäftslizenz", "Wsx4i0OdVEnXu1i63lLdv": "Firmenname", "ICkaBpHUtevl_AT1N__hY": "Einheitlicher Sozialkreditcode", "mzMzHjVtXGlHXVdehyj32": "ID-Upload", "Im9siJmOMFBKtFadxaSF8": "Name des gesetzlichen Vertreters", "wRlEeQixlH9M9_HIP4vYK": "Die ID-Nummer des gesetzlichen Vertreters", "SgzsmJZu_MjkxFDKgtv_h": "<PERSON><PERSON> können nur {0} Dateien hochgeladen werden und überschreiten nicht {1} MB", "qC-26Qu65eWrPudHuLnuV": "Unterstützte Dateitypen: {0}", "zEv9TzyaPU0xOkzgdVrzd": "Maximale Upload-Dateigröße: {0}MB", "mDQb0v7xumz3q5VuF3Vmc": "Gesicht des Personalausweises", "2ok-cfZmy8rdxwZL6-4nd": "Nationales Emblem auf der Vorderseite des Personalausweises", "IkmaKekEYC3fKh3fRt8NS": "<PERSON>te geben Si<PERSON> den Firmennamen ein", "_RtgmCH5vQ8wdY1tlQHW7": "Bitte geben Si<PERSON> einen einheitlichen Sozialkreditcode ein", "nisCJPEsBiP2BkHolBjWx": "Bitte geben Si<PERSON> den Namen des gesetzlichen Vertreters ein", "omKEokmgAc1zk-PZxqP-8": "Bitte geben Sie die ID-Nummer des gesetzlichen Vertreters ein", "y1yB9wGJ8CWF63TCI9WpX": "Bitte geben Sie die Mobiltelefonnummer des gesetzlichen Vertreters ein", "hQBDtnCXVOY5IURttX9Wp": "Erwerben", "Pzs_J67ueBLvXIl0jEXSE": "Erfolgreich gesendet", "ZPq_ipnNUy_FW5VWSeimD": "{0}Erneut senden in Sekunden", "_blypwAlGDx8o0-3JRFdO": "Bitte Gewerbelizenz hochladen", "LTy7Te4YBG56m2zQdlHiP": "Das Ausweisformat ist falsch", "0ZqoYIX5loeLfUCWxwKB0": "Das Format der Gewerbelizenz ist falsch", "IYmN5ElWd2GKDXm_6Ox7l": "Der einheitliche Sozialkreditcode ist falsch", "jjTIqk33Rdkkx6Py1HxoY": "Die Telefonnummer ist falsch", "FDRZnbeysAjO9tp4YIYqv": "Um die Informationen zum echten Namen zu ändern, müssen Si<PERSON> sich an das Unternehmen des Unternehmens wenden und die Informationen offline ändern", "rbQMeechuyoZqAFzEyLM6": "Authentifizierung mit echtem Unternehmensnamen", "TI6ctBVhkuX19fhWGPn49": "authentifiziert", "8jyucpdW1YuSmtRGAIb6K": "Geschäftslizenz", "IsF9Jc79BZ211alKXLRR0": "Ausweis", "QFiEj7y94A9Ja8AkWUNyf": "Handynummer mit echtem Namen", "qd8IBrhi8u-w8Y7DF8b-q": "Personalausweis des gesetzlichen Vertreters", "HAN6a6h7V1iZt0lnwGGcV": "Mobiltelefonnummer ändern", "T83DURPRdSVIBzAvsaeez": "Bitte stellen Si<PERSON> sicher, dass das Auszahlungs- und Inkassokonto normal eingezogen werden kann, andernfalls schlägt die Zahlung fehl und die im Auszahlungsantragsverfahren anfallende Bearbeitungsgebühr wird nicht zurückerstattet!", "d5h6lLvBW7oXMESBAf-xM": "Enterprise-Rückzug", "9dNZZYaCdlmDKhSaYelkB": "Persönlicher Rückzug", "GZy7CzuyKTwsBKOmuCs1T": "Die Informationsauthentifizierung wird derzeit nicht durchgeführt. Bitte fügen Sie sie zu<PERSON>t hinzu", "JN6k5nduoDoNFRKZS3DEn": "Abzugsbetrag", "pHaDrlW6Ws3jwOkuAbQoM": "Spezieller Rechnungs-Upload", "t66P_5Nqai7Za4Eu4ITGB": "Vorlagendatei", "uvVmCHuLW4xeFcLm45zbc": "<PERSON><PERSON><PERSON> ho<PERSON>n", "qR0w92Dg9fchUpj2DzSC_": "Unterstütztes Dateiformat: PDF, maximale Upload-Dateigröße: 5 MB", "uCBzEQrQku-26KkCRss4A": "Eingegangener Betrag", "fU-37Env5DcFrKxRb3xsW": "Die Auszahlungsrate beträgt {0} %", "vcjXle_BohdJUl4d1oLue": "Bitte lesen Sie die folgenden Bedingungen, Servicevereinbarung und Datenschutzrichtlinie und stimmen Sie ihnen zu", "7SBVFKuCpv5odq1HN33Ln": "altern", "Wx9bkDQ3RJ4pkj9EV2TAy": "Ich habe gelesen und bin mit der sofortigen Lokalisierung einverstanden", "w3RNmBUSW9RqkqTEoK59O": "Servicevereinbarung", "OqJohIkvVLQABgxc5DRdr": "Datenschutzerklärung", "bjGxifi3699uiCO6gsCRV": "Kontoverwaltung", "xiLtJRgF_g8uX1RTxKvfX": "Die oben genannten Informationen werden automatisch durch die Systemidentifikation ausgefüllt. Wenn die Informationen falsch sind, können sie manuell geändert werden", "NaD6zzw94Z1os45DPZwjr": "Kontoeröffnungsgenehmigung hochladen", "CUmdfQcRlZ4i_0jCpy3a0": "Name des Auszahlungskontos", "nMIYlhG5tsGuYCDxDF3tt": "Einlagenbank", "8Q3xmvZ9DUvHOt7XY-i9i": "Rechnungsname", "BjInLzWyZCAq-mP4O2kg0": "Name des Käufers", "xCBXDogrKXOiq4chx5EwV": "Zollnummer des Käufers", "YyzFoNwurUCVV9H6hgtsA": "Name des Verkäufers", "CCjOqQunpaqrCRLHmX3N4": "Kauf des Verkäufersy-Nummer", "aBPmzVAoqSZiJWXyn9bN-": "Steuersatz", "REFy169sYWJ7Md2hb8sGE": "Bitte geben Sie den Namen des Auszahlungskontos ein", "LWAmlhiJWmHzYrie1S9N1": "Bitte geben Sie Ihre Kontobank ein", "pJWb9Yer6FxP6ovkzGrK_": "Bitte geben Sie die Kartennummer des Bankkontos ein, auf das die Zahlung erfolgen soll", "WDW0-yYxFWMfodLosOWb9": "<PERSON>te geben Si<PERSON> den Rechnungstyp ein", "sgfGs3OTZyvteg31GHtiX": "Bitte geben Sie den Kontonamen des Käufers ein", "a2ULMzDVkEk-nPXKBJUXI": "Bitte geben Sie die Steueridentifikationsnummer des Käufers ein", "vgTEJTdZurmuqJhzrZ29P": "Bitte geben Sie den Kontonamen des Verkäufers ein", "vKsW1EMNxUJGGfV4ZYBXg": "Bitte geben Sie den Kontonamen des Verkäufers ein. Bitte geben Sie die Steueridentifikationsnummer des Verkäufers ein", "TvCzo89fxA8tR-8Lpyb0q": "Bitte Steuersatz eingeben", "Fkbqaa_km9aC0nyOkzceB": "<PERSON>te geben Si<PERSON> Ihre Handynummer ein", "hnIRT9k39y16Q99rQQzsA": "Personalausweisnummer", "oKb5Wq5V5XoAfzU-M0Otf": "<PERSON><PERSON> wurden keine Bankkarteninformationen hinzugefügt", "WryJR0-_zuJIjTJ4EKK1z": "<PERSON>s werden keine <PERSON>-<PERSON><PERSON> hinzugefügt", "zbt_yS1gKEnz57prLvp2K": "Bankkarte", "jvnedWVXi4gkwKd5I2hvo": "Die oben genannten Informationen werden automatisch von der Systemidentifikation ausgefüllt. Wenn die Informationen falsch sind, können sie manuell geändert werden. Bankkarten der Klasse I werden empfohlen", "DE215MPVNCkdUHvjIgGZk": "Bankkarten-Upload", "Fllf7DIpwifaI2rjf1wQO": "Empfängerbank", "L_lzmLBmj3YzI8mZG5J0S": "Alipay-Konto", "LgIQiPo4j90qzVhoe0Vy8": "Einhalten und zustimmen", "sdFitZwo_yLFH4QGGN3Yt": "Flexible Beschäftigungspartnerschaftsvereinbarung", "5tav7xkbaZ8_ibteH8f-S": "Alte Mobiltelefonnummer", "_0koNeMLJ29pQU0iaEDgu": "<PERSON><PERSON><PERSON>", "DxCo_EDksU3itpOI06hg-": "Bitte geben Sie den Bestätigungscode Ihrer alten Telefonnummer ein", "KKTmtCyrgM1yvbXV5zHcv": "<PERSON>te geben Si<PERSON> Ihre neue Telefonnummer ein", "3-OViSd7qngs2IVge-AK5": "Bitte geben Sie den Bestätigungscode für Ihre neue Telefonnummer ein", "oQ34kNi30enS9BhzsG0H-": "Um die Informationen zum echten Namen zu ändern, müssen Si<PERSON> sich an das Unternehmen des Unternehmens wenden und die Informationen offline ändern", "0sCR-_9qqz7K1qILK3DVb": "Bitte geben Sie den Auszahlungsbetrag ein", "EbBJyirZ75fpO-gVDM83Z": "Bitte geben Sie den Empfänger des Kontos ein", "OsDOX2z2g1Q-gK32LRxFM": "Bitte geben Sie die ID-Nummer des Zahlungsempfängers ein", "_l_YQpYxCoNyeAL5aqwGZ": "Der Wert enthält maximal 50 Zeichen", "pWME5Atx2F7StkRlHkIm3": "G<PERSON><PERSON> Sie maximal {0} Zeichen ein. Ein chinesisches Zeichen entspricht zwei Zeichen", "qkjCxOqPs_QAibUK-0Y1Q": "<PERSON><PERSON><PERSON>e maximal {0} <PERSON><PERSON><PERSON> ein", "WJLplQQ9j88JLH1YqCRmA": "<PERSON><PERSON><PERSON>e maximal {0} <PERSON><PERSON><PERSON> ein", "TF-2MZlCuvwuW28jBv6I0": "Bitte geben Sie den Banknamen des Empfängerkontos ein", "w_r8IwPRfn4mFsJI9VODn": "<PERSON>te geben Sie Ihr Alipay-Ko<PERSON> ein", "qlaqPAd2iRvHO0ZwbK-za": "Dienstanbieterkonto", "VDbpijEe2xTelunTQS-4e": "Der abgezogene Betrag übersteigt das verfügbare Guthaben", "eY_5eabsCmQBbE3r0rV5a": "Der Eingabebetrag ist geringer als der Mindestbetrag und die Mindestauszahlung einer einzelnen Summe beträgt {0} Yuan", "TRJhAFDq4ep7qm7BVCQXJ": "Der Eingabebetrag überschreitet den Höchstbetrag, das Tages- und Monatslimit {0} Millionen Yuan", "nST-25toc3noZP0cfW1qk": "Der eingegebene Betrag übersteigt das verfügbare Guthaben", "DK_Faq17n14OmHECO018w": "{0} <PERSON>, {1} <PERSON>, Rest<PERSON>rag {2} <PERSON> ab<PERSON>. <PERSON><PERSON>, den Auszahlungsantrag einzureichen?", "GzYwkFzGANgH5TJD5X8X2": "E<PERSON> wird er<PERSON>, dass es innerhalb von sieben Werktagen eintrifft. Sind <PERSON> sic<PERSON>, dass Sie den Auszahlungsantrag einreichen?", "xkj_ATjt1xs4_R-rg1Ji0": "Profilbild ändern", "5207eon94HDHbK4sDdL74": "Das Formatieren der Speicherkarte kann nicht wiederhergestellt werden, bitte gehen Si<PERSON> vorsichtig vor!", "EHD3KoOJqi2GCWNVeEmwd": "Formatieren Sie die Speicherkarte{0}", "kO825bS74aibjof-TZHgI": "Die Anzahl der exportierten Geräte darf {0} nicht überschreiten.", "rwsPrmsetvYwcvgy_tgQH": "<PERSON> Alertwert muss eine Ganzzahl im Bereich von {0}-{1} sein.", "xN-U2ljzfROs3R2UFCYsM": "Amazon-Tracking-Nummernverwaltung", "_N5YSHeYO9S0dkx3zF49M": "Bitte geben Sie die Sendungsverfolgungsnummer ein", "nBb0XGJJR6Ejlzv5qSHpV": "Amazon-Tracking-Nummer", "9tWLlIJg_5sgvhCXM44UQ": "<PERSON>te geben Sie die Amazon-Tracking-Nummer ein", "IyD6K3KtVOOO_VcBTjgZ2": "Es kann nach dem Löschen nicht wiederhergestellt werden. Möchten Si<PERSON> löschen?", "UFmFUDIdFnppVa1V3bBST": "<PERSON><PERSON><PERSON> ho<PERSON>n", "_oBDgDEVJpXqs_vs3_HI9": "Vorlagendatei", "YGoedXAVvM1MW-3niNbjm": "Erfolgreich importiert", "Wi6h3aRHdkRiVA_ltXWj0": "Erfolgreich bearbei<PERSON>t", "QE818HiR698_BEiYMx3u2": "Unterstützt nur EXECL-Dateien", "uTwenDZPA7tqG0989SQXu": "Paketrabatt", "aaBbwSAmqzqUCcJRh515g": "Agenturpreis ($)", "QLgJjy0jWjsRUEDzFwoX8": "Verkaufspreis ($)", "OFM2oOsQ2x9IwEF2ttv_b": "Richtpreis ($)", "xOo6bW69d4UFSs5A8Lc6t": "Kostenpreis ($)", "gQPrng0oObyjc6BgSvrjD": "Dollar", "g68FVq2_VXAjp7hYpDiRN": "Bearbeitungsgebühr", "mj3M4r0KwKkzvAFrIO1Wr": "<PERSON><PERSON> erhalten", "5-ZgZDBneAm7B6wX8GojM": "<PERSON><PERSON>", "A6LwrujHDGaAuqduxVNP_": "E/A-Einstellungen können nicht wiederholt werden", "DVbzGOXzFw3chkPtHUj3s": "Aufnahmevorgang", "bindingRecords": "Bindende Datensätze", "LpQxX7pBohd343PV2C6l-": "Build-Zeit", "_b20VKdHpaKqao11ii03n": "Operator", "w7fGz3NHk93u9dv-zX0QD": "Verbundenes Gerät", "YOrAWZ7vpBk3TJg-dkb4P": "Aufzeichnung", "F0uq5S360ilbM_B2J6AKU": "Statische Zeit", "Fcj9rd0TdhVtlRNldRgLO": "Statische Startzeit", "sTK0zTcZFNQTdsKoeupiA": "<PERSON>te geben Sie eine positive G<PERSON><PERSON><PERSON> ein", "AAQN_3lzSsjg1ihjW8Ox6": "<PERSON>te geben Sie eine positive <PERSON><PERSON><PERSON><PERSON> von {0}-{1} ein", "sjsJKFIklgkg1ihjW8Ox6": "Tag", "SQpS4TgBG3fMcqbMSt5r6": "Familienkontoeinstellungen", "jdlS4kVTg_CeomNPAM_bK": "Speicherkarte {0} formatieren", "tcqzqrxZ_gYuVhkt8OehJ": "Stopppunkt", "T6iMJCitY1kEG5bcW9HaV": "Freigabe-Link-Parameter sind ungültig", "-6NUqOp6i8GMNZf-wX0W2": "langsam", "MTpCOGV40pnn900xY1J0D": "schnell", "W1n3IpE3yaJTXBtZ2UgoA": "hohe Geschwindigkeit", "lmCYFn9l-af231uvKKXNg": "Zum Anzeigen klicken", "5l9_lUSa6C_lFR_eESOlJ": "Testen Sie den Kurs kostenlos", "PjztEjunq2DdH8CUhaTom": "Freund Klasse erhöhen", "5j3kZHTwBeczKhgIlGP_-": "Se<PERSON>ndenmodus", "OH_8D7HPFdgNgklMyg1AE": "Ausweis falsch hochgeladen", "OfBdh8qjrGLU-ZdWl5vOq": "Das Format der Kontoeröffnungslizenz ist falsch", "OWZM_rW5ca_xlQz_J8Xvy": "Falsches spezielles Rechnungsformat", "9OPyc0VO0-5p0d5Qed7Qy": "<PERSON><PERSON> spezielle Rechnung hochladen", "6HtrWK5wh8axd06VVCS92": "Als Standard festlegen", "uOPB9tT30SyYk_0INSAYq": "<PERSON>te geben Sie Ihr Alipay-Ko<PERSON> ein", "n_gZB7je9SGq7ENwoME-1": "Erst wenn die ersten Kontoinformationen für die Auszahlung perfektioniert sind, kann der Auszahlungsvorgang durchgeführt werden", "hIpV6Yp1O1wSHehp-inf-": "Das Sammelkonto darf nicht leer sein, bitte hinzufügen", "RMrKKjo43e39krDHu3aj8": "Bitte halten Sie sich an die flexible Beschäftigungspartnerschaftsvereinbarung und stimmen Sie ihr zu", "PA1XThpT0ZeCsgxFK0j0J": "Das Kartenformat ist falsch", "tKjv5WnytxK4wm-WJXePG": "Zahlungsmethode auswählen", "XFJzOJWIXzS-m-Ny2eXc9": "<PERSON>te geben Sie eine positive Zahl mit bis zu zwei Dezimalstellen ein", "ESkCYm4yQfnXTqmN8lyEM": "Die alten und neuen Nummern stimmen nicht überein. <PERSON>te geben Sie sie erneut ein", "2cdbOHjhkxnwglIpBtMkT": "Verifizierung des echten Namens", "IiLSldp0KbladWLu3iyAc": "<PERSON><PERSON> hochladen", "Wo_ctGJBTe7KxuJltu9Gt": "Avatar-Vorschau", "JmBSnAvO4DN3XSi1cxZnb": "Der Auszahlungsbetrag darf nicht leer sein", "Z0Tm_VlEKyRO3z_N1zVo5": "Der Abzugsbetrag darf nicht leer sein", "85Bv3yRHBX1Q-bx2fsC7i": "Die Telefonnummer darf nicht leer sein", "zovpNYJACi9yNGKOVuO_j": "Der erhaltene Betrag darf nicht leer sein", "6_bQ6z9UwX8_tB64es-tv": "Nicht hochgeladen", "G_w7OaWVA0Fc_ZaYRdEet": "Geschäftslizenz falsch hochgeladen", "MNysP677Xd0NQ6tBqvUxG": "Kontoeröffnungslizenzense wurde falsch hochgeladen", "oJ1yZ_MvfuN0QEeBrJW39": "Die Sonderrechnung wurde falsch hochgeladen", "n-cfAU-iHIIcYW0DRnb1M": "Bitte Ansprechpartner eingeben", "1kfaRbMY51Sk_FlCts7OX": "<PERSON>te geben Sie die Kontaktadresse ein", "IEdjqcPX5tg0KEDbRFuFf": "<PERSON><PERSON><PERSON><PERSON>", "u7luzUuxiNGav9UQrcdN-": "Einzahlungsschein", "Z3rtP8jS9MkkLD-7eNsm9": "Zahlungsprüfung", "0gKC2yGndZbATta9DrMLH": "<PERSON>te wählen Si<PERSON> ein Ergebnis aus", "Jht787aJMAS7FrZc5PFwo": "Bitte Bemerkungen eingeben", "zsci9ZO7C5fd2L3lpNgPb": "Bitte Grund auswählen", "-OG-7bm5-uMXgqOUZJs5E": "Bitte laden Sie den Einzahlungsschein hoch", "vDoHr7o7eBmMjapFFY8OX": "Der Benutzername ist falsch", "FEiCv02i2fFwURArNjISu": "Kontonummer ist falsch", "YTPo1eg1Yz7EQvr72KecO": "<PERSON><PERSON> wird empfohlen, die Empfängerbank zu konsultieren oder die Überweisungsmethode anzupassen", "I1GGIAAo0PS8mfokKAxQO": "Bitte geben Sie den Grund für den Fehler ein", "21imeJSSKJ9gbnFeLZBAO": "Prüfung der Umtauschrückgabe", "DZzQ1lUL4GRdGJWqYGSYd": "erneut austaus<PERSON>", "xdADy1Z3oZAlvbj18SpWe": "nicht erstattungsfähig", "2tHLcFI4qtrbhNizgLM_e": "Die Bankkarte ist Klasse II/III, was das Inkassolimit überschreitet", "GVKUFE9vSgm2vThS0OcLb": "Unternehmenskonto", "2xItceXA56C70CviN7hdw": "Persönliches Konto", "LZhiHlMRIm8k0EoZom6Tx": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WYfBrkaFrjnSb4zcrXvXP": "Auszahlung erfolgreich", "uC2ieR0grcypLFaU4Cin-": "Auszahlungsfehler", "fiYBECKlqfQ37zsh0oe3e": "Auszahlungsrate", "_ex7EzDE-2SAKg28OfvjC": "Kontotyp", "rcNxw-zV_WkSiLzT52c7F": "Konto prüfen", "YffYxg-01J2YhI9tvT13w": "Sonderrechnung", "uytDBRHTcdWnmzmK6fKY0": "Verarbeitungsfortschritt", "PiElRejZ-v3cSTBDm5qv5": "Auszahlungsantrag", "uj4JVD9nGaVo7z7c3-uH9": "eingereicht", "DLKB29kasijsA6oH3Hi1h": "Bargeld abheben und Geld überweisen", "5U7GHtibp0j-Y7dysyT4-": "Empfang prüfen", "VYdJLhD50Dg1KJJMCmDFZ": "Kontoänderungsdatensatz", "yqvaBjHTsKmA2LqSHAMJ3": "Bitte geben Sie die Ticketnummer ein", "2dcfGeIDG9wGif_SW6SCm": "Kontoinformationen", "xM9JFuMrJJaalkJwLOYoj": "Elektronische Rechnung", "RC9ijDhKKlVpzWTJ_XeCd": "Zahlungsmethode", "k6EP4_ZJK39lzc9AwkWvT": "Separates <PERSON>", "7lftycmlnIBPgSygUr96m": "Auszahlungskonto eingetroffen", "S2TQ3uhLwMNpAtrlBHHLZ": "Offline-Verteilung", "LTDA3b7Un6GgIPY4leo_H": "Offline-Auszahlung", "HrJ-Uh34Wr3BSUdYA4iZ4": "Auszahlungsprovision", "YdSMTbOvyY2TixaUAcudm": "Datensatznummer", "by_j84zg2KXDekxc0ORKK": "Kategorie zur Standortbestimmung von Mobiltelefonen", "WNFXoZ2k0AQRuxAeHiePH": "Paketgeschenkkategorie", "1Lm2fks-D28PGJDB2-QEm": "Geschenkaufzeichnung", "NSVI24la1iHcxE77XkD3j": "Bitte Bestellnummer eingeben", "LbFrBk6riSeMQhZfbSFBU": "<PERSON>ugeh<PERSON><PERSON><PERSON>", "24jZw1sAUlrUcHXVqC8nl": "Alte Gerätenummer", "ljiVq_vz1IcyxSBvLz5n9": "Neue Gerätenummer", "BLHhC-hCNlmfdLUyrpBGE": "Inhalt übertragen", "dCzQ4KFY-2pAvEdvMcXbd": "Funktionselemente", "UTq55YDmzFRzm3R7o5pS3": "Wirksame Zeit", "fcR_eRPV_vuaCod-X3pMK": "<PERSON><PERSON><PERSON><PERSON>", "eg65yL7TTeZtMUi-SI7Rf": "Paketübertragung", "9NTH28CxVugtC2EK38qjg": "Geschenkpaket", "YeAgt-s65pnisd3GVw5cZ": "Geschenkkontingent dieses <PERSON>", "8HGMdlzSUPltolCP43dQ3": "<PERSON><PERSON><PERSON> dieses <PERSON>", "e-k-39po_d2imfFB9rqcg": "WeChat Pay", "-6DPwMdNy_QQpwuo7kuOh": "gesamt", "OVTUT6W1T8c44aoqR3P6N": "Bitte Zahlungsart auswählen", "XhBA-h70LpYFm5FvDEQZD": "Scannen Sie den QR-Code, um zu bezahlen", "MKDFByNhuA4Q6lc7kKGnD": "bitte verwenden", "h0svoUIo43SfkW63bLk3W": "{0} mal", "VgvWw-6git1MC9SgonQOH": "Die Anzahl der Geräte ist größer als die verbleibenden Male! Bitte erneut eingeben", "BcqbA7moiyqrLmNgNYVy5": "<PERSON>te geben Sie die alte Gerätenummer ein", "BkVM3eOcVuzpNIfjm-pkB": "Gebundener Benutzer", "ifvJXu_6FIFkEmAwGXVGL": "<PERSON><PERSON><PERSON> {0}", "1ULnqJ-kbF80fAeQeJcku": "Bitte geben Sie Ihre registrierte E-Mail-Adresse ein", "MkwKY8UFV5BYmGR2Mxodq": "Mailbox-Formatfehler", "jhesBFPUhQElo899N45RY": "<PERSON><PERSON><PERSON>er-E-Mail-Adresse", "Ef1mk9PHB8ODRFJoczZKV": "<PERSON><PERSON><PERSON>", "oEDqfKAIpGPQkQG8CGkgy": "<PERSON><PERSON><PERSON><PERSON>", "aLxS4NInZyaUxSNvXTJB9": "Elektrofahrzeug", "3q5qw05Wj8yWZbxMIrGZp": "GeoKey-Verwaltung", "taCCGgf3d8bb84p3_id3E": "Schlüsselwert", "qxIADlgwah-virZ8nR6iu": "Kontingentzeitraum", "Y66QsIM1QtLdK5WM2rd3D": "Kreditüberschuss", "LGHa7JNWZdeF1_LXIkraR": "Limit", "uIEW8VtyZTL8vSRQuCNDv": "t<PERSON><PERSON><PERSON>", "H9MpmjLPEK7MSh_UDrxrI": "Ich habe gelesen und stimme zu", "kTLDGY0XzS0m8E0FCTjgU": "<PERSON><PERSON><PERSON>", "tHuKOisIETCyXaBDTuGJj": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnkuxm9_NrkH8_Tv3YleO": "Schlüsselname", "j7YuqwDqRlF0IjWiuKlba": "Google", "FTIf59K9kivPSetxIUrNf": "Tencent", "aIBhMRPmLVzRG-pQb1Mo9": "Baidu", "CHuYEEkDGK4YxGo8MurjZ": "nicht ben<PERSON><PERSON><PERSON><PERSON><PERSON>", "FXXEmOPHqITyLJvOCEuUC": "Schlüsselname", "pk3H6woWKDwAEch-Xwb41": "Benutzer konfigurieren", "PZwpizZ-BjCmgRnSp048L": "Ob <PERSON><PERSON><PERSON><PERSON> gelöscht werden soll: {0}{1}?", "Jm91_vcsOlbdLFr9M28Qu": "Mit der Geocoding-API können Sie den Google Address Resolution Service nutzen. Wenn Sie den Google Address Resolution Service weiterhin auf der Plattform nutzen möchten, müssen Sie Ihren Geocoding API Key auf der Plattform eingeben. Sie können sich registrieren und Holen Sie sich Ihren eigenen Schlüssel in der Google API-Konsole. Sie können ein persönliches kostenloses Zugriffslimit basierend auf Ihrem Kontingent festlegen. Bei Überschreitung des kostenlosen Google-Limits wird Ihnen eine Gebühr berechnet.", "FRbY0el5plNTH4CH8dDPT": "Google Maps API-Ladestandard", "9mmurV7wHvpIeR7nUXj2O": "Schlüsselwert", "ZidcfkEaoaegWCkr25Ddp": "Restgrad", "F2g2pJ6QAesDcCFK_8Ppv": "Anpassen oder nicht", "SEpOsvkTJIs7rq1SuxBWL": "Schlüsseltyp", "JVS1v5gcUSn5hMyjRZiMe": "Bitte geben Sie den Schlüsselnamen ein", "cD5nN5W2nGnv7ehPyw5fr": "Bitte geben Sie den Schlüsselwert ein", "26CTHIClRcVCK1bBRwhnj": "Bitte geben Sie den Schlüsselbetrag ein", "zoGrf2ereLlxNVDFzTq3t": "Bitte geben Sie die verbleibende Anzahl ein", "SXBcD_L6s79BG2r1rbApf": "<PERSON><PERSON>", "wOFW2Ax2skLKfvbu1gLQt": "Sie können sich registrieren und Ihren eigenen Schlüssel in der Google API-Konsole erhalten. Sie können ein persönliches kostenloses Zugriffslimit basierend auf Ihrem Kontingent festlegen. Bei Überschreitung des kostenlosen Google-Limits wird Ihnen eine Gebühr berechnet", "tjCzMH4WONHn5m1VGfzTQ": "Bitte überprüfen Sie die Nutzungsbedingungen für den Google Map API-Schlüssel", "SxnXfOEYbbEoCICpC21pX": "Bitte wählen Sie einen Zielkunden aus", "unLEXxQ5Se8zqvgBrB8Qc": "Abfrageklasse aufzeichnen", "DFz6LZXDUzGC9h1Hnul2u": "Bitte Pakettyp auswählen", "KFCuQkp2b0zldaAxmGwgB": "E-Mail ist nicht registriert", "gy8pmlehGRoLkJdV_edY8": "Vibrationsalert abfragen", "wlukedrwDRyOK0XeIkhZO": "Aktueller Vibrationsalertstatus des Geräts", "HihkzR3INnfDv_bS0ml8x": "Positionierungsmodus abfragen", "Ppa_zXemQO2ey5qX_mzXm": "SOS abfragen", "-S5Zj2mRhidmTd8IcTPAF": "Fragen Sie die aktuelle Gerätepositionierungsmethode ab", "dvr_kBxVSCo9O4skA53qY": "Die aktuell eingestellte SOS-Nummer abfragen", "rbAN2_LHXoCO3Rj9ifHHq": "Timing-Positionierungsmodus", "CEQE89HjKKDIM5xBDWBA8": "Intelligenter Positionierungsmodus", "OPHvVo3S-kkq8EJfg7gSZ": "Super-Energiesparmodus", "j-SWrpc1Vrf1S2wKVXsrg": "<PERSON>ckermodus", "ILxJ2c9W9RlSgcyXJU3AI": "Zahlung erfolgreich", "sZW0PuFvcmAVNmno6QOc7": "Ko<PERSON>", "mLKZ7cU5tvQwf6tn5ddby": "Bitte neue Gerätenummer eingeben", "xZblAEf-Ca5QidcBCgFWI": "Nicht zum Verschenken", "l9KpGab_FPNpBWZDNAWrF": "Sind <PERSON> sic<PERSON>, dass Sie das Paket übertragen?", "9ti28ZU5Zt40xby1G_ahv": "Geschenkkontingent dieses Monats = Gesamtzahl der aktivierten Geräte im Konto * Anteil, wenn weniger als 10 Mal, ist der Standardwert 10 Mal", "aizY__cr8e9LMRXQUTIgf": "<PERSON><PERSON>hl der aktivierten Geräte: {0}", "ux9Kw9v1rmHt6LahAEhBK": "Verhältnis: {0} %, aktualisiert am 1. j<PERSON><PERSON>", "bSBDQVvDZ5-2m1m1b0X38": "Kumuliertes Einkommen", "vx8cJaHHxhErDo_u98V9g": "Bewerten", "wvZQ-g667pqaS5k7ElKBJ": "<PERSON>ugeh<PERSON><PERSON><PERSON>", "SCx72TSY1VcnknS0ks5lJ": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DeCU3YJEQZ4m6WTKYOimH": "Systemeinstellungen", "JgTflL8nrYglkCeN2xVKk": "Längeneinheit", "uaDXv6VTf_i6KFdDpiA5b": "Kilometer (km)", "oz1Fpf2Cy2REH5VddyL87": "Meilen (mi)", "_Mb6dEr1YRXYFPEh11ycQ": "<PERSON><PERSON>", "TYzDOtOg8b-lSYzNRPTjQ": "<PERSON><PERSON>", "machineTypeSupport": "Modellunterstützung", "aUEMcGA3UzcsMsybrAd-j": "aktiver Zustand", "tCdgp3PrZMzQB5QsVmyih": "Kürzlich gebundene Mobiltelefonnummer", "KeCWjWFz4-VIYEIeukzc5": "Letzte Bindungszeit", "S741RuNHsAsXlx43GUApW": "Bestellung storniert", "kIM7VbZMekH-KO6wIHSqh": "Stornierungszeit", "WRHOzHvIYZ7yQT7MYxaBw": "Ob ein <PERSON> g<PERSON>uft werden soll", "LNt5xB9FS0cjz8sZxzrB2": "Details zur stornierten Bestellung", "8ki2CCZoENshLvjE7QLGh": "haben", "5Sv5ytuUMlSPalt5usauH": "Importzeit der Ausrüstung", "7HL8IXRIIp5cKUnInarno": "Ledgers<PERSON><PERSON>", "J0UYZYck8uhBvlodD6N4V": "einfrieren", "W7PEgcWDx2hes2lBqOX4u": "<PERSON><PERSON> angekommen", "91_1vPtPXMzqa7sdwnbWw": "Erhaltener Betrag", "8cpgA7arN32LnJYYoHs6b": "Der eingefrorene Betrag besteht aus dem eingefrorenen Kontobetrag und dem eingefrorenen Auszahlungsbetrag. Klicken Sie auf die Betragsnummer, um die Details anzuzeigen.", "iZ6Q-Cqe20id8Jqqi-n28": "Die persönliche Authentifizierung mit echtem Namen wurde noch nicht bestanden. Bitte wenden Sie sich an Ihren übergeordneten Dienstanbieter", "IpRD_Ggq8UhJWAyYyRTo4": "Gehen Sie zum richtigen Namen", "1xXJa_y9Dj73JgvFgLmpY": "{0} Echtnamenauthentifizierung ist noch nicht abgeschlossen. {1} kann erst zurückgezogen werden, nachdem der echte Name authentifiziert wurde. Möchten Sie sich authentifizieren?", "7R8ninLk0Y8vd9nEBvIPz": "Unternehmen", "oYXW0p5J3DkyunUzLbpq3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3bYcoGZhmlMZGYFqPsQMI": "Frieren Sie den geteilten Betrag ein", "DPJPrWEMHEaNY32Puj9ld": "Um die Sicherheit der Gelder zu gewährleisten, wird der erhaltene Kontobetrag für 7 Tage eingefroren. Nach 7 Tagen wird automatisch das verfügbare Guthaben verbucht, bevor <PERSON> Bargeld abheben können.", "1LeLeSLQG4tH3Mb7oPHNA": "Auszahlungsbetrag einfrieren", "24RGgbcmuZQWvxaEek6P9": "Ausgleich der Differenz (Steuer)", "cbV0W15tenwiobhwsxGKS": "Aufgrund der Vorgaben des Finanzamtes muss unser Unternehmen 6 % Mehrwertsteuer zahlen. Legt der Dienstleister eine Rechnung mit einem Steuersatz von 1 % oder 3 % vor, muss diese den Mehrwertsteuerbetrag von 6 % ausmachen, der vom Kontostand abgezogen wird.", "MxKJZnXtEbLQy_CJM4Wo0": "Bitte laden Sie spezielle Tickets hoch, die den Regeln entsprechen", "LgRB21jCFOEI2iFWMhk_u": "Bitte laden Sie ein spezielles Ticket mit dem Projektnamen als Werbegebühr hoch", "fugpgJ6HPD0IiniTJJsu3": "Name", "wxLv0t9cVv88YKsE56eV0": "Authentifizierung mit echtem Unternehmensnamen", "QLw5b0HuFkQor0jnyYnDQ": "Persönliche Echtnamen-Authentifizierung", "S_3XDTwVR39xyBxoDNayv": "<PERSON><PERSON> laden Sie Ihren Personalausweis hoch", "YoC4H8soU_i0YdFgPxxDM": "<PERSON>te geben Si<PERSON> Ihren Namen ein", "Zxsxh9PZBmwPf4rykaJF8": "Um die Sicherheit des Fondskontos zu gewährleisten, ist der Dienstleister verpflichtet, die Echtnamen-Authentifizierung durchzuführen. Ohne Angabe des echten Namens ist der Plattformbetrieb nicht möglich! Der persönliche Echtname unterstützt nur persönliche Abhebungen. Nach der Einreichung muss es vom übergeordneten Dienstleister überprüft werden. Erst nach bestandener Prüfung gilt der Echtname als erfolgreich.", "tCAlTVE55bteYRYT6gDbc": "Ausweis", "N9O3C4bv7mci7eE1eYJTp": "<PERSON>te geben Sie Ihre ID-Nummer ein", "IjrAT01SxjPX4qgbJDgmQ": "Die Informationsauthentifizierung wird derzeit nicht durchgeführt. Bitte fügen Sie sie zu<PERSON>t hinzu", "lDKrZEHddhAqje9P-dx2D": "nicht zertifiziert", "ACgMBoX8dES2ll2wNJlXu": "Zertifizierung läuft", "_6ZBaztE9i7WPTNBpU-JV": "Authentifizierung fehlgeschlagen", "NWB1XTgWnDZdVXApDMGP_": "Wird von einem erstklassigen Dienstleister überprüft", "eeLhrjq3zdRq8RmncZ7Re": "Die Überprüfung ist fehlgeschlagen", "f2J3ZgEdW0dBUMf-usvcc": "Zertifizierung", "Mbh9kHVbIMxc_aykJBsDr": "Rezertifizierung", "wPblFbX-4CmURcOYNxNPM": "Be<PERSON><PERSON><PERSON> an<PERSON>hen", "-UdcRA2f8GflwWkRatINL": "Überprüfen Sie die Bestellnummer", "KT8cy99eeIOwTPqi3IjgK": "Ausweisfoto", "2B9eM3k51NkPbpIJauFfj": "<PERSON><PERSON>gs<PERSON><PERSON>", "V5V_Z02EeDLZJVkjL5yB9": "Rezensent", "H_WXP1q8rO_zGjM7z2l0R": "Prüfungsgutachten", "DgQ6Po_e9_AEGCu1oDKXl": "Freigabestand", "KlZQCdiEceUY54vxcapV-": "<PERSON><PERSON><PERSON> rich<PERSON>ger Name", "r4G785OKstjScU_Nw3EzO": "Die Authentifizierung mit echtem Namen wird derzeit überprüft. <PERSON>te reichen Sie sie nicht erneut ein.", "GWiBNGprbSzdB2Z986MsC": "Die Authentifizierung mit echtem Namen wurde bestanden. <PERSON>te nicht erneut einreichen.", "oJu4xk2bHPcEt3j1zcnqU": "Bewerbungsstatus", "i-SpeLRCOCbLglqTGD0u-": "in Prüfung", "OUv3PKk3G_Cc_gdEyENKW": "Prüfung bestanden", "m9T-6rAOChrVMG0YDV8CF": "Prüfung nicht bestanden", "GrOaJg9APCHrf5k--_U_D": "passieren", "WuZQiTqcBj7cGod0nj7LO": "Sc<PERSON>itern", "-NuVs-wS7J4GYTOwjFuMh": "Rezensionskommentare sind erforderlich", "et_LYLHFrM04XhGjP46tP": "Bitte geben Sie das Account-Sharing-Verhältnis ein", "K-fRsoopgmoiVxQIwvZFl": "Das Account-Sharing-Verhältnis liegt nicht im verteilbaren Bereich", "lEttzd9lDWHeMmVtBmrNU": "Persönliche Echtnamen-Rezension", "QHCtboh3seN6ui33CZmWz": "bewältigen", "OLvc_EzZFYBlBhNIXnjZD": "Echter Name des Unternehmens", "Whfkt63jRh6VQ4s6QphEa": "Persönlicher richtiger Name", "sIND263kTy5EADi1pj1pP": "<PERSON><PERSON> a<PERSON><PERSON>ben", "specilUser-enduser": "<PERSON><PERSON><PERSON><PERSON>", "lhZ_M3uoptVK76DILnZ2l": "Die bestehenden apps sind jedoch nicht unbezahlbar", "regaliErrorTip": "Verlust des austragens. Das gerät ist auf die tabelle geladen", "bOBwT_f2edDC27PM3NQgy": "{0} Artikel ausgewählt", "Gad1mLkSTY0eVBR4fnhNI": "Zau<PERSON>list<PERSON>", "_uAM6l-rr2rggms5zdveJ": "Zaun ausgewählt", "AqxN0F8RcW0DD7-uAWTI5": "Kunde auswählen", "evYC9JPWCRXiYcDMbZvWx": "Persönliche <PERSON>", "NoFYSU79Nfj9dt-xcgrQ8": "Das Formularformat ist falsch. Bitte lesen Sie „Vorlagenformular“, um es erneut hochzuladen.", "xbF35iFGLVAJnswRVH0-u": "Datenzuordnung erfolgreich", "Bwg2kt_P1yhAb5-hjPOgO": "Anomalie der Iccid-Assoziation", "ek_N_GSAZAG9NyQXKbnJE": "Einige Datenzuordnungen sind abnormal. Weitere Informationen finden Sie im Download-Formular.", "GbKT46C8x66GgpjgBjOqn": "Die Dateigröße darf {0}{1} nicht überschreiten.", "SwOWzfqnAh0jCThFr4A6v": "{0} <PERSON><PERSON> ausgewählt", "4dGkAxc8S8l2COYbcDF-g": "<PERSON><PERSON><PERSON>", "s02oAL3-DrwNUsBNxn6Bw": "Um ein Scheitern der Zuordnung zu vermeiden, füllen Sie bitte IMEI und ICCID gemäß dem Musterformular aus. Es sind nicht mehr als 500 IMEI- und ICCID-Zuordnungen gleichzeitig zulässig.", "BMMOrNpblCgFpy0mjOb1Z": "Bitte laden Sie Dateien hoch. Es werden nur Dateien im XLS- und XLXS-Format unterstützt. Die Dateigröße beträgt ≤100 KB.", "c7A7o4pVd1rpivRvna5Gh": "IMEI-Chargenzuordnung ICCID", "4weboexar_U03Ho0q5Shn": "Die Datenzuordnung ist im Gange, bitte bedienen Sie die Seite nicht", "3JZI34eFq9qolsBRyHs7W": "Userdata_{0}.xls", "zdWLkJqxc7V4XODM1RAbn": "Wird geladen", "remoteControlTip": "Wenn die ferngesteuerte trennzeit entweder Leer Oder auf einen null-sortierhebel eingestellt ist, bedeutet dies die fortsetzung der unterbrechung", "remoteControlOne": "Fernsteuerung legend one.", "remoteControlTwo": "Fernsteuerung 2-4", "controlTime": "Zur kontrolle der zeit", "restore": "Zündung An", "registrationTime": "Zeit zum einchecken.", "equipmentService": "Kategorie ausrüstung, dienstleistungen", "softwareServices": "Das ist eine klasse Von software-dienstleistungen.", "deviceActivation": "Gruppe 1 aktiviert.", "setProfit": "<PERSON><PERSON><PERSON> sie den filter an", "packageIncluded": "Das ist auch ein paket.", "serviceAddTips": "Die folgenden anbieter werden entsprechend der eingereichten anträge aufgelistet?", "ZEGgtLzv8abXPKrlPdzUC": "Automatische Erneuerung", "ADC7SxVEyPAWy5cdn2lKb": "Nach der Änderung des Preises des automatischen Verlängerungspakets basieren nachfolgende Abzüge auf dem geänderten Preis. Änderung bestätigen?", "EFUKd-0PFs2kje_Z0c1h9": "warnen", "9G-UExFllfV8X9i978qOe": "Nachdem das automatische Verlängerungspaket aus den Regalen entfernt wurde, erfolgt kein A<PERSON>zug, wenn das aktuelle Paket des Benutzers abläuft. Möchten Sie es wirklich entfernen?", "gou": "<PERSON>nd", "mao": "<PERSON><PERSON>", "ucYI8ufupwygMJHhm1DQU": "Ruf die bullen an.", "desc": "Definiere es.", "driverRequired": "Name des fahrers - bitte füllen", "driverRFIDRequired": "Fahrer-nummer rfid-fid-nummer. Wählen sie den eintrag aus", "connectRequired": "Kontaktdaten - wählen sie eine vorgehensweise aus", "emailRequired": "E-mail - wählen sie den ausfüllen", "remarkRequired": "Die hälfte. Extras", "GrQZ6_ZOP74ym8FTQlT6z": "<PERSON><PERSON><PERSON><PERSON>, warte auf Gutschrift", "S6Pgt6-_Vw551atSMNdO9": "Transaktion fehlgeschlagen", "enoughTip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "Versionen.", "standardEdition": "Standard version.", "freeVersion": "Ausgabe gratis.", "ImportVersion": "I<PERSON>rt<PERSON><PERSON>.", "serviceCycle": "Der zyklen", "standardEditionTip": "Standard board (standard inaktiv)", "freeVersionTip": "<PERSON><PERSON><PERSON> anzeigen (lebenslänglich geöffnet)", "UpgradeBatch": "<PERSON>ch patrone zurückschicken.", "DeviceVersion": "Die laufwerke", "remove": "Entfernen.", "ServiceVersion": "In der dienstleistungsversion", "baseTip": "Die folgenden grundfunktionen sind zeitlebens offen", "nvx3Eu71rcjx9HzshOkfZ": "Import-export in einem jahr.", "3umrb4aXbfmzceFs9R4Lp": "Upgrade", "baseVipTip": "Zugriff in echtzeit, kreisum<PERSON>g, weitlaufwiedergabe (7 tage), ger<PERSON><PERSON>ordnung, erstellung einer ausstellung, alertanlage", "upResult": "<PERSON>s mich nachrüsten.", "upToastError": "Bitte fügen sie die ausrüstung hinzu", "runTimeAll": "Die fahrzeit beträgt insgesamt länger", "distanceAll": "Gesamtfahrmenge (privat)", "popularModels": "Das heiße modell.", "otherModels": "Anderes modell.", "6sWTyQZG3FPA7c6FT3VyC": "Luftfeuchtigkeitsstatistik", "b7e_Uxgecy2CNFWjkOaJG": "Diagramm zur Luftfeuchtigkeitsanalyse", "DWo-vUEFZ0QNf-S0qokc1": "Positionierungsstatus", "rnIm0BaHQUeveQZGjQ5-y": "Ob das festnetz effizient lokalisiert werden kann, zeigt an, ob externe elektronik angebunden ist", "x4U0kj1KZo88WNcFhVS-2": "Basisstation/GPS/WLAN/Bluetooth", "LgYLxSVSYDF0RSJiIbO6O": "Signalstärke", "4mc0BBksNeCOzN13HG2Kq": "ACC-Status", "vEYkN4a5nw7Mvi0ycE09T": "Start- und Endzeit", "accTotalTime": "ACC fightet immer lang", "VjZM92-uovWbEuTGQX-Qt": "Umplanen.", "ONfwsGz2suFI3faXq7qOq": "Teilweise erfolgreich.", "MGzpzjvhwPnlJ2_B_WuwB": "Zeit zurücksetzen.", "D18M1wrudyshM_m7yv-nZ": "Houston, hier houston.", "upgradeTip": "Wenn die version upgrade ist, können sie mehr funktionen testen! Nach upgrade wird die rückmeldung nicht unterstützt", "lycRATEE2FhUXs2E-l_NW": "Tonaufzeichnungen,", "q-mRStgBB2oIPAQj7EXBt": "<PERSON><PERSON><PERSON><PERSON>", "-SbPXykJz9GwiZN2raM0L": "löschen", "modelConfiguration": "<PERSON><PERSON> rich<PERSON>.", "modelManagement": "Management und modell.", "modelName": "Baujahr? - ja.", "category": "<PERSON><PERSON><PERSON>.", "myCompanyTip": "Bei typ typ unserer klasse kann der benutzer dieses modell bei der import-info vorrangig auswählen. Eine liste des modells erscheint als bevorzugte ausgabe eines beliebten modells und steht nur dem gewünschten benutzer zur verfügung", "companyModel": "Ich lasse dich arbeiten.", "baseTypeTip": "<PERSON><PERSON> modelltyp als vorlage angegeben ist, kann der benutzer dieses modell bei der import der ausrüstung als erste priorität festlegen und es steht allen nutzern offen", "baseType": "Das erste modell.", "modelId": "Identifikation nach modell.", "port": "Port.", "videoEquipment": "An videogerät.", "modelAlias": "<PERSON>rgen<PERSON><PERSON> spitznamen?", "originalModel": "Originalmodell.", "domainName": "Domains?", "generalAlarm": "Allgemeinen alert.", "超速报警": "Ich brauche einen einsatzwagen", "静止阈值": "Es ist die schwelle.", "离线报警": "Offline, bitte.", "ACC报警": "ACC, rufen sie 911.", "疲劳驾驶报警": "Müdigkeit beim fahren.", "怠速报警": "<PERSON><PERSON>en sie sofort die polizei!", "温度报警": "<PERSON>ert für verlogene temperatur (nur mit einem PC)", "低压报警": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "离线判断": "Offlinealarm konfigurieren", "录音": "Aufzeichnung.", "追踪(秒定)": "Ortung verfolgen", "温度感应器": "Unser thermostat reagiert auf alert", "监听(视频)": "Video (video) mitspielen.", "对讲(视频)": "<PERSON><PERSON> sprechen [video]", "油量设置": "Ich sehe das ö<PERSON>.", "车况": "Die wagenlage", "I/O设置": "<PERSON>e haben ein /O e<PERSON>t", "实时视频": "Live-Video", "视频回放": "Videowiedergabe", "companyModelMessage": "Dieses modell wird bei der import-import als erste gewählt. Eine liste des modells erscheint zunächst als bevorzugten modell und steht nur dem gewünschten benutzer zur verfügung", "baseTypeMessage": "<PERSON><PERSON> modelltyp als vorlage angegeben ist, kann der benutzer dieses modell bei der import der ausrüstung als erste priorität festlegen und es steht allen nutzern offen", "customModel": "Definiertes modell.", "timedTasksTip": "Die programmierte zeitaufgabe der haltevorrichtung wurde mit den folgenden ergebnissen abgeschlossen", "timedTasksToolTip": "Unterbrechung der ölzufuhr, automatische unterbrechung der ölzufuhr, ende der frist, unterbrechung des ölzufuhr, eintritt erfolgt", "设置结果": "Ergebnis: sitzung", "批量设置定时任务": "Die daten festlegen eine timeline", "定时任务设置": "Timer: gesetzt.", "执行类型": "Typ b:..?", "日期范围": "Datumsangaben genau.", "选择日期": "Das datum auswählen.", "时间范围": "Der zeitbereich.", "是否开启": "Aktivieren sie es.", "选择时间范围": "<PERSON><PERSON><PERSON>en sie den zeitlichen bereich.", "请选择执行类型": "Bitte geben sie den ausführungstyp an", "kHt0e3FCxBfFaaS8fs2o9": "Durch den zaun fuhr das elektrische öl", "QmAH9PaQGsFNjcHUM3n5d": "<PERSON><PERSON><PERSON><PERSON> in einen stromausfall", "eL2A3XxsMOLN0LfDsxexr": "Weg Von dem ausfall und in die unterbrechung des treibstoffs. Es kann nur eine wahl getroffen werden", "cNZ24bjJRWQo6Y-kbZs7V": "Falsche pin. Bitte wiederholen sie", "rzbvqLXmVrJcwetdcqj1N": "<PERSON><PERSON> ben<PERSON><PERSON><PERSON><PERSON><PERSON> definiert sich bereits", "vjQ5tSfzfzJZkLEG4pRuV": "<PERSON><PERSON> zu haustieren", "-QiINVEAEQs1lOyxoY2-U": "<PERSON><PERSON>", "V7ZEeKcoq4ZacqGumFBso": "Der name eines haustiers.", "EF_z9YeHVGT1BuC0Xxt9R": "Die familienadresse.", "BpPwm6MYFDChvmC_ZNEFm": "Der maximalen zeitdauer liegt bei höchstens sieben tagen", "0j5o0HN0PNLvrneCrbb43": "Anfangsprobleme.", "e-YOvR7f7jEgWnfpsPHIN": "<PERSON>, mein sohn", "1DjgiuZQ8BexU2yv9t_wp": "Letztes mal die Von spannung", "5JuYDTW-zrpUmNxJw_nSd": "Das basishaus darf nicht <PERSON> sein", "受限驾驶报警": "Nur für fahrer ist die polizei zuständig", "受限驾驶报警提示": "Akt<PERSON><PERSON><PERSON>, um einen Alarm auszulösen, wenn sich das Fahrzeug zu festgelegten Zeiten bewegt", "操作结果": "Operationliches ergebnis:", "sNuyqv3TTfegG8OtSl7nU": "Bitte wählen sie aus/geben sie das verzeichnis der servicepersonen ein", "7kCT4o4MOEYlu09rJb2Q6": "Das konto des bedieners teilen", "xUIxHiKX3S634h2ZT-Hei": "Bitte wählen sie das getrennte benutzerkonto für den einkauf Von dienstleistungen aus/geben sie diese ein", "dC7w9wyuS8ypXXErkJ-QS": "Ausweis des dienstleistungsanbietern", "-gm8iiFKWgzDomRd-mtiS": "<PERSON>.", "rmrECn8K92VTFKS9hwdkb": "<PERSON>n sie ge<PERSON><PERSON><PERSON> haben, verwenden die benutzer die rückmeldung und schieben die rückmeldung an das angegebene postfach weiter", "qzvBp0BIfI3fGjokogkL0": "Sie können mehrere emails mit symbolen e<PERSON>ben; <PERSON><PERSON><PERSON> sie.", "tQHLaMWzMo2JN4I2BZ1jm": "Feedback profil", "员工号Tip": "Die personalnummer - wählen sie den eintrag aus", "身份证": "Identifizierungsnummer. - höchste verschlüsselung", "群组名称Tip": "Gruppennamen - wählen sie einen ausgefüllten eintrag", "身份证号": "Dienstnummer, bitte.", "员工号": "Eine personalnummer?", "群组名称": "Nach gruppen.", "3000Tip": "Die größe der listengröße beträgt mehr als 3000. Bitte aktivieren sie die einstellung Oder exportieren sie die druckaufträge", "团队": "A-team.", "地区": "bezirks", "库存仓": "Die lagerhalle.", "团队Tip": "Die gruppe sollte gefüllt werden", "地区Tip": "Distrikt - es ist untertitelt", "库存仓Tip": "Lagerdepot - alle lieferungen", "确定删除吗": "Haben sie es wirklich gelöscht?", "防盗拾音提示": "<PERSON><PERSON> em<PERSON>, weniger als 30 tage zu wählen", "录音勾选提示": "Markieren sie die aufnahme, um dies durchzuführen", "录音提示": "Date<PERSON> der aufzeichnung, die nur als normale dateien heruntergeladen werden können", "防干扰报警": "Alarm wegen der einmischung.", "77_wwzNTTHafPEOC0QLk-": "Es stellte sich heraus, dass sie sich, mei, mit dem ursprünglichen passwort, einloggen,", "b3aU4VIlinEYLMqxOzRUw": "Um die weitergabe des laufwerks zu verhindern, müssen sie den code ändern! Wenn diese änderungen nicht zur unpünktlichkeit der informationen über das gerät führen, so übernimmt die plattform nicht die rechtliche verantwortung.", "zflDZ6jTRVCKTu5u3tpKp": "<PERSON><PERSON><PERSON> sie, um das passwort zu ändern", "8pEMx5x-VOk4MOLoRZRuv": "<PERSON><PERSON><PERSON>?", "aBF16On3BfsZNCUJ7A4yI": "Ein pilot?", "BDiR1RKwGe_Gkkjd42Suc": "<PERSON><PERSON><PERSON><PERSON>.", "aaRIAHINadX98uPLHcfWD": "In den kofferraum.", "OkTXgR5JsQCfmgiR-pPei": "ADSA", "0NL__z7IrwP4XRWDx9a8W": "DMS", "YpT7EDmLyZivmHVHmVfBV": "BSD", "2EYnIDOIt8Y1ZBMp4phrT": "Bitte wähle die provinz.", "06KhnrOrA4Bs_1vwHy2w2": "<PERSON><PERSON><PERSON>en sie den ort aus.", "KYOUEl9w_JqXiNDpynhBS": "Bitte wählen sie den bereich/die stadt", "timeType": "Das zeitformat.", "timeTypeTip": "<PERSON><PERSON><PERSON>, das zeitformat wird umgeschaltet und ist auf die gesamte system-zeitanzeige anwendbar", "24Hour": "<PERSON><PERSON>cht in 24 stunden.", "12Hour": "Gemacht für 12 stunden.", "1zn8t2V58hA4zxG_Y7jeu": "<PERSON>ü<PERSON>.", "s4Towo3SLRRMYC574wkLg": "<PERSON><PERSON><PERSON><PERSON>", "RDM65k10RlfRQk4fHEbjd": "Nach aktivierung gibt man einen alarmwert ein, der Von e<PERSON>m sodown ausgelöst wird, wenn die externe spannung die schwelle überschreitet.", "fCvirGBsZoJLXRJHHHZbd": "Bitte geben sie einen ganzzahligen wert ein", "RqtauiLCXvZ1erD4k81hD": "Der alarm darf nicht Leer ausgehen.", "取消成功": "Absagen. - habe ich.", "确定删除设备视频": "Das video wird sicher gel<PERSON>t?", "视频时间段": "Das ist eine zeitverzögerung.", "创建人": "<PERSON>ü<PERSON>.", "下载视频": "<PERSON><PERSON><PERSON> sie das video herunter.", "10分钟提示": "Maximale dauer zum herunterladen Per video: 10 minuten", "设备上传视频提示": "Das video zu uploadieren hat den SIM track gekostet. Bitte gehen sie vorsichtig damit um.", "上传中": "Ich rieche ihn hoch.", "请先查询设备回放列表": "Bitte überprüfen sie zuerst die liste des laufwerks zurückspulen", "取消失败": "Abbrechen abbrechen", "查看进度": "Stand der hammer!", "视频存证提示": "Das herunterladen Von webseiten ist erfolgreich. Bitte begeben sie sich später zum video cut und laden sie video dateien herunter!", "时间错误提示": "Die perfektion ist falsch.", "仅支持查询仅一个月的数据": "Unterstützung für die abfrage Von weniger als einem monat", "视频存证tip": "Video-chats (interne ausgabe) : cloud aufzeichnung des videos dauert einen monat nach erstellung des videos aus der cloud, bitte speichern sie es rechtzeitig zum herunterladen", "选填项": "<PERSON><PERSON><PERSON>en sie eine karte aus.", "必填项": "<PERSON><PERSON><PERSON> un<PERSON>.", "更多信息": "Mehr information (optionell)", "填写": "<PERSON><PERSON><PERSON> sie aus.", "yGQ6XcdO8Sy64ocojoYZl": "Audio-zeit: \"a propos\"", "94iJTFe_HpSBynofR58Ti": "Wie auch immer, es handelt sich um bahnlagerdi<PERSON>te", "fsYFvBFucZiu_oWbNvZ22": "Ein sammelruf für feedback", "油量总览": "Irgendwas muss ich doch gesehen haben", "偷油次数": "Du hast öl geklaut.", "偷油量": "<PERSON><PERSON>rit stehlen!", "偷油次数提示": "Die \"öleinstellungsstelle\" hat den \"gestohlenen ölalarm\" eingeschaltet, nachdem das", "偷油量提示": "Die ölausgleichsstelle hat den warnbereich für das diederdesaster geöffnet, indem sie den ausgangswert für das diederkennung festlegt", "总油耗": "Gesam<PERSON> ben<PERSON>.", "总加油量": "Commander commander", "总偷油量": "Das ganze benzin geklaut", "偷油总量": "Du hast alles geklaut.", "偷油报警": "\"<PERSON>ufe 911, wenn du\"", "油量报警值提示": "Wenn wir den alarmwert für eine kurze entsättigung festlegen, bei der die reaktionsgeschwindigkeit > alarm beträgt, löst das gerät alle 1min bei einem diebstahl aus", "总量": "Summe.", "加油": "Ihr seid fast da!", "偷油": "Das öl klauen.", "油量骤减": "Die ölmenge sinkt", "总行驶里程": "Reisemeilen meilen.", "油量明细": "Das öl ist hell", "油量事件": "<PERSON><PERSON>, das ist die menge.", "TXWIRMSEWbj4vtHr7DUJv": "Unterlabel!", "温湿度报警详情": "Schickt den sicherheitsdienst", "fsnMITM1wU8xV2PKzLWjZ": "3. Luftfeuchtigkeit", "O0qntBrpdv8l2SC3pG09l": "Die temperatureinschätzung der feuchtigkeit", "nz2_Ad5OYtByfOB1QF3Vl": "Und die schwellung der luft", "ObM5FY6uCuadK3X9CPiCl": "Bitte geben sie den namen des laufwerks ein", "NyEHBemDgp87PEzlnyLVc": "<PERSON>te geben sie den kosenamen kodiert /ID ein", "WT2DYWziS2YIrcleR2tGT": "Die (° C)", "ETgTh7hRkghCY7YNBf6I8": "Die (° C)", "2dy9zO3qN7emQTgGASZpk": "Die temperatur (° C)", "Uys4ymT9Nt1lR31ln20t_": "Durchschnittliche feuchtigkeit (%)", "aM_5tWEss1bMoV08rT5Dn": "Maximale feuchtigkeit (%)", "dVDmsVU8O1mAWZAWej8uF": "Minimale feuchtigkeit (%)", "ufw1tWMtXV3ffNamDz5xU": "Verängstigung der polizei.", "Fo0DGwbxnBAlVn0y2ck32": "Die polizei", "KUJ4j-738X9s_wvGrcaRX": "8. <PERSON><PERSON><PERSON> <PERSON> not<PERSON><PERSON>", "_-k0Jh2WNEm1YsmgIBnmd": "Die polizei war eine längere zeit unterwegs", "-F6HE4fAdtZPFvdc63MRN": "Der thermostat.", "QO0rjr3U04jQP_3aNJSJg": "Min: sehr kalt", "XghrPNgWiK990yE5fPxJ0": "Dann folgt die hypertemperatur", "uFtXUo1GQdbTBo5ngcwx0": "<PERSON><PERSON>, das war das ende der hypertemperatur", "lIYpSzkBAnha_7zYd70o4": "Das ist die adresse.", "fbT5E73inb0AGt14hJy4I": "B: superkräfte", "Fgh4tYRD97bN5_Q2AzTWT": "", "bSqSifScjo2TKle1XmMKS": "<PERSON><PERSON> geht rapide runter.", "3chnb5MVuzr2-XHeDkCxA": "Min: oh, min oh, gott!", "uGZs9lFwECt-z6ucB3Gj-": "B.", "l6q5r2x6VZq5zNJ91t7hi": "<PERSON><PERSON> geht rapide runter.", "EULZSW-P16RFs_3B-vk2x": "Temperatur (℃)", "PBPHCzoG8bgpY_K0XQs2v": "Feuchtigkeit (%)", "子标签": "Unterlabel!", "关联时间": "Zeit zur kontaktaufnahme.", "关联中": "<PERSON>be kontakte.", "子标签ID": "ID (ID) untergebene", "子标签名称": "Unterbezeichnungen wie folgt", "取消关联时间": "Organisiere die verbindung.", "标签关联记录": "Beschriftung, verknüpfung.", "电量": "motor", "导入修改": "Import modifikation", "更新标签": "Das brauchen sie täglich.", "批量设置温度报警": "Die grüße haben einen alarm ausgelöst", "关联记录": "Verbindung aufzunehmen.", "温度报警提示": "Die meldung geht durch, wenn {y: i} 0 {y: i} grad den temperaturanzeiger überschritten hat", "取消关联提示": "Bitte wählen sie, wenn die verwandten daten entfernt werden müssen", "清空设备提示": "Überprüfen sie die verbindungen der entseufzer", "是否确认取消关联": "Ob die verbindung aufgelöst wurde", "更新标签成功": "Aktualisierungen. Erfolgreich.", "子标签输入提示": "G<PERSON>en sie eine dezimal-id (höchstens 10) in zeile ein", "子标签空提示": "Nichts als Leer. Bitte geben sie einen unterfenster ein", "请勿输入重复值": "<PERSON>te geben sie keine wiederholwerte ein", "请输入正确值": "Bitte geben sie einen wert ein", "温度报警设置": "Alarm für die temperatur", "低温报警提示": "<PERSON><PERSON><PERSON>, daß die temperaturen. ≤ 20 ° C, die temperaturen - 123 ° C 20 ° C, und als temperatur, weiter 30min nach ausgelöst.;", "高温报警提示": "<PERSON><PERSON><PERSON>, daß die hitze. ≥ 20 ° C, die temperaturen 20 ° C in 127 ° C, und als temperatur, weiter 30min nach ausgelöst.;", "持续多久后产生报警": "Bis ich den alarm ausgelöst habe", "输入数值提示": "Bitte geben sie einen wert zwischen \"o\" und \"i\" ein", "不能大于180分钟": "Nicht mehr als 180 minuten", "高温报警值需大于低温报警值": "Der notruf bei hitze muss höher sein als der bei kälte", "标签更新失败": "Bitte aktualisieren sie eine seite Oder klicken sie auf das feld aktualisieren, um daten zu erhalten", "子标签温度报警": "Das unterfenster hat alarm", "XlMy7U3zd-o82bjczCtb3": "Extrem heiß.", "2yW2Lw-Pm4B3CHnUv6_RI": "Cryostasis.", "更新标签超时": "Das eingabefeld verlä<PERSON><PERSON> lassen, starten sie es erneut", "最大的范围不能超过三个月": "Maximal drei monate. - maximal drei monate", "指令下发失败": "Befehl fehlschlagen", "标签导入修改成功": "Label importiert erfolgreiche modifikation", "标签导入修改": "Fehlerhafter beschriftung der signatur importiert. Bitte erneut starten", "子标签取消关联成功": "Unterfenster abbrechen erfolgreiche verbindung beendet", "子标签取消关联失败": "Unterfenster abbrechen abbrechen abbrechen bitte starten sie es erneut", "标签更新成功": "Etikett wiederhergestellt", "取消删除": "Abbrechen und löschen", "取消并删除": "Streichen und entfernen der mission wird bestätigt", "取消并删除tip": "Löschen sie erfolgreich", "确认删除": "Die bestätigung der entfernung der mission bestätigt", "任务添加完成": "Mission hinzugefügt.", "批量下载任务": "Ein mengendownload.", "任务名称": "Name der mission?", "请前往任务列表查看": "Bitte lesen sie sich die einsatzliste durch", "执行时间": "Zeitpunkt der hinrichtung", "音频包数量": "Zahl der audio-ports", "任务ID": "ID für die mission.", "数量已超过15条": "Das Waren mehr als 15", "标签更新超时": "Bitte aktualisieren sie eine seite Oder klicken sie auf das feld aktualisieren, um daten zu erhalten", "请输入任务名称": "<PERSON>te geben sie den namen der mission ein", "名称不能超过20个字符": "Name darf nicht mehr als 20 zeichen haben", "音频": "Tonaufnahme.", "取消选中": "Löschen sie den auserwählten.", "是否确认取消选中": "Wird beim deaktivieren der karte bestätigt?", "已选中": "<PERSON><PERSON>m<PERSON>, herr.", "ACC开启": "ACC öffnen.", "ACC关闭": "ACC schlossen.", "nhLyAGMQLMPMAnlog-vFL": "<PERSON>.", "KOTAIifGVE4RneCwmKmqS": "Falls das gerät nach 5 minuten einen niedrigeren benzinwert meldet, schickt die plattform einen niedrigölalarm.", "低油报警": "Clay hat angerufen.", "X6lIcAfgOp1sXkdgE_9OW": "Bitte geben sie einen niedrigen ölwerte ein", "62qJdcSuv6iGqUcKFNdY3": "Es ist ein ganzzahliger wert für eine meldung", "低油报警值不可大于满箱油值": "Ein niedriges ölanzeigen darf den vollen ausgabe-wert nicht überschreiten", "套餐版本": "Und nur dann als paket.", "子音频名称": "Name der audiodatei", "子音频时间": "Audio-zeit: was bedeutet das überhaupt?", "合包记录": "Die geheime art der sammlung.", "3qycxyVXHpDIRbCj9IK8c": "Auch etwas zeit.", "uQX-Oo4jyWWmFhVUIFDZa": "Speichern und veröffentlichen", "9HvYA8H1k0s5oYPTo_TRz": "Die ankündigung kann nicht <PERSON> sein", "6mWY-LWd-3OLeGOmBOlvR": "<PERSON><PERSON><PERSON>en sie die sprache aus.", "EcwH1eTZ4h47qTr03bT2-": "G<PERSON>en sie den titel ein?", "M27dQ-Dpq2WkJc3ntw1gg": "Lässt sich der löscher feststellen", "Xv7QA7Z7u7lCYx4f7NmWs": "<PERSON><PERSON><PERSON>en sie den gewünschten zeitraum aus", "是否解除绑定": "Wurde die verbindung gelöst?", "以下设备不存在": "Die folgenden geräte sind nicht vorhanden und sind nicht neu", "新增设备": "Neues equipment.", "批量解绑": "Fessle es.", "默认": "Standard?", "瞬时速度": "<PERSON>u sie ab.", "机油压力计算": "Druckausgleich für motoröl", "节气门位置": "<PERSON><PERSON><PERSON><PERSON><PERSON> in position", "瞬时油耗": "<PERSON><PERSON> ben<PERSON> in einem augenblick", "空气流量": "Luftdurchfluss.", "发动机实时负载": "Der motor ist in echtzeit.", "算法校准": "Kalibrierung kalibriert.", "油量tab提示": "Die plattform arbeitet täglich in regelmäßigen abständen aus. Sie kann auch öldaten ermitteln, die \"kalibriert\" Oder \"nicht geeicht\" sind.", "油量tab提示Remark": "Anmerkung: bei der kalibrierung kann es einige abweichungen geben. <PERSON>te lassen sie eine auswertung in kombination mit dem tatsächlichen gebrauch durchführen", "油箱": "Den tank.", "关": ".", "gQI5MVNOUbZlMcC0kHzKK": "Bindungsstatus", "iVMYDWp97LRmLGrbbnnsD": "Gebunden", "Oeez9vYceazcpBOlH3Xq-": "Ungebunden", "油耗": "Energieverbrauch.", "请切换至任务列表下载文件": "<PERSON>te ändern sie die datei zum herunterladen der einsatzliste", "导出任务进行中": "Exportiert die mission", "网页过期": "Die verknüpfung wird nach eintritt Von null & gelöscht", "统计时间": "Statistische daten.", "ExtensionTip": "Exportiert werden kann man die felder der verschiedenen anwendungen gruppieren", "统计": "Statistik.", "总加油次数": "2o besuche?", "总加油量(L)": "<PERSON>in, leute!", "总偷油次数": "Du hast öl geklaut.", "总偷油量(L)": "Du hast alles geklaut.", "油量分析": "Analyse des ölgehalts", "设备名称不能为空": "Kein name des geräts ist Leer", "监控已结束": "Zugriff auf überwachung beendet zugriff auf die benutzerkennung fortsetzen", "唤醒失败": "Versuchen sie es später noch mal", "限速": "Tempolimits?", "距离": "Distanz.", "speedAlarmTip": "Wird der schwellenwert überschritten, löst das system den notruf bei überhöht und wird alle 1 minute wiederholt, wodurch der alarm ausgelöst wird, bis die geschwindigkeit unter dem schwellenwert sinkt und automatisch abschaltet", "speedAlarmToast": "Unterstützung nur für die abfrage der gestern und davor", "durationTip": "Das war eine glatte zahl", "riskLevel": "klassifizierung", "deviceTotal": "Gesamtzahl der anlagen", "deviceOnline": "Ein online-videogerät?", "riskEquipment": "Etwas gefährliches.", "highRiskEvents": "Es ist ein sehr gefährlicher tag.", "mediumRiskEvents": "Heute ist ein gefährliches ereignis", "lowRiskEvents": "\"Heute ein wenig riskant\"", "bigFullScreen": "Große überwachungskameras.", "notProcessed": "<PERSON>cht bepackt.", "processed": "<PERSON>ir<PERSON> g<PERSON>.", "resolved": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "lowRisk": "<PERSON><PERSON><PERSON> gering", "mediumRisk": "<PERSON><PERSON><PERSON> risiko.", "highRisk": "<PERSON><PERSON> risiko.", "batchProcessing": "In der menge.", "handleThing": "Bitte wählen sie die gewünschten daten aus", "handleContent": "In flaschen.", "handleType": "Wie wirst du", "handleResult": "Bearbeitung abgeschlossen.", "handleOk": "Kann es los<PERSON>hen?", "phoneNotify": "<PERSON><PERSON>.", "wxNotify": "<PERSON><PERSON>t hat angerufen.", "positives": "<PERSON><PERSON><PERSON><PERSON>?", "interaction": "Geräteinteraktion.", "processingContent": "Woraus ist das?", "exportData": "Etwas exportieren.", "exportError": "Exportiert nicht mehr", "exportSuccess": "Exportiert die daten. Sie können sie bei der aktuellen mission herunterladen", "EventTaskTip": "Aufgabe: die export-mission wird ausgeführt, wenn die export-tests in der haupt-asset - ausgabe ausgeführt werden. Die mission kann binnen sieben tagen abgelaufen sein. Bitte laden sie die daten rechtzeitig Oder exportieren sie sie erneut", "terminalContentTip": "Bitte geben sie mit dem gerät den inhalt ein und geben sie bis zu 200 zeichen ein", "bathHandleTip": "Maximale unterstützung für die eingabe Von 200 zeichen über 200 zeichen kann nicht weiter eingeben", "terminalContent": "Sprechen sie die nachrichten vor.", "terminalPlay": "Letzte verbindung zum terminal.", "processImmediately": "Veranlassen sie das.", "Vikgp6W99LW5DLi9_vpHo": "Das programm ist nicht abgeschlossen.", "fgRSP3L6Ua8B8bVBYLsMS": "Wir schalten jetzt die kamera ab", "unresolved": "<PERSON>s ist nicht geklärt.", "unresolvedTip": "Der vorfall ist gelöst, man kann nicht fortfahren", "highRiskTip": "Die bedrohung unserer existenz verlangt höchste priorität", "mediumRiskTip": "<PERSON><PERSON><PERSON>e gefahren erfordern rasches handeln", "lowRiskTip": "Frühwarnung vor potenziellen gefahren, die früher abgewendet werden kann", "realMonitoring": "G<PERSON>en sie mir die bilder!", "playContentSuccess": "Die übertragung wurde ein erfolg", "playContentError": "Das ansagen ist fehlgeschlagen. Bitte noch mal starten", "hJ_bvQO9HYG8ge-S_IDO-": "Die bildschirme sind ohne durchgang geöffnet", "AG-MTfxbowF8XTdMK8yYK": "Überwachung läuft.", "a64oSYjNagLD0Gg88CxK3": "Zugriff blockieren.", "yokKefCUCFgzYsRo9BnzS": "Die anlage wird geräumt", "q9r2Om1U7SO896v3LSVI0": "Überwachung abbrechen.", "_uJmjZE1r8GDjxEZyeh-U": "Wir machen eine simulation", "x0jvg0WV_R32p1KMHyujw": "<PERSON><PERSON> jemand and<PERSON>.", "8_hhY_1Tx0o0w7Y1wdCRb": "Die programmleitung hat die 9. <PERSON><PERSON><PERSON> ü<PERSON>ch<PERSON>ten, kann nicht weiter erweitert werden", "3JCIEcBT9HpfXFK0Fu_TN": "Ka<PERSON>as l<PERSON>.", "事件弹窗配置": "Die konfiguration des zeitfensters", "开启后Tip": "Wenn die markierungen aktiviert sind, wird eine warnung durch ein fenster angezeigt", "事件声音配置": "Ereignis-rauschen schen", "开启声音后Tip": "Eine erinnerung wird angezeigt, wenn ein ereignis erö<PERSON>net wurde", "事件任务Tip": "Exportiert die daten, so können sie sie in der video-mission ereigniskette herunterladen", "9FY6SGcGYrCwFEcACJ5BV": "Ein preis.", "m8MBU7gaNdNDQorXi7_92": "<PERSON>rg<PERSON><PERSON>en sie die beschreibung.", "FIgAdMCZbTjUXRp-BVY-9": "Zuerst alle einträge in * eingeben", "A-DnuXZN2arqL1ZwkHLQ5": "Widersprüchliche forderungen", "bjf4Pxo6-3enYjZX-8tyQ": "* das zielmöchten möchten, dass sie gleichzeitig einen maximalen zeitfenstern anzeigen, der größer als 1 ist", "ShF6ZmpbA1EijFMRApFIw": "* es gibt einen zeitkonflikt zwischen der aktuellen werbung und der bereits veröffentlichten werbung. Bitte ändern sie diese nur erneut", "TzYCTuYEh0mlda6hmKngc": "<PERSON><PERSON><PERSON><PERSON>, braver junge.", "_5ENuHSGjo9AOnh9VZMSm": "Neue werbung.", "D2vixOeMA0xifby0Pqek1": "Größe der anzeige:", "ck-6pjY9Q2p9eZYMfj3gz": "<PERSON><PERSON><PERSON> die werbung:", "sxNR6X6-YPM-rM3UYnGC_": "Name der werbung:", "fSSnMr-IMjus9Nm2qaloa": "<PERSON>en benutzer?", "7GGCLgtrmSuZ3yX7QKlUB": "Lage: zwei, drei, vier, drei, zwei, eins.", "Zw7bEnWCItRO3OZR4f41u": "Malaiische ausrüstung:", "s8ggwKHE3jM4A355doFBv": "<PERSON>r das ist noch nicht das letzte!", "mziSJbA1dzcUsPsiiLLBE": "Art der werbung:", "Q_XGjoL48fZZVJzBlyXW7": "Foto für werbung.", "levGypCyH-rXOx1mmg65m": "Begrenzter zugang:", "ND62G7W-ITK8yn7gYxUyU": "Maximale größe: 2Mb maximale format: JPG/PNG", "eVbyEIX4agI-jM3BK9Ief": "URL: bitte geben sie die adresse ein:", "vT48eh78S-pz3WROKQ9tn": "Bitte geben sie einen namen für die werbung ein", "XclqZtcCm0lDPV1UqYj3i": "Die länge darf nicht länger als 20 zeichen sein", "wZpmnbEoDIWkbpBtxW5gU": "<PERSON>te wählen sie einen werbeplatz aus", "d64gIF26q7WcJy1oOOKkL": "Bitte wählen sie die werbesaison aus", "lvS33ZKf9iiNf7JKvRJHO": "Bitte wählen sie die art der werbung aus", "Ae9AKKIZR7Z7EcvHe76aW": "Laden sie die werbefotos hoch", "zLoxHFKf86oWk8m9HKhm_": "Die liste der laufwerke ist fehlgeschlagen", "e7akjZcWeEbSamhM97F5v": "Bitte hochladen der bilder Von PPG Oder JPG. Gut", "wxzOyEnLiHt1bTxTUpCHM": "Bitte hochladen ≤ mb.", "BWhWUz0AlHOT-S0V0wK4i": "Die zahl der werbekunden lässt sich nicht speichern und veröffentlichen", "8cuHAuf6V9y1S0ktrujuE": "Erfolgreicher start.", "IobVQNnwBhgHS5pOxH4Vw": "Speichern und veröffentlichen, was erfolgreich ist", "S2DqHrLgq8j-ObZQL40qH": "Speichern und veröffentlichen", "MJzt0chtyypPJrJM2etk4": "<PERSON><PERSON> s<PERSON>", "GJ8mxuRz8wIpmKeftNX8V": "Die einzelheiten der werbung lassen sich nicht beschaffen", "dGvQSz_VHN0aEluf-2SYm": "Adresse (URL) : bitte geben sie die richtige adresse ein", "Bkq0Yczk7b865d7nN8M8W": "Name der werbung:", "k9HNUSJDi33dfFV3JFI0F": "Wird geladen.", "vt1MZfWXyMZzyFU7ZD6kc": "<PERSON><PERSON><PERSON>gen ohne ein bild.", "8Y9n6AMUzmJgLppRkiPqC": "<PERSON><PERSON><PERSON>.", "ybYXmIEwb2gFT3jw0NhiZ": "Logg dich aus.", "dRz9guMYbTUs-t4KKVMoI": "Ob sie die anzeige löschen", "FPiSGK9vmdXLttuGBLCNy": "Liste der anzeigen fehlgeschlagen", "-BryKeYUPdPBoc7NnBDjn": "<PERSON>rb<PERSON><PERSON>.", "QMXVAEUa1l0LSMMq1P-GE": "Ob ich die anzeige veröffentlichen soll", "9qwy42KyBhnFOtUfCP4L5": "<PERSON><PERSON>, er hat es veröffent<PERSON>t.", "p8YaGbXkOnRHfH73h6uwL": "Rückmeldung bestätigen. Werbeanzeige?", "WUHni97FVPSbV261NuNyM": "Rückzug erreicht.", "52qKTZxNp_787WjWjlGIw": "Den zurückgeworfen.", "Xflr8gmyOVSwPDsoDtigU": "Bestätigen sie ihre aktuelle anzeige.", "46L3Sa2n3_HRwD4n9EMdB": "<PERSON><PERSON> treffer.", "ta6lpPTi4NhVLI6x0xtqy": "Ausgeloggt? Negativ.", "4XmNWhQoR-eEcZdOzqJR9": "Der sortierwert muss zwischen 1-99 liegen", "jM1EyGoVHeSsOrb84SzPr": "Sortiert und gespeichert.", "QR-j-6cQyMmEYZUipjhag": "Sortiert nicht stattgefunden", "1jUjxmqgvba50alYQ9LBI": "ID der werbung", "qKT-47crN5dDtrd4hPLo_": "Name der werbung", "OD9HmFY35w2-KGjqj9mSV": "Lage: werbung.", "zlQECz3Ehrie3BAaW7pLa": "Art der werbung", "6IeAplUy8IVQGWPIsK9pY": "Da ist die anzeigenauflage.", "fVGJhvEZ-ZDSDhA0XchW_": "Ein werbespot.", "rM1Ai797icksN5452hvqh": "Bei einer sequenz.", "ZIlhD_KbnyMZiGVadFupW": "erneuerung", "D7J2uzbGR9NdUHMzsYJET": "<PERSON><PERSON><PERSON><PERSON>.", "uURJR8YrvL3Ha-nUf-EOH": "Und lass mich mitmachen.", "u4qG7qA0CreU6kKaHGVjc": "Bin online.", "sd5fGztwwaG_oY68aoZgd": "Bin offline.", "lc73xHWPc169AIGWpQWED": "(frau) voll besetzt.", "PV07lSEjTue8rcXVfQvZW": "Sämtliche handy-nutzer:", "HYB2ZWBIbSoGGHFRq_rlo": "<PERSON><PERSON><PERSON> aller geräte", "p0LuUDlOJamobXWFBmvwq": "Die titelseite.", "_0ruF2IV_hA9Zn4miCbgz": "Technisches gerät. Siehe detail.", "B3sJW7aZoEUiyVh30YerS": "Das war die letzte woche.", "ZoUjvBsJRkXXrvCva1pLY": "Der letzte monat", "-tjmITfzU3XeMoxOA-2N7": "Die letzten drei monate,", "JrWkSlLps5yku5MOyYKnU": "Englisch.", "_YIgMRgV6UMh4EHG2MJaE": "Sehr reich. Chinesisch.", "LNy00b-btNH2e2vSaDazn": "Spanisch. Danke.", "l5_XZj_k7myJXWJsiNRJh": "<PERSON><PERSON>.", "kHWK3fJ2TvdGRZXQrIcgC": "Russisch?", "616Gu11GhAUHLm6tzMB06": "Das ist Deutsch.", "TJ-4o27rDqFCGOPlOzqqa": "Französisch?", "BNtah_dKQVXGzDPA2no0n": "<PERSON><PERSON> aufs fenster.", "e0alpg_MbQSLh3XDGIzmi": "Banner", "hkY4dtYbrsma9O2lxwpp0": "Die letzten sieben tage,", "aXr65zaaIK8D8KzkjjUMx": "Bitte wählen sie den richtigen werbe-typ aus", "ipe0_s69Mr1o3M0-Vh_YQ": "Bildauslesen leider nicht möglich.", "v_1dniF62eoQTZy9tRtjP": "<PERSON><PERSON> auslesen nicht möglich", "CBX9YAaJlHGAGvxJjAX20": "<PERSON><PERSON>.", "cxagxiJXpUhAZ_HjQvqTX": "Operation abgebrochen", "1eoXePBAdeMp4mp4IXK97": "Wir wollen werbung machen.", "d0nknHU7xudZOANlToBfT": "kleinanzeigen", "F997Le_b3iMzyjviH1w_b": "Werbung zurück.", "4SdxzgE6mEOHAhf-kAa1i": "Details zu werbung", "S8KzvULqkjLUEUDggUP7O": "<PERSON><PERSON> Von der basis.", "sWZVMsKpDLm7Zo_TUC1zI": "Das ist eine datenanalyse.", "1ozcMqmPXf7ML4ahZPcz-": "Entfernen sie diese anzeige?", "ehydDkOY8K6pbQMdrYdvw": "Bestätigen sie den aufruf?", "TFWMEXmNyu3JWAhFrUI5c": "Checken sie die anzeige durch?", "9-KD5m4BVoNYl4cRLd00f": "Werden wir die anzeige auf version zurückbringen?", "tJI29weqgBdl7X8Q22xXm": "Tut mir leid, dass die werbung im moment noch nicht online ist", "eRqgRC5edGZp2h9L4B0Wx": "Statistischen termin: 0.30", "X0_QBixRCOYRGZyOIlRuw": "Guck doch mal in die stellenliste", "5n4RaESXtvz5GiDax_JZt": "Die werbung rechnet die besucher zusammen", "dwAD15TZTKz8D9Aeii6hE": "<PERSON><PERSON> daten einholen", "EmaS6irOhwdMjUdKb7kpt": "Status der werbung:", "oFuc9t_2oQw5HruLy8wPn": "ID: werbung", "Faq4yaX0d6aAiKJuLoqDy": "Art der ausrüstung:", "z1M9X6X4Jc_lcHUZPY2yo": "Eine freundin Von uns.", "Mxt4sLL2QJGL5YrRL4TOQ": "Erneuerung der person:", "EiJhbLu2h-Rx5MX_YXwHW": "Aktualisiert:", "Qq5z7_IdzCpASYspXX9xc": "Die anzahl der besucher berechnet sich", "fQKeYjglc9g0-0oWVktBZ": "In der einheit personen.", "alO9uZXdsebWQhFqe3vS2": "Die anzahl der werbebesucher", "n7TbeWPx6ERiuCLdi1xMo": "Wird wie ein browser ser ser", "UiI3Qz1DnMzuUOsVXVsFz": "Einheiten einheiten.", "1ykDVrd_FE2ioXA_6at7N": "Browser?!", "atBMWwI4wBA1fjp9rDMUL": "ID: werbung", "bmp6Q586E8NHP4TOjI9wy": "<PERSON>en benutzer?", "0PRcq7VkoU6VCeXMG6lv_": "Umfang der werbung:", "pTu4TJ_3imdKY9kGkrkN4": "Lage: zwei, drei, vier, drei, zwei, eins.", "OgP1t0z_sac6t8wWYfsip": "Status der werbung:", "uO5Hxlkc71BV8-Ipub1Lv": "<PERSON><PERSON><PERSON>en sie bitte den werbestatus", "LCpYvqukl9DTtyOJRGv5v": "Sie haben den vertrag noch nicht unterschrieben und können ihn nicht e<PERSON><PERSON>, bitte wenden sie sich an ihre vorgesetzten, damit sie den vertrag unterzeichnen.", "lmpTTGK79olDncBLWwHkE": "Nummer des vertrags?", "8QcKpYI9MVF-Y0PFV5nfI": "Bitte nimm die vertragsnummer ein", "xCxLzzOADKKHqh3RXgLz_": "<PERSON>ur buchstaben und zahlen", "NFsODE3FCSNwfl1kJhx4L": "Typ der dienstleister", "Kl8KHimuoV06FBms4UjzY": "<PERSON>te wählen sie die dienstart", "djmIMlcXzVVOAmDmvw_vQ": "Ökologie.", "hndIUBCh21ExxiafeHaPI": "Schnüffeln.", "LW0_DuWras1S6OvP-zQvd": "Namen, bitte?", "CScSIqDVa1M_YVWDKHimX": "Typ des service:", "5xesHLL5WSWphtJ7fjIKW": "<PERSON><PERSON><PERSON><PERSON><PERSON> pre<PERSON> (￥)", "tWpblUPkU3n357-hOUDWy": "Abscheidungspreis ($)"}