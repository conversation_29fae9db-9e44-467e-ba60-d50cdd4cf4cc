{"name": "swd", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build": "vue-cli-service build", "prettier": "prettier --write .", "upload_domestic": "node ./config/upload.js domestic", "upload_abroad": "node ./config/upload.js abroad", "upload_test": "node ./config/upload-test.js", "export_excel": "node ./config/translate/exportExcel.js", "import_excel": "node ./config/translate/importExcel.js", "translate": "node ./config/translate/index.js", "upload_avatar": "node ./config/oss/index.js"}, "dependencies": {"@wchbrad/vue-easy-tree": "^1.0.12", "axios": "^0.21.0", "clipboard": "^2.0.8", "core-js": "^3.6.5", "dayjs": "^1.11.9", "echarts": "^5.0.1", "echarts-gl": "^2.0.1", "ejs": "^3.1.9", "el-table-infinite-scroll": "^1.0.10", "element-ui": "^2.15.2", "fuse.js": "^6.4.6", "mapbox-gl": "^2.1.1", "maptalks": "^0.49.1", "maptalks.mapboxgl": "^0.3.3", "maptalks.markercluster": "^0.8.3", "node-ssh": "^13.1.0", "overlayscrollbars": "^1.13.1", "overlayscrollbars-vue": "^0.2.2", "path-to-regexp": "^6.2.0", "qrcodejs2": "0.0.2", "qs": "^6.9.4", "screenfull": "^5.1.0", "videojs-contrib-hls": "^5.14.1", "vue": "^2.6.11", "vue-cookies": "^1.7.4", "vue-echarts": "^5.0.0-beta.0", "vue-i18n": "^8.22.1", "vue-json-excel": "^0.3.0", "vue-jsonp": "^2.0.0", "vue-router": "^3.5.4", "vue-slider-component": "^3.2.11", "vue-video-player": "^5.0.2", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "x2js": "^3.4.1", "xlsx": "^0.17.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-prettier": "^6.0.0", "@vue/preload-webpack-plugin": "^2.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "crypto-js": "^4.2.0", "css-unicode-loader": "^1.0.3", "eslint": "^7.5.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "postcss-preset-env": "^9.3.0", "prettier": "^1.19.1", "sass": "^1.32.13", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "svg-sprite-loader": "^5.2.1", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.8.0", "zip-webpack-plugin": "^4.0.1"}, "engines": {"node": "14.16.0"}}