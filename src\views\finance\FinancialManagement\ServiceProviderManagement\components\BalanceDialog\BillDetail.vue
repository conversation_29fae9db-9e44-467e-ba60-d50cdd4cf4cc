<template>
  <div class="balance-detail-container">
    <el-form inline>
      <el-form-item :label="$t('lg.time') + ':'">
        <base-date-picker
          class="data-picker-wdth"
          v-model="form.timespan"
          type="datetimerange"
          range-separator="→"
          :start-placeholder="$t('XbFegIO6XdtwVCLj3tHjn')"
          :end-placeholder="$t('k3rb7GIYeArL-6QUB2jYR')"
          :default-time="['00:00:00', '23:59:59']"
          :format="systemDateFormat"
          prefix-icon="el-icon-date"
          :picker-options="pickerOptions"
        >
        </base-date-picker>
      </el-form-item>
      <!-- slect-input -->
      <el-form-item>
        <el-input :placeholder="$t('bzVAxsJEtQtawIDU5RBt1')" v-model.trim="form.orderOrPayVal" class="input-with-select" clearable>
          <el-select v-model="form.prependFilterType" slot="prepend">
            <el-option :label="$t('lcNVANxOy5jOngZ8n6BSU')" :value="1"></el-option>
            <el-option :label="$t('6zhGiJhw5vrsMXx3jSew5')" :value="2"></el-option>
          </el-select>
        </el-input>
      </el-form-item>
      <!-- input -->
      <el-form-item :label="$t('TlgmpSebIew_qw82Z7273') + ':'">
        <el-input :placeholder="$t('f8gnN4SjblDXQUgvZtnei')" clearable v-model.trim="form.imei"></el-input>
      </el-form-item>
      <el-form-item :label="'分账服务商账号' + ':'">
        <search-user
          ref="UserTree"
          :placeholder="'请选择/输入分账服务商账号'"
          :isCur="true"
          :size="'small'"
          :defaultAutoSelectUser="false"
          @autoSelectUser="getSelectOperateUser"
          :showSearch="false"
        ></search-user>
      </el-form-item>
      <el-form-item :label="$t('7HL8IXRIIp5cKUnInarno') + ':'">
        <el-select class="bill-status" v-model="form.status">
          <el-option v-for="item in statusArr" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="clickSearch">{{ $t('lg.query') }}</el-button>
        <el-button @click="handleReset">
          {{ $t('lg.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
    <div class="amount-container">
      <div class="provider">
        {{ $t('uOMU5cwmKPl8Qugv8cZpi') }}：<span>{{ info.name + '(' + info.serviceProviderAccount + ')' }}</span>
      </div>
      <div class="amount">
        {{ $t('tf9xHu3qD81AQzCR4i2Bh') }}：<span>{{ sum1 }}</span>
      </div>
      <div class="amount">
        {{ $t('zTlleNA-rGV7NcSvnohCO') }}：<span>{{ sum2 }}</span>
      </div>
      <div class="amount">
        {{ $t('91_1vPtPXMzqa7sdwnbWw') }}：<span>{{ sum3 }}</span>
      </div>
    </div>
    <!-- table -->
    <el-table
      fit
      ref="deviceTable"
      v-loading="dataLoading"
      :data="tableData.data"
      class="swd-table"
      :header-cell-style="{
        background: '#FAFAFA',
        color: 'rgb(0,0,0,0.85)',
        height: '54px'
      }"
      :height="tableData.height"
    >
      <el-table-column :label="$t('lg.serial')" width="70" align="left">
        <template slot-scope="scope">
          {{ (searchParams.pageNo - 1) * searchParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lcNVANxOy5jOngZ8n6BSU')" prop="id"> </el-table-column>
      <el-table-column :label="$t('S3i2lNWXrpS54EiZ_zVr5')" prop="amount" width="120"> </el-table-column>
      <el-table-column :label="$t('6zhGiJhw5vrsMXx3jSew5')" prop="orderId">
        <!-- <template slot-scope="{ row }">
          <span style="color: #3370ff;cursor: pointer" @click="OrderManage(row)">{{ row.orderId }}</span>
        </template> -->
      </el-table-column>
      <el-table-column :label="$t('uOMU5cwmKPl8Qugv8cZpi')" prop="serviceProviderName"> </el-table-column>
      <el-table-column :label="'分账服务商账号'" prop="profitServiceProviderName"> </el-table-column>
      <el-table-column :label="'分账服务商ID'" prop="profitServiceProviderId"> </el-table-column>
      <el-table-column :label="$t('TlgmpSebIew_qw82Z7273')" prop="imei"> </el-table-column>
      <el-table-column :label="$t('fkWmt4XVNoCEOqcqM53Vu')" prop="totalAmount" width="100"> </el-table-column>
      <el-table-column :label="$t('KgSLgX2QXc5P4ebGJhoCr')" prop="proportion" width="80"> </el-table-column>
      <el-table-column :label="$t('7HL8IXRIIp5cKUnInarno')" prop="status">
        <template slot-scope="{ row }">
          <span>{{ row.status === 0 ? $t('J0UYZYck8uhBvlodD6N4V') : $t('W7PEgcWDx2hes2lBqOX4u') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.time')" prop="createTime">
        <template slot-scope="{ row }">
          <span>{{ row.createTime | utcToLocalTime }}</span>
        </template>
      </el-table-column>
      <template slot="empty">
        <p class="empty-container">
          {{ dataLoading ? '' : $t('lg.noData') }}
        </p>
      </template>
    </el-table>
    <!-- pagination -->
    <div class="pagination">
      <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" style="float:right;" :loading="exportLoading" @click="downloadTableData">
          <svg-icon v-if="!exportLoading" icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip>
      <base-pagination
        :current-page.sync="searchParams.pageNo"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next"
        :total="tableData.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>
  </div>
</template>

<script>
import pickerOptionMixinx from '@/mixins/pickerOptions.js'
import { timeConvert, lastMonthNowDate } from '@/utils/date.js'
import dayjs from 'dayjs'
import { mapGetters, mapState } from 'vuex'
import SearchUser from '@/views/finance/FinancialManagement/PublicComponents/ClientSearch'

import { _profitSharingDetailSelectTotalAmount, _profitSharingDetailSelectPage, _profitSharingDetailExplore } from '@/api/order.js'
export default {
  mixins: [pickerOptionMixinx],
  components: { SearchUser },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {}
    },
    changeTabInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      sum1: '--',
      sum2: '--',
      sum3: '--',
      dataLoading: false,
      exportLoading: false,
      form: {
        timespan: [lastMonthNowDate(), dayjs().format('YYYY-MM-DD 23:59:59')],
        orderOrPayVal: '',
        prependFilterType: 1,
        imei: '',
        profitProviderId: '',
        status: -1
      },
      searchParams: {
        pageNo: 1,
        pageSize: 15
      },
      tableData: {
        data: [],
        total: 0,
        height: '480'
      },
      statusArr: [
        { label: this.$t('dashloadi-NdxYrTzYiTv'), value: -1 },
        { label: this.$t('J0UYZYck8uhBvlodD6N4V'), value: 0 },
        { label: this.$t('W7PEgcWDx2hes2lBqOX4u'), value: 1 }
      ]
    }
  },
  computed: {
    ...mapState({
      systemDateFormat: state => state.user.systemDateFormat
    }),
    ...mapGetters(['currencyParams']),
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  mounted() {
    // 带着单号跳转过来
    if (this.changeTabInfo.activeName === 'billDetail' && this.changeTabInfo.payId) {
      //带参跳转tab
      this.form = {
        timespan: [dayjs(timeConvert(dayjs(this.changeTabInfo.beginTime), 'local')).format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],
        orderOrPayVal: this.changeTabInfo.payId,
        prependFilterType: 1,
        imei: ''
      }
    }
    this.clickSearch()
  },
  methods: {
    // 获取设备服务商id
    getSelectOperateUser(user) {
      this.form.profitProviderId = user.id
    },
    clickSearch() {
      let params = {}
      if (this.form.prependFilterType == 1 && this.form.orderOrPayVal) {
        // 分账单号
        params.profitSharingId = this.form.orderOrPayVal
      } else if (this.form.prependFilterType == 2 && this.form.orderOrPayVal) {
        // 充值单号
        params.orderId = this.form.orderOrPayVal
      }
      if (this.form.imei) {
        params.imei = this.form.imei
      }
      if (this.form.profitProviderId) {
        params.profitProviderId = this.form.profitProviderId
      }
      if (this.form.status !== -1) {
        params.status = this.form.status
      }
      if (this.form.timespan) {
        params.beginTime = timeConvert(this.form.timespan[0])
        params.endTime = timeConvert(this.form.timespan[1])
      } else {
        params.beginTime = timeConvert(lastMonthNowDate())
        params.endTime = timeConvert(dayjs().format('YYYY-MM-DD 23:59:59'))
      }
      this.searchParams = { pageNo: 1, pageSize: 15, ...params }
      this.getTotal()
      this.getTableList()
    },
    handleReset() {
      this.form = {
        timespan: [lastMonthNowDate(), dayjs().format('YYYY-MM-DD 23:59:59')],
        orderOrPayVal: '',
        prependFilterType: 1,
        imei: '',
        profitProviderId: '',
        status: -1
      }
      this.$refs.UserTree.inputClear() // 重置设备服务商
      this.clickSearch()
    },
    // 获取合计金额
    async getTotal() {
      try {
        let { pageNo, pageSize, ...params } = this.searchParams
        let res = await _profitSharingDetailSelectTotalAmount({ profitProviderId: this.info.id, ...params, currency: this.currencyParams })
        if (res.ret === 1) {
          let obj1 = res.data.find(item => {
            return item.type === 1
          })
          this.sum1 = obj1?.amount || '--'
          let obj2 = res.data.find(item => {
            return item.type === 2
          })
          this.sum2 = obj2?.amount || '--'
          let obj3 = res.data.find(item => {
            return item.type === 3
          })
          this.sum3 = obj3?.amount || '--'
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 获取列表数据
    async getTableList() {
      this.dataLoading = true
      try {
        let res = await _profitSharingDetailSelectPage({
          profitProviderId: this.info.id,
          ...this.searchParams,
          // isWithdrawalReview: 0,
          currency: this.currencyParams
          // profitProviderId: this.info.id
          // serviceProviderId: '1659403011845259264'
          // serviceProviderId: this.$cookies.get('bisUserId')
        })
        if (res.ret === 1) {
          this.tableData.data = res.data.data || []
          this.tableData.total = res.data.total || 0
        } else {
          this.$message.error(res.msg)
        }
        this.dataLoading = false
      } catch (error) {
        this.dataLoading = false
        throw new Error(error)
      }
    },
    /* downloadExcel */
    async downloadTableData() {
      if (this.exportLoading === true) {
        return
      }
      this.exportLoading = true
      try {
        let params = JSON.parse(JSON.stringify({ profitProviderId: this.info.id, ...this.searchParams }))
        let res = await _profitSharingDetailExplore(params)
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
        this.exportLoading = false
      } catch (error) {
        this.exportLoading = false
        throw new Error(error)
      }
    },
    /* changeCurrentPage */
    handleCurrentChange(page) {
      this.searchParams.pageNo = page
      this.getTableList()
    },
    /* changeTableRowSize */
    handleSizeChange(size) {
      this.searchParams.pageNo = 1
      this.searchParams.pageSize = size
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.balance-detail-container {
  width: 100%;
  .el-form {
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-date-editor {
      width: 225px;
    }
    .el-input {
      width: 179px;
      box-sizing: border-box;
    }
    ::v-deep .bill-status.el-select {
      .el-input {
        width: 179px;
        box-sizing: border-box;
      }
    }
    .input-with-select {
      width: 279px;
      .el-select {
        // width: 100px;
      }
      .el-input__inner {
        width: 179px;
      }
    }
  }
  .amount-container {
    display: flex;
    line-height: 22px;
    margin-bottom: 10px;
    color: #262626;
    .amount {
      margin-right: 30px;
      span {
        color: #fb772b;
      }
    }
    .provider {
      margin-right: 30px;
    }
  }
  .el-table {
    width: 100% !important;
    ::v-deep .el-table__header,
    ::v-deep .el-table__body {
      width: 100% !important;
    }
  }
  ::v-deep .el-input-group__prepend {
    background-color: #fff;
    min-width: 60px;
    .el-input__inner {
      text-align: left;
      font-size: 14px;
      color: #000000;
      &:hover {
        color: #000000;
      }
    }
  }
  .pagination {
    height: 32px;
    padding-top: 10px;
    padding-bottom: 10px;
    text-align: center;
  }
}
</style>
