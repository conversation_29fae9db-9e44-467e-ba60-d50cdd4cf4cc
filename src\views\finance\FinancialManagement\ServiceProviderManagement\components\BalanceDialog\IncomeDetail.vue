<template>
  <div class="balance-detail-container">
    <el-form inline>
      <el-form-item :label="$t('lg.time') + ':'">
        <base-date-picker
          class="data-picker-wdth"
          v-model="form.timespan"
          type="datetimerange"
          range-separator="→"
          :start-placeholder="$t('XbFegIO6XdtwVCLj3tHjn')"
          :end-placeholder="$t('k3rb7GIYeArL-6QUB2jYR')"
          :default-time="['00:00:00', '23:59:59']"
          :format="systemDateFormat"
          prefix-icon="el-icon-date"
          :picker-options="pickerOptions"
        >
        </base-date-picker>
      </el-form-item>
      <el-form-item :label="$t('lg.type') + ':'">
        <el-select class="acc-query" v-model="form.tradeType">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('-qO1k-kyMJ78r-k7hvTFh') + ':'">
        <el-input :placeholder="$t('_N5YSHeYO9S0dkx3zF49M')" clearable v-model.trim="form.searchId"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="clickSearch">{{ $t('lg.query') }}</el-button>
        <el-button @click="handleReset">
          {{ $t('lg.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
    <div class="amount-container">
      <div class="amount">
        {{ $t('tnVXZ-wvzIO2qu-BR6Raa') }}：<span>{{ sum }}</span>
      </div>
      <div class="provider">
        {{ $t('uOMU5cwmKPl8Qugv8cZpi') }}：<span>{{ info.name + '(' + info.serviceProviderAccount + ')' }}</span>
      </div>
    </div>
    <!-- table -->
    <el-table
      fit
      ref="deviceTable"
      v-loading="dataLoading"
      :data="tableData.data"
      class="swd-table"
      :header-cell-style="{
        background: '#FAFAFA',
        color: 'rgb(0,0,0,0.85)',
        height: '54px'
      }"
      :height="480"
    >
      <el-table-column :label="$t('lg.serial')" align="left">
        <template slot-scope="scope">
          {{ (searchParams.pageNo - 1) * searchParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('tf9xHu3qD81AQzCR4i2Bh')" prop="amount"> </el-table-column>
      <el-table-column :label="$t('lg.type')" prop="tradeTypeStr">
        <!-- <template slot-scope="{ row }">
          <span>{{ row.tradeType | tradeTypeStr(te) }}</span>
        </template> -->
      </el-table-column>
      <el-table-column :label="$t('lg.balance')" prop="balance"> </el-table-column>
      <el-table-column :label="'订单编号'" prop="busTradeNo">
        <template slot-scope="{ row }">
          <span class="ljdw-color-blue cursor-point" v-if="row.busTradeNo" @click="toOrder(row)">{{ row.busTradeNo }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('-qO1k-kyMJ78r-k7hvTFh')" prop="payId">
        <template slot-scope="{ row }">
          <span v-if="(info.bisUserId === 1 && ![0, 2, 7].includes(row.tradeType)) || row.tradeType === 3">{{ row.payId }}</span>
          <span v-else style="color: #3370ff;cursor: pointer" @click="toDetailBill(row)">{{ row.payId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.time')" prop="createTime">
        <template slot-scope="{ row }">
          <span>{{ row.createTime | utcToLocalTime }}</span>
        </template>
      </el-table-column>
      <template slot="empty">
        <p class="empty-container">
          {{ dataLoading ? '' : $t('lg.noData') }}
        </p>
      </template>
    </el-table>
    <!-- pagination -->
    <div class="pagination">
      <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" :loading="exportLoading" style="float:right;" @click="downloadTableData">
          <svg-icon v-if="!exportLoading" icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip>
      <base-pagination
        :current-page.sync="searchParams.pageNo"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next"
        :total="tableData.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>
  </div>
</template>

<script>
import { timeConvert, lastMonthNowDate } from '@/utils/date.js'
import dayjs from 'dayjs'
import pickerOptionMixinx from '@/mixins/pickerOptions.js'
import { mapGetters, mapState } from 'vuex'

import { _getIncomeBalance, _getIncomeTableList, _exportIncomeTableList } from '@/api/order.js'
export default {
  mixins: [pickerOptionMixinx],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      sum: '--',
      dataLoading: false,
      exportLoading: false,
      form: {
        timespan: [lastMonthNowDate(), dayjs().format('YYYY-MM-DD 23:59:59')],
        tradeType: -1,
        searchId: ''
      },
      searchParams: {
        beginTime: timeConvert(lastMonthNowDate()),
        endTime: timeConvert(dayjs().format('YYYY-MM-DD 23:59:59')),
        pageNo: 1,
        pageSize: 15
      },
      typeOptions: [
        { key: 0, label: this.$t('dashloadi-NdxYrTzYiTv'), value: -1 },
        { key: 3, label: this.$t('lg.income') + '-' + this.$t('k6EP4_ZJK39lzc9AwkWvT'), value: 0 },
        { key: 4, label: this.$t('lg.income') + '-' + this.$t('S2TQ3uhLwMNpAtrlBHHLZ'), value: 2 },
        { key: 1, label: this.$t('lg.income') + '-' + this.$t('DZzQ1lUL4GRdGJWqYGSYd'), value: 5 },
        { key: 2, label: this.$t('lg.income') + '-' + this.$t('HrJ-Uh34Wr3BSUdYA4iZ4'), value: 6 },
        { key: 9, label: this.$t('lg.income') + '-' + this.$t('7lftycmlnIBPgSygUr96m'), value: 8 },
        { key: 5, label: this.$t('lg.pay') + '-' + this.$t('7lftycmlnIBPgSygUr96m'), value: 1 },
        { key: 6, label: this.$t('lg.pay') + '-' + this.$t('HrJ-Uh34Wr3BSUdYA4iZ4'), value: 4 },
        { key: 7, label: this.$t('lg.pay') + '-' + this.$t('LTDA3b7Un6GgIPY4leo_H'), value: 3 },
        { key: 8, label: this.$t('lg.pay') + '-' + this.$t('S2TQ3uhLwMNpAtrlBHHLZ'), value: 7 },
        { key: 23, label: '支出-退款', value: 23 }
      ],
      tableData: {
        data: [],
        total: 0,
        height: '440'
      }
    }
  },
  computed: {
    ...mapState({
      systemDateFormat: state => state.user.systemDateFormat
    }),
    ...mapGetters(['currencyParams', 'userInfo']),
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  filters: {
    tradeTypeStr(val, te) {
      const statusMap = {
        0: te('lg.income') + '-' + te('k6EP4_ZJK39lzc9AwkWvT'),
        1: te('lg.pay') + '-' + te('7lftycmlnIBPgSygUr96m'),
        2: te('lg.income') + '-' + te('S2TQ3uhLwMNpAtrlBHHLZ'),
        3: te('lg.pay') + '-' + te('LTDA3b7Un6GgIPY4leo_H'),
        4: te('lg.pay') + '-' + te('HrJ-Uh34Wr3BSUdYA4iZ4'),
        5: te('lg.income') + '-' + te('DZzQ1lUL4GRdGJWqYGSYd'),
        6: te('lg.income') + '-' + te('HrJ-Uh34Wr3BSUdYA4iZ4'),
        7: te('lg.pay') + '-' + te('S2TQ3uhLwMNpAtrlBHHLZ'),
        8: te('lg.income') + '-' + te('7lftycmlnIBPgSygUr96m')
      }
      return statusMap[val] || ''
    }
  },
  mounted() {
    this.getIncomeBalance()
    this.getIncomeTableList()
  },
  methods: {
    clickSearch() {
      let params = {}
      if (this.form.tradeType !== -1) {
        params.tradeType = this.form.tradeType
      }
      if (this.form.searchId) {
        params.searchId = this.form.searchId
      }
      if (this.form.timespan) {
        params.beginTime = timeConvert(this.form.timespan[0])
        params.endTime = timeConvert(this.form.timespan[1])
      } else {
        params.beginTime = timeConvert(lastMonthNowDate())
        params.endTime = timeConvert(dayjs().format('YYYY-MM-DD 23:59:59'))
      }
      this.searchParams = { pageNo: 1, pageSize: 15, ...params }
      this.getIncomeBalance()
      this.getIncomeTableList()
    },
    handleReset() {
      this.form = {
        timespan: [lastMonthNowDate(), dayjs().format('YYYY-MM-DD 23:59:59')],
        tradeType: -1,
        searchId: ''
      }
      this.clickSearch()
    },
    // 获取合计金额
    async getIncomeBalance() {
      try {
        let { pageNo, pageSize, ...params } = this.searchParams
        let res = await _getIncomeBalance({ ...params, bisUserId: this.info.bisUserId })
        if (res.ret === 1) {
          this.sum = res.data || '--'
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 获取列表数据
    async getIncomeTableList() {
      this.dataLoading = true
      try {
        let res = await _getIncomeTableList({
          ...this.searchParams,
          currency: this.currencyParams,
          // serviceProviderId: '1659403011845259264'
          bisUserId: this.info.bisUserId
        })
        if (res.ret === 1) {
          this.tableData.data = res.data.data || []
          this.tableData.total = res.data.total || 0
        } else {
          this.$message.error(res.msg)
        }
        this.dataLoading = false
      } catch (error) {
        this.dataLoading = false
        throw new Error(error)
      }
    },
    /* downloadExcel */
    async downloadTableData() {
      if (this.exportLoading === true) {
        return
      }
      this.exportLoading = true
      try {
        let params = JSON.parse(JSON.stringify({ ...this.searchParams, bisUserId: this.info.bisUserId }))
        let res = await _exportIncomeTableList(params)
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
        this.exportLoading = false
      } catch (error) {
        this.exportLoading = false
        throw new Error(error)
      }
    },
    // 跳转明细和分账详单
    toDetailBill(row) {
      let { payId } = row
      if ([0, 2, 7].includes(row.tradeType)) {
        this.$emit('changeTab', { activeName: 'billDetail', payId, beginTime: new Date(row.createTime).Format('yyyy-MM-dd 00:00:00') })
      } else {
        this.$emit('changeTab', { activeName: 'withrawalDetail', payId, beginTime: row.createTime })
      }
    },
    toOrder(row) {
      this.$emit('toOrder', { activeName: 'third', busTradeNo: row.busTradeNo, beginTime: row.createTime })
      this.$emit('close')
    },
    /* changeCurrentPage */
    handleCurrentChange(page) {
      this.searchParams.pageNo = page
      this.getIncomeTableList()
    },
    /* changeTableRowSize */
    handleSizeChange(size) {
      this.searchParams.pageNo = 1
      this.searchParams.pageSize = size
      this.getIncomeTableList()
    },
    // 翻译函数
    te(arg) {
      const hasKey = this.$t(arg)
      if (hasKey) {
        const result = this.$t(arg)
        return result
      }
      return arg
    }
  }
}
</script>

<style lang="scss" scoped>
.balance-detail-container {
  .el-form {
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-date-editor {
      width: 225px;
    }
    .el-input {
      width: 179px;
    }
    .el-select {
      width: 142px;
    }
  }
  .amount-container {
    display: flex;
    line-height: 22px;
    margin-bottom: 10px;
    color: #262626;
    .amount {
      margin-right: 30px;
      span {
        color: #fb772b;
      }
    }
  }
  .el-table {
    width: 100% !important;
    ::v-deep .el-table__header,
    ::v-deep .el-table__body {
      width: 100% !important;
    }
  }
  .pagination {
    height: 32px;
    padding-top: 10px;
    padding-bottom: 10px;
    text-align: center;
  }
}
</style>
