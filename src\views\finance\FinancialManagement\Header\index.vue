<template>
  <header class="box" :class="{ min: !configObj.showAdminOrProviderAmount }">
    <div class="left" v-if="configObj.showAdminOrProviderAmount">
      <div class="item">
        <div class="img-bg">
          <img src="@/assets/img/FinancialManagement.png" alt="" />
        </div>
        <span style="margin: 0 14px">{{ $t('NC0GvIUNGMIckA487qi_W') }}:</span>
        <span style="color: #336FFF; font-size: 24px">{{ currencySymbol }}{{ balance }}</span>
      </div>
      <div class="item">
        <span>{{ $t('0DzF5hCURmG4E1XvrInUx') }}：</span>
        <el-tooltip placement="top" v-if="availableBalance < 0">
          <div slot="content" style="width: 200px">
            <span>您的账户因有退款订单扣除，现可用余额为负数</span>
          </div>
          <img src="@/assets/img/tip1.png" alt="" />
        </el-tooltip>
        <span :style="{ color: availableBalance < 0 ? 'red' : '#336FFF', fontSize: '24px' }">{{ currencySymbol }}{{ availableBalance }}</span>
      </div>
      <div class="item">
        <span>{{ $t('zTlleNA-rGV7NcSvnohCO') }}：</span>
        <el-tooltip placement="top">
          <div slot="content" style="width: 200px">
            <span>{{ $t('8cpgA7arN32LnJYYoHs6b') }}</span>
          </div>
          <img src="@/assets/img/tip1.png" alt="" />
        </el-tooltip>
        <!-- <el-popover placement="top" trigger="click">
          <div style="width: 200px">
            <span>{{ $t('UpktOagmkL4g9LVa3s4DI') }}</span>
          </div>
          <img slot="reference" style="cursor: pointer;" src="@/assets/img/tip1.png" alt="" />
        </el-popover> -->
        <span style="margin-left: 7px;font-size: 18px;cursor: pointer;" @click="showDetail">{{ currencySymbol }}{{ freezeBalance }}</span>
      </div>
      <div class="right-op" v-if="userInfo.userType == 8">
        <div class="btn">
          <el-button type="primary" @click="getServiceInfo">{{ $t('j3-S-EHwypDM9f764RfWK') }}</el-button>
        </div>
      </div>
    </div>
    <div class="right">
      <el-button
        type="primary"
        v-if="permissionArr.includes('finance:finance_manage:package_add') || [1681, 8108, 18060, 44242].indexOf(+userInfo.userId) !== -1"
        @click="handleOpenDialog(1)"
        >{{ $t('o7LB26olpIL6MYrh3AEkB') }}</el-button
      >
      <el-button
        type="primary"
        v-if="
          permissionArr.includes('finance:finance_manage:device_import') ||
            [1681, 8108, 18060, 18118, 43736, 16288, 17808, 44274, 44273, 44832].indexOf(+userInfo.userId) !== -1
        "
        @click="handleOpenDialog(2)"
        >{{ $t('lg.logDict.import') }}</el-button
      >
      <el-button
        type="primary"
        icon="reset"
        color="#ffffff"
        v-if="permissionArr.includes('finance:finance_manage:device_reset') || [1681, 8108, 18060, 18118, 44242, 44665].indexOf(+userInfo.userId) !== -1"
        @click="handleOpenDialog(3)"
        >{{ $t('lg.reset') }}</el-button
      >
    </div>
    <!-- 实名 -->
    <Authentification v-if="authVisible" :activeTab="activeTab" :visible.sync="authVisible" />
    <!-- 冻结金额详细 -->
    <Detail :balanceInfo="balanceInfo" :visible.sync="detailVisible"></Detail>
    <!-- 提现弹窗 -->
    <WithrawDialog :visible.sync="withdrawVisible" :enterpriseInfo="enterpriseInfo" :personalInfo="personalInfo" @success="withdrawSuccess" />
    <!-- 未签署合同弹窗 -->
    <el-dialog :visible.sync="showNoContractDialog" width="360px" :show-close="false" custom-class="no-contract-dialog" center>
      <div style="text-align:center;line-height:28px;font-size:16px;color:#333;">
        {{ $t('LCpYvqukl9DTtyOJRGv5v') }}
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="width:100px;" @click="showNoContractDialog = false">{{ $t('TzYCTuYEh0mlda6hmKngc') }}</el-button>
      </span>
    </el-dialog>
  </header>
</template>

<script>
import { mapGetters } from 'vuex'
import { _getAccountBalance, _getServiceProviderInfo, _getFreezeAccountBalanceDetail } from '@/api/order.js'
import Detail from './Detail.vue'
export default {
  components: {
    Detail,
    Authentification: () => import('@/views/Dashboard/components/Authentification.vue'),
    WithrawDialog: () => import('@/views/finance/FinancialManagement/Withraw/Index.vue')
  },
  inject: ['configObj'],
  data() {
    return {
      balance: '',
      availableBalance: '',
      freezeBalance: '',
      permissionArr: [],
      authVisible: false,
      detailVisible: false,
      balanceInfo: {},
      activeTab: '',
      enterpriseInfo: {},
      personalInfo: {},
      withdrawVisible: false, //提现弹窗
      showNoContractDialog: false // 未签署合同弹窗
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'currencySymbol', 'currencyParams', 'permissions'])
  },
  mounted() {
    this.getAccountBalance()
  },
  methods: {
    async getAccountBalance() {
      let res = await _getAccountBalance({ currency: this.currencyParams })
      if (res.ret == 1) {
        this.balance = res.data.balance
        this.availableBalance = res.data.availableBalance
        this.freezeBalance = res.data.freezeBalance
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开弹窗
    handleOpenDialog(type) {
      this.$emit('open', type)
    },
    // 获取实名认证信息
    async getServiceInfo() {
      let res = await _getServiceProviderInfo({ serviceProviderId: this.$cookies.get('bisUserId') })
      // const { contractNumber, isNew } = res.data[0]
      const contractNumber = res.data.find(item => item.constractNumber)?.constractNumber || ''
      const isNew = res.data.find(item => item.isNew)?.isNew || ''
      if (res.ret == 1 && res.data && res.data.length) {
        //status 认证状态 0.未认证 1.认证中 2.已认证 3.认证失败
        this.enterpriseInfo = res.data.find(item => item.type === 1) || { status: 0 }
        this.personalInfo = res.data.find(item => item.type === 0) || { status: 0 }

        /**
         * 点击提现时，若该账号未实名，则弹出企业实名提示窗
          点击提现时，若该账号已企业实名，则弹窗tab为企业提现
          点击提现时，若该账号仅个人实名，则弹窗tab为个人提现
          点击提现时，若该账号未企业实名，个人实名还未通过，则不弹出该弹窗，直接提示"个人实名认证暂未通过，请联系上级服务商"
          点击提现时，若该账号未企业实名，个人实名认证失败，则弹出个人实名提示窗
          点击提现时，若该企业是2.7.2版本改动前（引入了合同功能）的旧服务商，则不校验是否有合同编号
          点击提现时，若该企业是2.7.2版本改动后（引入了合同功能）的新服务商，则校验是否有合同编号弹窗提示先录入合同编号
         * **/
        if (this.enterpriseInfo.status === 0 && this.personalInfo.status === 1) {
          this.$message.error(this.$t('iZ6Q-Cqe20id8Jqqi-n28'))
          return
        } else if (this.enterpriseInfo.status === 0 && this.personalInfo.status === 3) {
          this.confirmRealNameAuth('personal')
          return
        } else if (isNew && !contractNumber) {
          // 新服务商且没合同编号则弹窗，旧服务商没直接放行
          //弹窗显示
          this.showNoContractDialog = true
          return
        }
        //提现弹窗
        this.withdrawVisible = true
      } else {
        //接口返回空,全部未认证
        this.enterpriseInfo = { status: 0 }
        this.personalInfo = { status: 0 }
        this.confirmRealNameAuth('enterprise')
      }
    },
    confirmRealNameAuth(activeTab) {
      let typeName = activeTab === 'enterprise' ? this.$t('7R8ninLk0Y8vd9nEBvIPz') : this.$t('oYXW0p5J3DkyunUzLbpq3')
      this.$confirm(this.$t('1xXJa_y9Dj73JgvFgLmpY', [typeName, typeName]), this.$t('lg.notification'), {
        confirmButtonText: this.$t('IpRD_Ggq8UhJWAyYyRTo4'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      }).then(() => {
        this.activeTab = activeTab
        this.authVisible = true
      })
    },
    async showDetail() {
      const res = await _getFreezeAccountBalanceDetail({ currency: this.currencyParams })
      this.balanceInfo = res.data
      this.detailVisible = true
    },
    withdrawSuccess() {
      this.withdrawVisible = false
      this.$emit('withdrawSuccess')
    }
  },
  created() {
    this.permissionArr = this.permissions
      .filter(item => {
        return item.type === 2
      })
      .map(item => item.perms)
  }
}
</script>

<style lang="scss" scoped>
header.box {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  height: 106px;
  background: #ffffff;
  margin-bottom: 16px;
  padding-right: 20px;
  border-radius: $containerRadius;
  &.min {
    height: 53px;
  }
}
.left {
  display: flex;
  align-items: center;
  height: 92px;
  .item {
    height: 38px;
    border-right: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding: 0 42px;
    font-size: 14px;
    flex-shrink: 0;
  }
  .img-bg {
    width: 54px;
    height: 54px;
    background-color: #e1eaff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 24px;
      height: 22px;
    }
  }
  .right-op {
    .btn {
      margin: 0 20px 0 80px;
    }
    .charge {
      display: flex;
      .reference-slot {
        display: flex;
        align-items: center;
        margin-right: 15px;
        cursor: pointer;
        .circle {
          width: 13px;
          height: 13px;
          border: 1px solid #dcdfe6;
          border-radius: 50%;
          margin-right: 5px;
          &.active {
            background: radial-gradient(#fff 0%, #fff 30%, #3370ff 40%, #2656c5 100%);
          }
        }
        img {
          margin-right: 4px;
        }
      }
    }
  }
}
.right {
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-left: 20px;
}
.no-contract-dialog {
  border-radius: 10px !important;

  .el-dialog__body {
    padding: 32px 24px 16px 24px !important;
  }
  .el-dialog__footer {
    text-align: center !important;
    padding-bottom: 24px !important;
  }
}
</style>
