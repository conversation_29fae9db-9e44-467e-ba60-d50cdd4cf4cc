<template>
  <div class="table-container">
    <el-table v-loading="loading" :data="tableData" height="calc(100% - 52px)">
      <el-table-column prop="packId" :label="$t('883ApjJAhE8Rjotd5jXrT')"></el-table-column>
      <el-table-column prop="packName" :label="$t('uigvo2391weT6pPqyfxYg')">
        <template slot-scope="{ row }">
          <span style="color: #3370ff;cursor: pointer" @click="showDetailDialog(row, 1)">{{ row.packName }}</span>
        </template></el-table-column
      >
      <el-table-column prop="packType" :label="$t('qzwEjrvjWERzbbwcvI5rK')">
        <template slot-scope="{ row }">
          <span>{{ getCurrentPackageType(row.packType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="wirelessType" :label="$t('2247BJLuV1ixtjGA1O7gV')">
        <template slot-scope="{ row }">
          <span>{{ row.wirelessType | applyTypeFilterVer3 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="wirelessType" :label="$t('vF3kO35MlJqcY7R7BojED')">
        <template slot-scope="{ row }">
          <span>{{ row.universal | universeTypeFilterVer3 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="standard" :label="$t('0gTRzqUCLGERAfFhL-8y3')">
        <template slot-scope="{ row }">
          <span>{{ row.standard | standardTypeFilterVer3 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="primePrice" :label="currencyParams == 'CNY' ? $t('tf0jk5t-3-we9pamkVaB8') : $t('xOo6bW69d4UFSs5A8Lc6t')"> </el-table-column>
      <el-table-column prop="guidingPrice" :label="currencyParams == 'CNY' ? $t('hqZrtrvlMrwFhVBJDoK9d') : $t('OFM2oOsQ2x9IwEF2ttv_b')"> </el-table-column>
      <el-table-column prop="category" :label="$t('_CXEjvnybYTxiZPXdJknx')">
        <template slot-scope="{ row }">
          <span>{{ row.packStatus | packStatusFilterVer3 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" :label="$t('Zbkw-Z-GkLdk92q2GqQb9')">
        <template slot-scope="{ row }">
          <span>{{ row.startTime | UTCTimeToLocalTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" :label="$t('tjh3v6c9w4iWKRhEsxCel')">
        <template slot-scope="{ row }">
          <span>{{ row.endTime | UTCTimeToLocalTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="handle" :label="$t('lg.operator')">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" @click="showDetailDialog(row, 2)">{{ $t('lg.edit') }}</span>
          <span class="swd-table-cell-btn" @click="confirmDelete(row)">{{ $t('lg.delete') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="swd-pagination-container">
      <!-- 分页 -->
      <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" class="swd-download-btn" @click="emitExport">
          <svg-icon icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip>
      <base-pagination
        :current-page.sync="searchParams.pageIndex"
        :page-sizes="[15, 25, 35]"
        :page-size="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { _getPackageSettingList, _deletePackageSetting, _exportPackageSettingList, getPackageClassify } from '@/api/order.js'
export default {
  data() {
    return {
      detailVisible: false,
      loading: false,
      tableData: [],
      total: 0,
      searchParams: {
        pageIndex: 1,
        pageSize: 15
      },
      packageTypeList: []
    }
  },
  computed: {
    ...mapGetters(['currencyParams'])
  },
  created() {
    this.getPackageClassifyList()
  },
  methods: {
    search(info) {
      this.searchParams = { ...{ pageIndex: 1, pageSize: 15 }, ...info }

      this.getPackageSettingList()
    },
    async getPackageSettingList() {
      this.loading = true
      try {
        let res = await _getPackageSettingList(this.searchParams)
        if (res.ret === 1) {
          this.tableData = res.data || []
          this.total = res.total || 0
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.loading = false
        throw new Error(error)
      } finally {
        this.loading = false
      }
    },
    // 打开套餐详情弹窗
    showDetailDialog(row, type) {
      this.$emit('showDetailDialog', { type, info: row, visible: true })
    },
    // 点击删除
    confirmDelete(row) {
      this.$confirm(this.$t('fPpUDmYmZBfijNxuYo-dT'), this.$t('lg.notification'), {
        confirmButtonText: this.$t('lg.confirm'),
        cancelButtonText: this.$t('lg.cancel'),
        type: 'warning'
      }).then(() => {
        this.deletePackageProxy(row.packId)
      })
    },
    // 删除套餐代理
    async deletePackageProxy(packId) {
      try {
        let res = await _deletePackageSetting({ packId })
        if (res.ret === 1) {
          this.$message.success(this.$t('cvyH4iJuWCiLfB_Wsn4Nn'))
          this.getPackageSettingList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 导出
    emitExport() {
      if (this.tableData.length == 0) {
        this.$message.warning(this.$t('lg.noData'))
        return
      }
      let { pageIndex, pageSize, ...param } = this.searchParams
      _exportPackageSettingList(param).then(res => {
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel;charset=UTF-8' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
      })
    },
    handleCurrentChange(page) {
      this.searchParams.pageIndex = page
      this.getPackageSettingList()
    },
    handleSizeChange(pageSize) {
      this.searchParams.pageSize = pageSize
      this.getPackageSettingList()
    },
    // 翻译函数
    te(arg) {
      const hasKey = this.$t(arg)
      if (hasKey) {
        const result = this.$t(arg)
        return result
      }
      return arg
    },
    async getPackageClassifyList() {
      const result = await getPackageClassify()
      this.packageTypeList = result.data.reduce((pre, cur) => {
        pre.push(...cur.childrens)
        return pre
      }, [])
    },
    getCurrentPackageType(code) {
      return this.packageTypeList.find(item => item.code === code)?.name || '-'
    }
  },
  filters: {
    packStatusFilter(value, te) {
      let str
      switch (+value) {
        case 1:
          str = te('lg.startUsing')
          break
        case 2:
          str = te('lg.stopUsing')
          break
        default:
          str = '-'
          break
      }
      return str
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container {
  height: 100%;
  background-color: #fff;
  padding: 0px 20px 10px;
  height: 0;
  flex: 1;
  .el-table {
    width: 100% !important;
    ::v-deep .el-table__header,
    ::v-deep .el-table__body {
      width: 100% !important;
    }
  }
}
</style>
