<template>
  <div class="refund-detail">
    <div class="refund-detail-title">退款审核</div>
    <el-form ref="refForm" label-width="80px" :inline="true" :model="form" :rules="rules">
      <el-form-item label="审核结果" prop="result">
        <el-radio-group v-model="form.result" @change="resultChange">
          <el-radio :label="1" :value="1">同意</el-radio>
          <el-radio :label="2" :value="2">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <br />
      <el-form-item label="实退金额" prop="realRefundAmount" v-if="form.result !== 2">
        <el-input v-model.trim="form.realRefundAmount" clearable placeholder="请输入实退金额"></el-input>
      </el-form-item>
      <el-form-item label="退款单号" prop="refundNo" v-if="form.result !== 2">
        <el-input v-model.trim="form.refundNo" clearable placeholder="请输入退款单号"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="form.remark" style="width: 640px;" placeholder="请输入" maxlength="200"></el-input>
      </el-form-item>
      <br />
      <el-form-item label="附件" prop="file" class="form-item-file">
        <BtnUpload
          ref="BtnUpload"
          url="/client/common/upload.do"
          :multiple="true"
          :coverUpload="false"
          :tipMessage="'支持上传的文件类型:.rar .zip .doc .docx .pdf .jpg，单个文件不超过10Mb'"
          :beforeUpload="beforeReceiptUpload"
          @on-success="handleInvoiceUrlSuccess"
          @on-error="handleInvoiceError"
          @on-remove="handleInvoiceRemove"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import BtnUpload from '@/components/Upload/BtnUpload.vue'
import { _refundVerify } from '@/api/order.js'
import { mapGetters } from 'vuex'
export default {
  components: { BtnUpload },
  props: {
    orderInfo: {
      type: Object,
      default: () => {}
    },
    refundInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        result: '',
        realRefundAmount: '',
        refundNo: '',
        remark: '',
        files: []
      },
      rules: {
        result: [{ required: true, message: this.$t('0gKC2yGndZbATta9DrMLH'), trigger: 'change' }],
        realRefundAmount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              console.log(typeof Number(value), 'Number(value)---')
              if (!value) {
                callback(new Error('请输入实退金额'))
              } else if (isNaN(Number(value))) {
                callback(new Error('请输入正确的金额'))
              } else if (Number(value) > this.refundInfo.applyAmount) {
                callback(new Error('实退金额不能超过申请退款的金额'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        refundNo: [{ required: true, message: '请输入退款单号', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters(['currencyParams'])
  },
  methods: {
    async handleSubmit() {
      console.log(this.form, 'this.fo--------')
      this.$refs['refForm'].validate(valid => {
        if (valid) {
          this.$confirm('请确认是否提交审核结果？', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            center: true
          }).then(() => {
            this.handleVerify()
          })
        }
      })
    },
    async handleVerify() {
      const { ret, data } = await _refundVerify({
        refundId: this.orderInfo.id,
        currency: this.currencyParams,
        ...this.form
      })
      if (ret === 1) {
        this.$message.success('审核成功')
        this.$emit('success')
      }
    },
    resultChange(e) {
      if (e === 1) {
        this.$set(this.rules, 'remark', [{ required: false, message: '请输入内容', trigger: 'change' }])
      } else {
        this.$set(this.rules, 'remark', [{ required: true, message: '请输入内容', trigger: 'change' }])
        this.form.realRefundAmount = ''
        this.form.refundNo = ''
      }
      this.$refs['refForm'].clearValidate()
    },
    async handleInvoiceUrlSuccess({ type, file, data }) {
      console.log(data, 'data----------')
      this.form.files.push({
        fileName: data.fileName,
        url: data.viewUrl
      })
    },
    handleInvoiceRemove() {
      this.form.files = []
    },
    beforeReceiptUpload(file) {
      let type = file.type || (file.raw && file.raw.type) || ''
      console.log(type, 't----------')
      // let isValid = ['application/pdf'].indexOf(type)
      let isValid = true
      const supportTypes = ['.rar', '.zip', '.doc', '.docx', '.pdf', '.jpg']
      const fileExt = '.' + file.name.split('.').pop()
      console.log(fileExt, 'fileExt----------')
      if (!supportTypes.includes(fileExt)) {
        this.$message.warning('请上传正确的文件')
        return false
      }
      if (file.size / 1024 / 1024 > 10) {
        this.$message.warning('文件大小不能超过10MB')
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-detail {
  &-title {
    font-weight: 500;
    margin-bottom: 20px;
  }
  ::v-deep .form-item-file {
    .el-upload-list__item-name {
      max-width: 500px;
    }
  }
}
</style>
