<template>
  <el-dialog
    :title="null"
    custom-class="provider-manage-balance-dialog"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    top="12vh"
    width="1188px"
    @open="handleOpen"
    append-to-body
  >
    <div class="balance-dialog-body">
      <svg-icon icon-class="icon_close" class="detail-close-icon" @click="dialogVisible = false"></svg-icon>
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane lazy :label="$t('lg.incomeexpend')" name="incomeDetail">
          <IncomeDetail
            :info="info"
            v-if="dialogVisible && activeName === 'incomeDetail'"
            @changeTab="changeTab"
            @toOrder="info => $emit('toOrder', info)"
            @close="dialogVisible = false"
          />
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('HdBEUEdRZtTN4ZfrwWDWj')" name="withrawalDetail" v-if="info.bisUserId != 1">
          <WithrawalDetail
            :info="info"
            :changeTabInfo="changeTabInfo"
            @showDetail="showWithrawalDetail"
            v-if="dialogVisible && activeName === 'withrawalDetail'"
          />
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('xsV0NNfyQJJhMNnQpfXJr')" name="billDetail">
          <BillDetail :info="info" :changeTabInfo="changeTabInfo" v-if="dialogVisible && activeName === 'billDetail'" />
        </el-tab-pane>
        <el-tab-pane lazy :label="'短信消耗'" name="smsConsum">
          <SmsConsum :isServerManage="true" :userInfo="info" v-if="dialogVisible && activeName === 'smsConsum'" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <Detail :visible.sync="detailVisible" dialogType="1" :info="detailRowInfo" />
  </el-dialog>
</template>

<script>
import IncomeDetail from './BalanceDialog/IncomeDetail.vue'
import WithrawalDetail from './BalanceDialog/WithrawalDetail.vue'
import BillDetail from './BalanceDialog/BillDetail.vue'
import SmsConsum from '../../Message/index.vue'
import Detail from '@/views/finance/FinancialManagement/WithdrawVerify/components/Detail.vue'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    IncomeDetail,
    WithrawalDetail,
    BillDetail,
    Detail,
    SmsConsum
  },
  data() {
    return {
      activeName: 'incomeDetail',
      changeTabInfo: {},
      detailRowInfo: {},
      detailVisible: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleOpen() {
      this.activeName = 'incomeDetail'
      this.changeTabInfo = {}
    },
    changeTab(info) {
      const { activeName } = info
      this.changeTabInfo = info
      this.activeName = activeName
    },
    showWithrawalDetail(info) {
      this.detailRowInfo = info
      this.detailVisible = true
    },
    handleTabClick() {
      this.changeTabInfo = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.balance-dialog-body {
  position: relative;
  min-height: 100px;
  border-radius: 4px;
  background-color: #fff;
  .detail-close-icon {
    position: absolute;
    z-index: 1;
    right: 20px;
    top: 24px;
    cursor: pointer;
  }
  ::v-deep .el-tabs__header {
    .el-tabs__nav-scroll {
      padding-left: 30px;
    }
    .el-tabs__item {
      height: 54px;
      line-height: 54px;
    }
  }
  ::v-deep .el-tabs__content {
    padding: 5px 24px;
  }
}
</style>
<style lang="scss">
.provider-manage-balance-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
</style>
