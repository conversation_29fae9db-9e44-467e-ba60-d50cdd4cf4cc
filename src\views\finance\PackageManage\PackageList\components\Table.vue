<template>
  <div class="table-container">
    <el-table v-loading="loading" :data="tableData" height="calc(100% - 52px)" :loading="loading">
      <el-table-column prop="packId" :label="$t('Bpdy-V0IcCvHQ_Agega0T')"></el-table-column>
      <el-table-column prop="packName" :label="$t('uigvo2391weT6pPqyfxYg')">
        <template slot-scope="{ row }">
          <span style="color: #3370ff;cursor: pointer" @click="showDetailDialog(row, 1)">{{ row.packName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="packType" :label="$t('qzwEjrvjWERzbbwcvI5rK')">
        <template slot-scope="{ row }">
          <span>{{ getCurrentPackageType(row.packType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="packStatus" :label="$t('_CXEjvnybYTxiZPXdJknx')">
        <template slot-scope="{ row }">
          <span>{{ row.packStatus | packStatusFilterVer3 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="agentPrice" :label="currencyParams == 'CNY' ? $t('1W2i1KY0Bfz_8olXlC2d6') : $t('aaBbwSAmqzqUCcJRh515g')"> </el-table-column>
      <el-table-column prop="salePrice" :label="currencyParams == 'CNY' ? $t('zUwkOXwA9wIf2S0kzBT7x') : $t('QLgJjy0jWjsRUEDzFwoX8')"> </el-table-column>
      <el-table-column prop="discount" :label="$t('pWM8WfOGzZzcyBJMF4w4Q') + '（%）'"> </el-table-column>
      <el-table-column prop="discountedPrice" :label="currencyParams == 'CNY' ? $t('5xesHLL5WSWphtJ7fjIKW') : $t('tWpblUPkU3n357-hOUDWy')"> </el-table-column>
      <el-table-column prop="sort" :label="$t('HbdiV2RLW3mpJDkZ_KYFG')">
        <template slot-scope="{ row }">
          <div>
            {{ row.sort ?? '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="onSale" :label="$t('lg.status')">
        <template slot-scope="{ row }">
          <div class="status" @click="handleStatus(row)">
            <svg-icon icon-class="shangjia" v-if="row.onSale === 1"></svg-icon>
            <svg-icon icon-class="xiajia" v-else></svg-icon>
            <span class="status-desc" :class="{ 'left-24': row.onSale !== 1 }">{{
              row.onSale ? $t('cu1pVC4m4_ExdeETrqlaH') : $t('_7CU8hvXPxWIHZ2Hw09lG')
            }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="handle" :label="$t('lg.operator')" width="100">
        <!-- 手机定位类型套餐只有超管可以编辑套餐 -->
        <template slot-scope="{ row }" v-if="userType === 0 || row.packType !== 9">
          <span class="swd-table-cell-btn" @click="showEditDialog(row)">{{ $t('lg.edit') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="swd-pagination-container">
      <!-- 分页 -->
      <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" class="swd-download-btn" @click="emitExport">
          <svg-icon icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip>
      <base-pagination
        :current-page.sync="searchParams.pageIndex"
        :page-sizes="[15, 25, 35]"
        :page-size="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </base-pagination>
    </div>

    <Edit ref="Edit" :visible.sync="editVisible" :info="packInfo" @success="handleEditSuccess" />
    <PackageDetail ref="PackageDetail" :visible.sync="detailVisible" :info="packInfo" :id="packInfo.packId" :type="detailType" :showCost="false" />
  </div>
</template>

<script>
import PackageDetail from '../../components/PackageDetail.vue'
import { mapGetters } from 'vuex'
import { _getPackageList, _exportPackageList, _changePackageStatus, getPackageClassify } from '@/api/order.js'
import Edit from './Edit.vue'
export default {
  components: {
    Edit,
    PackageDetail
  },
  data() {
    return {
      detailType: 1, // 0-新增 1-详情 2-编辑
      detailVisible: false, // 套餐详情弹窗
      packInfo: {}, // 选择的套餐代理信息
      editVisible: false, // 编辑弹窗
      loading: false,
      tableData: [],
      total: 0,
      searchParams: {
        pageIndex: 1,
        pageSize: 15
      },
      packageTypeList: [],
      changePackStatusLoading: false
    }
  },
  computed: {
    ...mapGetters(['currencyParams', 'userType'])
  },
  created() {
    this.getPackageClassifyList()
  },
  methods: {
    search(info) {
      this.searchParams = { ...{ pageIndex: 1, pageSize: 15 }, ...info }

      this.getPackageList()
    },
    async getPackageList() {
      this.loading = true
      try {
        let res = await _getPackageList(this.searchParams)
        if (res.ret === 1) {
          this.tableData = res.data || []
          this.total = res.total || 0
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      } finally {
        this.loading = false
      }
    },
    // 打开编辑弹窗
    showEditDialog(row) {
      this.packInfo = row
      this.editVisible = true
    },
    // 打开套餐详情
    showDetailDialog(row, type) {
      this.detailType = type
      this.packInfo = row
      this.detailVisible = true
    },
    // 导出
    emitExport() {
      if (this.tableData.length == 0) {
        this.$message.warning(this.$t('lg.noData'))
        return
      }
      let { pageIndex, pageSize, ...param } = this.searchParams
      _exportPackageList(param).then(res => {
        let a = document.createElement('a')
        let blob = new Blob([res], { type: 'application/ms-excel;charset=UTF-8' })
        let objectUrl = URL.createObjectURL(blob)
        a.setAttribute('href', objectUrl)
        let fileName = new Date()
        fileName = fileName.getFullYear() + '' + (fileName.getMonth() + 1) + fileName.getDate()
        a.setAttribute('download', `${fileName}.xlsx`)
        a.click()
      })
    },
    // 点击上下架状态
    async handleStatus(row) {
      //手机定位类套餐，非管理员不可以编辑上下架
      if (this.userType !== 0 && row.packType === 9) {
        this.$message.warning('该套餐不允许下架')
        return
      }
      if (this.changePackStatusLoading) {
        return
      }
      if (row.onSale == 0 && row.packStatus == 0) {
        this.$message.warning(this.$t('1tG8AEnd8XObHHyr_LJWO'))
        return
      }
      if (row.onSale == 1 && row.standard == 1) {
        this.$message.warning(this.$t('hqIAi-yNhcVJJes_Wii8D'))
        return
      }
      if (row.onSale == 1 && row.renewType) {
        this.$confirm('', this.$t('EFUKd-0PFs2kje_Z0c1h9'), {
          message: `<span style="color:red">${this.$t('9G-UExFllfV8X9i978qOe')}</span>`,
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
          .then(() => {
            this.changePackageStatus(row)
          })
          .catch(e => {
            console.log(e)
          })
      } else {
        this.changePackageStatus(row)
      }
    },
    async changePackageStatus(row) {
      let params = {
        agentConfigId: row.agentConfigId,
        onSale: row.onSale == 0 ? 1 : 0
      }
      this.changePackStatusLoading = true
      try {
        let res = await _changePackageStatus(params)
        if (res.ret === 1) {
          this.$message.success(this.$t('lg.success'))
          this.getPackageList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
      this.changePackStatusLoading = false
    },
    // 编辑成功
    handleEditSuccess() {
      this.getPackageList()
    },
    handleCurrentChange(page) {
      this.searchParams.page = page
      this.getPackageList()
    },
    handleSizeChange(pageSize) {
      this.searchParams.pageSize = pageSize
      this.getPackageList()
    },
    // 翻译函数
    te(arg) {
      const hasKey = this.$t(arg)
      if (hasKey) {
        const result = this.$t(arg)
        return result
      }
      return arg
    },
    async getPackageClassifyList() {
      const result = await getPackageClassify()
      this.packageTypeList = result.data.reduce((pre, cur) => {
        pre.push(...cur.childrens)
        return pre
      }, [])
    },
    getCurrentPackageType(code) {
      return this.packageTypeList.find(item => item.code === code)?.name || '-'
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container {
  background-color: #fff;
  padding: 0px 20px 10px;
  height: 0;
  flex: 1;
  .el-table {
    width: 100% !important;
    ::v-deep .el-table__header,
    ::v-deep .el-table__body {
      width: 100% !important;
    }
  }
  .status {
    position: relative;
    cursor: pointer;
    svg {
      width: 59px;
      height: 23px;
    }
    .status-desc {
      position: absolute;
      left: 8px;
      color: #fff;
    }
    .left-24 {
      left: 24px;
    }
  }
}
</style>
