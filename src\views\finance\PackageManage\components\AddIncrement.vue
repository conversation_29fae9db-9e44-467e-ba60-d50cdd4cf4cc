<template>
  <div class="increment">
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="packContent" :label="$t('ovK_RRKbTaBSEHhgL_kRy')" width="166">
        <template slot-scope="{ row }">
          <el-select class="width-146" v-model="row.funcId" :disabled="type !== 0 || !onDefaultTab" @change="handleChange($event, row, 1)">
            <el-option v-for="item in packContentOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="validDuration" :label="$t('mMGDudnh3-MYiKN3ZmVZQ')" width="98"
        ><template slot-scope="{ row }">
          <el-input
            class="width-78"
            v-model="row.validDuration"
            :disabled="type !== 0 || row.validDurationDisabled || !onDefaultTab"
            @change="handleChange($event, row)"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="validDurationUnit" :label="$t('iIRxRd4XquTPvNwkbbPAt')" width="98">
        <template slot-scope="{ row }">
          <el-select
            class="width-78"
            v-model="row.validDurationUnit"
            :disabled="type !== 0 || row.validDurationUnitDisabled || !onDefaultTab"
            @change="handleChange($event, row, 3)"
          >
            <el-option v-for="item in validRuleOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select> </template
      ></el-table-column>
      <el-table-column prop="calculateByMonth" :label="$t('eIhE-MpijfPx1_YivOWfn')" width="98">
        <template slot-scope="{ row }">
          <el-select
            class="width-78"
            v-model="row.calculateByMonth"
            :disabled="type !== 0 || row.calculateByMonthDisabled || !onDefaultTab"
            @change="handleChange($event, row)"
          >
            <el-option v-for="item in monthCalcOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select> </template
      ></el-table-column>
      <el-table-column prop="param" :label="$t('iMVebDXUsA5c_fxl671kL')" width="108">
        <template slot-scope="{ row }">
          <div class="param-count">
            <el-select
              v-if="[9, 14, 70].indexOf(row.funcId) !== -1"
              class="width-78"
              v-model="row.param"
              :disabled="type !== 0 || row.paramDisabled || !onDefaultTab"
              @change="handleChange($event, row)"
            >
              <el-option v-for="item in uploadoptionsType[String(row.funcId)]" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-input
              v-else
              class="width-78"
              v-model="row.param"
              :disabled="type !== 0 || row.paramDisabled || !onDefaultTab"
              @change="handleChange($event, row)"
            ></el-input>
            <span class="txt" v-if="[14, 65, 70].indexOf(row.funcId) !== -1">{{ $t('lg._day') }}</span>
            <span class="txt" v-if="[20, 63].indexOf(row.funcId) !== -1">{{ $t('NZn8Y-nZV3WRZLds93mmP') }}</span>
            <span class="txt" v-if="[9].indexOf(row.funcId) !== -1">{{ $t('lg._second') }}</span>
            <span class="txt" v-if="[69].indexOf(row.funcId) !== -1">{{ $t('Z9YgHkSg2uB840tCIrZyC') }}</span>
            <span class="txt" v-if="[71].indexOf(row.funcId) !== -1">{{ '条' }}</span>
          </div>
        </template></el-table-column
      >
      <el-table-column prop="validType" :label="$t('ygl1CCzlGB6pYQi5oJgMg')" width="128"
        ><template slot-scope="{ row }">
          <el-select
            class="width-108"
            v-model="row.validType"
            :disabled="type !== 0 || row.validTypeDisabled || !onDefaultTab"
            @change="handleChange($event, row)"
          >
            <el-option v-for="item in validTypeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="primePrice" :label="$t('YEsjuadygPpzDlYbOQdfC')" width="98"
        ><template slot-scope="{ row }">
          <el-input
            class="width-78"
            v-model="row.primePrice"
            :disabled="type === 1 || (type !== 0 && row.primePriceDisabled) || !onDefaultTab"
            @change="handleChange($event, row)"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="remark" :label="$t('lg.remark')" @change="handleChange($event, row)" :width="onDefaultTab ? 100 : 130">
        <template slot-scope="{ row }">
          <el-input v-model="row.remark" :disabled="type !== 0 || row.remarkDisabled || !onDefaultTab"></el-input>
        </template>
      </el-table-column>
      <el-table-column v-if="type == 0 && onDefaultTab">
        <template slot-scope="{ row }"> <svg-icon class="delete" icon-class="delete1" @click="deleteRow(row)"></svg-icon> </template
      ></el-table-column>
    </el-table>

    <div class="add-btn" @click="addRow" v-if="type === 0 && !disabled && onDefaultTab">
      <svg-icon class="left" icon-class="add1" @click="deleteRow(row)" v-if="type == 0"></svg-icon>
      <span class="right">{{ $t('sdgerhtnzbZKrtr4A445D') }}</span>
    </div>
  </div>
</template>

<script>
import { _getFunctionsSelector } from '@/api/order.js'
export default {
  props: {
    disabled: {
      type: Boolean,
      default: true
    },
    type: {
      // 操作类型 0-新增 1-详情 2-编辑
      type: [Number, String],
      default: 1
    },
    data: {
      type: Array,
      default: () => []
    },
    onDefaultTab: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ruleKey: 1,
      tableData: [],
      packContentOptions: [], // 增值功能
      validRuleOptions: [
        { label: this.$t('lg._day'), value: 1 },
        { label: this.$t('sfwA5_FWQER4kGGM23f19'), value: 2 },
        { label: this.$t('sfwA5_FWQER4kGGM23f20'), value: 3 }
      ], // 生效规则
      monthCalcOptions: [
        { label: this.$t('SrGP45dYF4TfIMabJ8GI0'), value: 1 },
        { label: this.$t('PSJif2BD7ijRnlbhUCzbL'), value: 0 }
      ], // 按月计算
      validTypeOptions: [
        { label: this.$t('iPiBIJga70QxIIRCBldDv'), value: 1 },
        { label: this.$t('FXJLzQWH0isx1K681LHvq'), value: 2 }
      ], // 生效类型
      uploadOptions: [
        { label: 30, value: '30' },
        { label: 10, value: '10' },
        { label: 5, value: '5' },
        { label: 1, value: '1' }
      ], // 上传间隔选项
      uploadoptionsType: {
        '9': [
          { label: 30, value: '30' },
          { label: 10, value: '10' },
          { label: 5, value: '5' },
          { label: 1, value: '1' }
        ],
        '14': [
          { label: 2, value: 2 },
          { label: 30, value: 30 },
          { label: 90, value: 90 },
          { label: 180, value: 180 }
        ],
        '69': [
          { label: 2, value: 2 },
          { label: 30, value: 30 },
          { label: 90, value: 90 },
          { label: 180, value: 180 }
        ],
        '70': [
          { label: 1, value: 1 },
          { label: 2, value: 2 },
          { label: 3, value: 3 },
          { label: 7, value: 7 },
          { label: 30, value: 30 },
          { label: 60, value: 60 },
          { label: 90, value: 90 }
        ],
        undefined: []
      },
      paramsData: [], // 增值功能参数数据
      checkInfo: {
        type: -1,
        isValid: true
      } // 参数是否合法
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(val) {
        this.tableData = [...this.data].map(item => {
          return { ...item, ...this.handleDefaultParams(item), ruleKey: this.ruleKey++ }
        })
      },
      deep: true
    }
  },
  created() {
    this.getFunctionsSelector()
  },
  methods: {
    // 处理item默认参数有
    handleDefaultParams(item, type) {
      item = { ...item, ...this.initDisabledParams(item) }
      switch (Number(item.funcId)) {
        case 9: // 上传间隔
          item.calculateByMonthDisabled = true
          if (type === 1) {
            item.validDuration = 0
            item.validDurationUnit = 2
            item.calculateByMonth = 0
            item.param = 30
            item.validType = 1
            item.primePrice = 0
            item.remark = ''
          }
          break
        case 14: // 轨迹查询时长
          item.calculateByMonthDisabled = true
          this.uploadOptions = [
            { label: 2, value: 2 },
            { label: 30, value: 30 },
            { label: 90, value: 90 },
            { label: 180, value: 180 }
          ]
          if (type === 1) {
            item.validDuration = 0
            item.validDurationUnit = 2
            item.calculateByMonth = 0
            item.param = 30
            item.validType = 1
            item.primePrice = 0
            item.remark = ''
          }
          break
        case 20: // 防盗录音时长
          if (type === 1) {
            item.validDuration = 0
            item.validDurationUnit = 2
            item.calculateByMonth = 0
            item.param = 0
            item.validType = 1
            item.primePrice = 0
            item.remark = ''
          }
          if (item.validDurationUnit == 1) {
            item.calculateByMonth = 0
            item.calculateByMonthDisabled = true
          }
          break
        case 63: // 高清录音时长
          if (type === 1) {
            item.validDuration = 0
            item.validDurationUnit = 2
            item.calculateByMonth = 0
            item.param = 0
            item.validType = 1
            item.primePrice = 0
            item.remark = ''
          }
          if (item.validDurationUnit == 1) {
            item.calculateByMonth = 0
            item.calculateByMonthDisabled = true
          }
          break
        case 64: // 平台服务激活
          item.validDurationDisabled = true
          item.validDurationUnitDisabled = true
          item.calculateByMonthDisabled = true
          item.paramDisabled = true
          item.validTypeDisabled = true
          if (type === 1) {
            item.validDuration = undefined
            item.validDurationUnit = undefined
            item.param = undefined
            item.validType = 1
            item.calculateByMonth = 0
            item.primePrice = 0
            item.remark = ''
          }
          break
        case 65: // 平台服务时长
          item.validDurationDisabled = true
          item.validDurationUnitDisabled = true
          item.calculateByMonthDisabled = true
          item.validTypeDisabled = true
          if (type === 1) {
            item.validDuration = undefined
            item.validDurationUnit = undefined
            item.validType = 1
            item.calculateByMonth = 0
            item.primePrice = 0
            item.param = 0
            item.remark = ''
          }
          break
        case 67:
          // 好友关联服务时长
          // item.validDurationDisabled = true  // 有效时长
          // item.validDurationUnitDisabled = true // 生效规则
          item.calculateByMonthDisabled = true // 按月计算
          item.validTypeDisabled = true // 生效类型
          item.paramDisabled = true // 数量
          if (type === 1) {
            item.validDuration = undefined // 有效时长
            item.validDurationUnit = undefined // 生效规则
            item.validType = 1 // 生效类型
            item.calculateByMonth = undefined // 是否按月计算
            item.primePrice = 0 // 成本价
            item.param = 0 // 数量
            item.remark = '' // 备注
          }
          break
        case 68:
          item.validDurationDisabled = true
          item.validDurationUnitDisabled = true
          item.calculateByMonthDisabled = true
          item.validTypeDisabled = true
          item.paramDisabled = true
          if (type === 1) {
            item.validDuration = undefined
            item.validDurationUnit = undefined
            item.validType = 1
            item.calculateByMonth = 0
            item.primePrice = 0
            item.param = 0
            item.primePrice = 0
            item.remark = ''
          }
          break
        case 69:
          item.validDurationDisabled = true
          item.validDurationUnitDisabled = true
          item.calculateByMonthDisabled = true
          item.validTypeDisabled = true
          if (type === 1) {
            item.validDuration = undefined
            item.validDurationUnit = undefined
            item.validType = 1
            item.calculateByMonth = 0
            item.primePrice = 0
            item.param = 5
            item.primePrice = 0
            item.remark = ''
          }
          break
        case 70:
          item.calculateByMonthDisabled = true
          if (type === 1) {
            item.validDuration = 0
            item.validDurationUnit = 2
            item.validType = 1
            item.calculateByMonth = 0
            item.primePrice = 0
            item.param = 2
            item.remark = ''
          }
          break
        case 71:
          item.validDurationDisabled = true
          item.validDurationUnitDisabled = true
          item.calculateByMonthDisabled = true
          item.validTypeDisabled = true
          if (type === 1) {
            item.param = ''
            item.primePrice = ''
          }
          break
        default:
          break
      }
      return item
    },
    // 获取增值功能下拉列表
    async getFunctionsSelector() {
      try {
        let res = await _getFunctionsSelector()
        if (res.ret === 1) {
          this.packContentOptions =
            res.data.map(item => {
              return {
                label: item.funcName,
                value: item.funcId
              }
            }) || []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 新增行
    addRow() {
      this.tableData.push({
        ruleKey: this.ruleKey++,
        funcId: undefined,
        validDuration: 0, // 有效时长
        validDurationUnit: 2, // 生成规则
        calculateByMonth: 0, // 是否按月计算
        param: 0, // 数量
        validType: 1, // 生效类型
        primePrice: 0, // 成本价
        remark: ''
      })
    },
    // 删除行
    deleteRow(row) {
      this.tableData = this.tableData.filter(item => item.ruleKey !== row.ruleKey)
    },
    // 数据改变时
    handleChange(val, row, type) {
      this.tableData =
        this.tableData.map(item => {
          if (item.ruleKey === row.ruleKey) {
            item = { ...row, ...this.handleDefaultParams(item, type) }
          }
          return item
        }) || []

      let price = this.tableData.reduce((prev, cur) => {
        if (cur.primePrice) {
          return prev + Number(cur.primePrice)
        }
        return prev
      }, 0)
      this.$emit('getPrimePrice', price)
    },
    // 初始化禁用选项
    initDisabledParams() {
      return {
        validDurationDisabled: false, // 有效时长
        validDurationUnitDisabled: false, // 生成规则
        calculateByMonthDisabled: false, // 是否按月计算
        paramDisabled: false, // 数量
        validTypeDisabled: false, // 生效类型
        primePriceDisabled: false, // 成本价
        remarkDisabled: false
      }
    },
    // 获取增值功能参数
    getRuleParams() {
      //增值功能表单字段限制
      let validFlag = true
      //数字
      let reg = /^\d+$/
      //两位小数点的数字
      let reg1 = /^\d+(\.\d{0,2})?$/
      for (let i = 0; i < this.tableData.length; i++) {
        let item = this.tableData[i]
        console.log(reg1.test(+item.primePrice), reg1.test(+item.primePrice))
        if (this.type === 0 && !item.validDurationDisabled && (!item.validDuration || !reg.test(+item.validDuration) || item.validDuration <= 0)) {
          //新增时校验有效时长，详情和编辑时这个字段无法修改，无需校验
          this.$message.warning('有效时长必须为正整数')
          validFlag = false
          break
        } else if (this.type === 0 && !item.paramDisabled && (!item.param || !reg.test(+item.param) || item.param <= 0)) {
          //新增时校验数量，详情和编辑时这个字段无法修改，无需校验
          this.$message.warning('数量必须为正整数')
          validFlag = false
          break
        } else if (this.type !== 1 && !item.primePriceDisabled && item.primePrice && !reg1.test(+item.primePrice)) {
          //新增或者编辑时校验成本价，查看详情无需校验
          this.$message.warning(this.$t('HcbviLThyjLIgx7yQhJIo'))
          validFlag = false
          break
        } else if (this.type !== 1 && item.funcId === 71 && !item.primePriceDisabled && (!reg1.test(+item.primePrice) || item.primePrice <= 0)) {
          //新增或者编辑时校验成本价，查看详情无需校验,短信告警类成本价不允许设置为0
          this.$message.warning('成本必须为大于0的数值')
          validFlag = false
          break
        }
      }
      if (!validFlag) {
        return validFlag
      }
      return this.tableData.map(item => {
        let {
          ruleKey,
          validDurationDisabled,
          validDurationUnitDisabled,
          calculateByMonthDisabled,
          paramDisabled,
          validTypeDisabled,
          primePriceDisabled,
          remarkDisabled,
          ...rest
        } = item
        return rest
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.increment {
  .width-146 {
    width: 146px;
  }
  .width-78 {
    width: 78px;
  }
  .width-108 {
    width: 108px;
  }
  .width-130 {
    width: 130px;
  }
  .add-btn {
    text-align: center;
    cursor: pointer;
  }
  .delete {
    color: #ff1818;
    display: inline-block;
    font-size: 16px;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
    text-align: center;
  }
  .add-btn {
    color: #3370ff;
    margin-top: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    .left {
      font-size: 16px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      border-radius: 50%;
      margin-right: 6px;
      cursor: pointer;
    }
  }
  .param-count {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    .txt {
      padding-left: 5px;
      white-space: nowrap;
    }
  }
}
</style>
