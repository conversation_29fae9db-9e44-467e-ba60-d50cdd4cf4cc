<template>
  <div class="container">
    <Header ref="Header" @open="handleOpenDialog" @withdrawSuccess="withdrawSuccess" v-if="configObj.amountShowSwitch" />
    <section class="content" :class="{ hasHeader: configObj.amountShowSwitch, minHeader: !configObj.showAdminOrProviderAmount }">
      <el-tabs v-model="activeName" @tab-click="handleClick" class="cus-tab">
        <!-- 收支明细 -->
        <el-tab-pane v-if="permissionArr.includes('finance:finance_manage:income_detail')" lazy :label="$t('lg.incomeexpend')" name="first">
          <IncomeAndExpenditureDetail ref="first" @refreshAccount="refreshAccount" @changeTab="changeTab"
        /></el-tab-pane>
        <!-- 提现明细 -->
        <el-tab-pane v-if="permissionArr.includes('finance:finance_manage:withrawal_detail')" lazy :label="$t('HdBEUEdRZtTN4ZfrwWDWj')" name="eighth">
          <WithdrawRecord ref="eighth" :changeTabInfo="changeTabInfo" @refreshAccount="refreshAccount" />
        </el-tab-pane>
        <!-- 分账详单 -->
        <el-tab-pane v-if="permissionArr.includes('finance:finance_manage:bill_detail')" lazy :label="$t('xsV0NNfyQJJhMNnQpfXJr')" name="second"
          ><DetailedBill :isSecondMountedSearch="isSecondMountedSearch" @changeTab="changeTab" @refreshAccount="refreshAccount" ref="second"
        /></el-tab-pane>
        <!-- 订单管理 -->
        <el-tab-pane v-if="permissionArr.includes('finance:finance_manage:order_manage')" lazy :label="$t('lg.limits.order_manager')" name="third"
          ><OrderManage :isThirdMountedSearch="isThirdMountedSearch" ref="third"
        /></el-tab-pane>
        <!-- 服务商订单 -->
        <el-tab-pane
          lazy
          :label="$t('serviceNumberOrder')"
          v-if="permissionArr.includes('finance:finance_manage:service_order') || [1681, 8108].indexOf(+userInfo.userId) !== -1"
          name="serviceNumberOrder"
        >
          <service-number-order ref="ServiceNumberOrder" :changeTabInfo="changeTabInfo" />
        </el-tab-pane>
        <!-- 导入记录 -->
        <el-tab-pane
          v-if="permissionArr.includes('finance:finance_manage:import_record') || [1681, 8108, 18060, 18118].indexOf(+userInfo.userId) !== -1"
          lazy
          :label="$t('lg.importRecord')"
          name="sixth"
          ><ImportRecord ref="sixth"
        /></el-tab-pane>
        <!-- 重置记录 -->
        <el-tab-pane
          v-if="permissionArr.includes('finance:finance_manage:import_record') || [1681, 8108, 18060, 18118].indexOf(+userInfo.userId) !== -1"
          lazy
          :label="$t('D18M1wrudyshM_m7yv-nZ')"
          name="resetRecord"
        >
          <ResetRecord ref="resetRecord" :changeTabInfo="changeTabInfo" />
        </el-tab-pane>
        <!-- 服务商管理 -->
        <el-tab-pane v-if="permissionArr.includes('finance:finance_manage:provider_manage')" lazy :label="$t('OQQDG7tWbK4xJ_SxAn7mM')" name="fourth"
          ><ServiceProviderManagement @changeTab="changeTab"
        /></el-tab-pane>
        <!-- 亚马逊订单 -->
        <el-tab-pane
          v-if="permissionArr.includes('finance:finance_manage:amazon_order') && showAmazonOrder"
          lazy
          :label="$t('xN-U2ljzfROs3R2UFCYsM')"
          name="seventh"
          ><AmazonOrderManager
        /></el-tab-pane>
        <!-- 提现审核 -->
        <el-tab-pane v-if="permissionArr.includes('finance:finance_manage:withrawal_verify')" lazy :label="$t('YgZRMzhnb44UD16opnREb')" name="ninth"
          ><WithdrawVerify @verifySuccess="verifySuccess"
        /></el-tab-pane>
        <!-- 退款管理 -->
        <el-tab-pane lazy :label="'退款管理'" name="refundManage" v-if="userInfo.userType === 0">
          <RefundManage />
        </el-tab-pane>
        <!-- 实名认证 -->
        <el-tab-pane lazy :label="$t('lEttzd9lDWHeMmVtBmrNU')" name="realnameVerify"><RealNameVerify @verifySuccess="verifySuccess"/></el-tab-pane>
        <!-- 短信消耗 -->
        <el-tab-pane lazy :label="'短信消耗'" name="message"><Message /></el-tab-pane>
      </el-tabs>
    </section>

    <!-- 套餐修改弹窗 -->
    <Modify ref="Modify" :visible.sync="modifyVisible" @success="changeTab" @showDetailDialog="showDetailDialog" />
    <Import
      ref="Import"
      :visible.sync="importVisible"
      @success="changeTab"
      @showAddCustomer="showAddCustomer"
      :addUserInfo="addUserInfo"
      @showDetailDialog="showDetailDialog"
    />
    <!-- 重置 -->
    <Reset ref="Reset" :visible.sync="resetVisible" @success="changeTab" @showDetailDialog="showDetailDialog" />
    <PackageDetail ref="PackageDetail" :visible.sync="detailVisible" :info="packInfo" :id="packInfo.packId" :type="detailType" :showCost="false" />

    <!--      新增下级用户-->
    <el-dialog :title="$t('asdiOIosdj_564Hjoijid')" :visible.sync="addSubVisible" :close-on-click-modal="false" width="650px" top="5vh">
      <add-sub ref="addSub" :showButton="false" @closeAccountDialog="closeAccountDialog"></add-sub>
      <span slot="footer">
        <el-button @click="handleAddCancel">{{ $t('lg.cancel') }}</el-button>
        <el-button type="primary" @click="handleAddConfirm">{{ $t('lg.submit') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Header from '@/views/finance/FinancialManagement/Header/index.vue'
import { _getIsServiceAdmin, _getServiceProviderConfig } from '@/api/finance.js'
import { mapGetters } from 'vuex'

export default {
  components: {
    Header,
    IncomeAndExpenditureDetail: () => import('./IncomeAndExpenditureDetail/index.vue'),
    WithdrawRecord: () => import('./WithdrawRecord/index.vue'),
    DetailedBill: () => import('./DetailedBill/index.vue'),
    OrderManage: () => import('./OrderManage/index.vue'),
    ImportRecord: () => import('./ImportRecord/index.vue'),
    ServiceProviderManagement: () => import('./ServiceProviderManagement/index.vue'),
    AmazonOrderManager: () => import('./AmazonOrderManager/index.vue'),
    Modify: () => import('./PublicComponents/Modify.vue'),
    Import: () => import('./PublicComponents/Import.vue'),
    Reset: () => import('./PublicComponents/Reset.vue'),
    // Withdraw: () => import('./PublicComponents/Withdraw.vue'),
    AddSub: () => import('@/components/Bussiness/AddSub.vue'),
    PackageDetail: () => import('@/views/finance/PackageManage/components/PackageDetail.vue'),
    WithdrawVerify: () => import('./WithdrawVerify/index.vue'),
    ServiceNumberOrder: () => import('./ServiceNumberOrder'),
    RealNameVerify: () => import('./RealNameVerify'),
    ResetRecord: () => import('./ResetRecord'),
    RefundManage: () => import('./RefundManage'),
    Message: () => import('./Message')
  },
  provide() {
    return {
      configObj: this.configObj
    }
  },
  data() {
    return {
      detailType: 1, // 0-新增 1-详情 2-编辑
      detailVisible: false,
      packInfo: {}, // 套餐信息
      activeName: 'first',
      addUserInfo: {},
      addSubVisible: false,
      modifyVisible: false, // 套餐修改弹窗
      importVisible: false, // 设备导入弹窗
      resetVisible: false, // 设备重置弹窗
      isSecondMountedSearch: true, // 是否需要组件挂载时执行一次搜索
      isThirdMountedSearch: true, // 是否需要组件挂载时执行一次搜索
      changeTabInfo: {},
      permissionArr: [],
      configObj: {
        isServiceAdmin: false,
        amountShowSwitch: 0, //单独服务商管控金额显示
        exportSwitch: 0,
        showAdminAmount: false, //单独三巨头管控金额显示
        showAdminOrProviderAmount: false //三巨头+服务商管控金额显示，具体逻辑看下面接口参数解释
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'userId', 'permissions']),
    showAmazonOrder() {
      return window.location.host.indexOf('union.ihere.net') == -1
    }
  },
  watch: {
    '$route.params': {
      immediate: true,
      handler(val) {
        if (val.activeName) {
          this.changeTab(val)
        }
      }
    }
  },
  async mounted() {
    await this.getIsServiceAdmin()
    this.getServiceProviderConfig()
  },
  methods: {
    // 打开套餐详情弹窗
    showDetailDialog({ type, info, visible }) {
      if (info) {
        this.packInfo = info || {}
      }
      this.detailType = type
      this.detailVisible = visible
    },
    // 新增用户
    showAddCustomer(user) {
      this.addSubVisible = true
      this.$nextTick(() => {
        this.$refs.addSub.confirmUser(user ? user : this.userInfo)
      })
    },
    closeAccountDialog(e) {
      if (e.close) {
        this.addSubVisible = false
      }
      if (e.addUserInfo) {
        this.addUserInfo = e.addUserInfo
      }
    },
    handleAddCancel() {
      this.$refs.addSub.cancel()
    },
    handleAddConfirm() {
      this.$refs.addSub.submitAccount()
    },
    // 打开弹窗
    handleOpenDialog(type) {
      if (type === 1) {
        this.modifyVisible = true
      } else if (type === 2) {
        this.importVisible = true
      } else if (type === 3) {
        this.resetVisible = true
      }
    },
    handleClick(tab, event) {
      this.$refs.Header?.getAccountBalance()
    },
    changeTab(info) {
      this.changeTabInfo = info //建议使用props+watch方式切换tab带参查询，避免旧版的refs的方式产生的很多问题
      let { activeName, payId, orderId, busTradeNo, beginTime } = info //旧版代码先不做改动
      switch (activeName) {
        case 'second':
          this.activeName = 'second'
          this.isSecondMountedSearch = false
          this.$nextTick(() => {
            this.$refs.second.setParamsAndGoSearch({ payId, beginTime })
          })
          break
        case 'third':
          this.activeName = 'third'
          this.isThirdMountedSearch = false
          this.$nextTick(() => {
            this.$refs.third.setParamsAndGoSearch({ orderId, busTradeNo, beginTime })
          })
          break
        case 'fifth':
          this.modifyVisible = false
          break
        case 'sixth':
          this.importVisible = false
          this.$confirm(this.$t('importRemind'), this.$t('remind'), { customClass: 'tip-dialog' }).then(() => {
            this.activeName = 'sixth'
            this.$nextTick(() => {
              this.$refs.sixth.handleSearchParams({})
            })
          })
          break
        case 'eighth':
          this.activeName = 'eighth' //新版tab切换带参查询时，无需再调用子组件方法查询。
          // this.$nextTick(() => {
          //   this.$refs.eighth.changTabSearch(info)
          // })
          break
        case 'serviceNumberOrder':
          this.activeName = 'serviceNumberOrder'
          break
        case 'resetRecord':
          this.activeName = 'resetRecord'
          break
      }
    },

    async getIsServiceAdmin() {
      const { ret, data } = await _getIsServiceAdmin()
      if (ret && data) {
        this.configObj.isServiceAdmin = data
        if ((this.userId === 1 && data) || this.userId !== 1) {
          //当userId === 1时，是swd账号，必须是isServiceAdmin（刘总，段总，财务账号）为true时才显示金额
          //当userId !== 1时，是服务商账号，可以查看金额
          this.configObj.showAdminAmount = true
        }
      }
    },
    async getServiceProviderConfig() {
      const { ret, data } = await _getServiceProviderConfig()
      if (ret && data) {
        this.configObj.amountShowSwitch = data.amountShowSwitch
        this.configObj.exportSwitch = data.exportSwitch
        if ((this.userId === 1 && this.configObj.isServiceAdmin) || (this.userId !== 1 && data.amountShowSwitch)) {
          //当userId === 1时，是swd账号，必须是isServiceAdmin（刘总，段总，财务账号）为true时才显示金额
          //当userId !== 1时，是服务商账号，在服务商管理-编辑-金额显示打开时也可以显示金额
          /**
          showAdminOrProviderAmount字段和showAdminAmount的区别在于：
          showAdminAmount字段是三巨头账号或者服务商账号都可以显示，
          showAdminOrProviderAmount时段是三巨头账号或者服务商账号中打开金额显示开关的可以显示
          **/
          this.configObj.showAdminOrProviderAmount = true
        }
      }
    },
    //点击tab里的搜索按钮时，刷新头部账户总金额，产品要求！！！
    refreshAccount() {
      this.$refs.Header?.getAccountBalance()
    },

    // 提现成功
    withdrawSuccess() {
      setTimeout(() => {
        this.$refs.Header?.getAccountBalance()
        //提现成功，刷新头部金额，收支明细和提现明细tab
        if (this.$refs.first && this.$refs.first.handleReset) {
          this.$refs.first.handleReset()
        }
        if (this.$refs.eighth && this.$refs.eighth.handleReset) {
          this.$refs.eighth.handleReset()
        }
      }, 1000)
    },
    //审核成功
    verifySuccess() {
      //审核成功，刷新头部金额，收支明细和提现明细tab
      this.$refs.Header?.getAccountBalance()
      if (this.$refs.first && this.$refs.first.handleReset) {
        this.$refs.first.handleReset()
      }
      if (this.$refs.eighth && this.$refs.eighth.handleReset) {
        this.$refs.eighth.handleReset()
      }
    }
  },
  created() {
    this.permissionArr = this.permissions
      .filter(item => {
        return item.type === 2
      })
      .map(item => item.perms)
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  box-sizing: border-box;
  padding: 16px;
  border-radius: $containerRadius;
  // display: flex;
  // flex-direction: column;
}
.content {
  // flex: 1;
  height: 100%;
  border-radius: $containerRadius;
  background: #ffffff;
  &.hasHeader {
    height: calc(100% - 122px);
    &.minHeader {
      height: calc(100% - 69px);
    }
  }
}

// tab样式
::v-deep .cus-tab {
  height: 100%;
  .el-tabs__content {
    height: calc(100% - 61px);
  }
  .el-tab-pane {
    height: 100%;
  }
  .el-tabs__nav-wrap.is-top {
    padding-left: 30px;
    margin: 0 0 20px;
  }
  .el-tabs__item.is-top {
    height: 46px;
    line-height: 46px;
  }
}
</style>
<style lang="scss">
.tip-dialog button:first-child {
  display: none;
}
</style>
