<template>
  <div class="service-provider-settins">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item>
        <template #label>
          <span class="ljdw-mr-4">金额显示</span>
          <el-tooltip placement="top" effect="dark">
            <div slot="content" style="width: 400px">
              财务管理所有页面的账户总额、可用总额、冻结金额、提现；收支明细-余额；分账详单-收支金额、冻结金额、已到账金额；提现明细-合计提现金额；服务商管理-累计分账、账户余额
            </div>
            <PcFinanceTip size="12" />
          </el-tooltip>
        </template>
        <el-switch :active-value="1" :inactive-value="0" v-model="form.amountShowSwitch" @change="amountShowSwitchChange"></el-switch>
      </el-form-item>
      <el-form-item>
        <span slot="label">
          <span class="ljdw-mr-4">导出表格</span>
          <el-tooltip placement="top">
            <div slot="content">
              财务管理所有页面的导出功能
            </div>
            <PcFinanceTip size="12" /> </el-tooltip
        ></span>
        <el-switch :active-value="1" :inactive-value="0" :disabled="exportSwitchDisabled" v-model="form.exportSwitch"></el-switch>
      </el-form-item>
      <el-form-item v-if="isHasPermissions('contract:entry')">
        <span slot="label">
          <span class="ljdw-mr-4">合同录入</span>
          <el-tooltip placement="top">
            <div slot="content">
              合同录入是指录入合同编号，点击确认提交后，服务商就可以正常发起提现。
            </div>
            <PcFinanceTip size="12" /> </el-tooltip
        ></span>
        <el-button v-if="!contractNumber" type="text" @click="handleEntryContract">录入</el-button>
        <span v-else>{{ contractNumber }} <el-button type="text" icon="el-icon-edit" @click="handleEntryContract()"/></span>
      </el-form-item>
    </el-form>
    <AddContractDialog ref="addContractDialogRef" :visible.sync="addContractDialogVisible" :value="contractNumber" @save="handleSaveContract" />
  </div>
</template>

<script>
import { PcFinanceTip } from '@/assets/icon'
import AddContractDialog from './AddContractDialog.vue'
import permissionMixin from '@/mixins/permission'
export default {
  mixins: [permissionMixin],
  components: { PcFinanceTip, AddContractDialog },
  props: {
    editRecord: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        amountShowSwitch: 0,
        exportSwitch: 0
      },
      contractNumber: '',
      addContractDialogVisible: false,
      isEdit: false
    }
  },
  computed: {
    exportSwitchDisabled() {
      //开启导出表格功能必须先开启金额显示
      return this.form.amountShowSwitch === 0
    }
  },
  watch: {
    editRecord: {
      handler(val) {
        if (val) {
          this.form = { amountShowSwitch: val.amountShowSwitch, exportSwitch: val.exportSwitch }
          this.contractNumber = val.contractNumber
        }
      },
      immediate: true
    }
  },
  methods: {
    getFormData() {
      return this.form
    },
    amountShowSwitchChange(e) {
      if (!e) {
        this.form.exportSwitch = 0
      }
    },
    handleEntryContract() {
      this.addContractDialogVisible = true
    },
    handleSaveContract(contractNumber) {
      this.contractNumber = contractNumber
    },
    getContractNumber() {
      if (this.contractNumber) {
        return this.contractNumber
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.service-provider-settins {
  // margin-top: 15px;
  ::v-deep .el-form {
    .el-form-item {
      margin-bottom: 5px;
    }
  }
}
</style>
