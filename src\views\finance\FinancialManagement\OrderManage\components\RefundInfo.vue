<template>
  <div class="refund-info">
    <div class="refund-info-title">退款信息</div>
    <div class="refund-info-content">
      <div class="refund-info-item">
        <span style="margin-right: 100px;">申请人：{{ refundInfo.applicant }}</span>
        <span>提交时间：{{ refundInfo.applyTime | UTCTimeToLocalTime }}</span>
      </div>
      <div class="refund-info-item">退款金额：￥{{ refundInfo.applyAmount }}</div>
      <div class="refund-info-item">
        <base-alert style="width: 600px;">
          <span>一旦勾选退掉该权益，用户<span style="color: #FD736E;">所有订单</span> 里含有该权益的都会被收回。且不可回退</span>
        </base-alert>
        <el-checkbox-group v-model="refundInfo.equityRecovery" disabled>
          <el-checkbox v-for="recovery in recoveryList" :key="recovery.funcId" :label="recovery.funcId">{{ recovery.funcName }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="refund-info-item">备注：{{ refundInfo.remark }}</div>
      <div class="refund-info-item refund-info-file">
        <span style="flex-shrink: 0;">附件：</span>
        <div class="refund-file-item-container" v-if="refundInfo.files && refundInfo.files.length">
          <span
            class="ljdw-color-blue cursor-point refund-file-item"
            style="margin-right: 10px;"
            v-for="file in refundInfo.files"
            :key="file.url"
            @click="clickDownload(file.url, file.fileName)"
            >{{ file.fileName }}<PcButtonDown size="16"
          /></span>
        </div>
        <span v-else>-</span>
      </div>
    </div>
  </div>
</template>

<script>
import { PcButtonDown } from '@/assets/icon'
import { _getRefundInfo, _getEquityRecoveryList } from '@/api/order.js'
export default {
  components: { PcButtonDown },
  props: {
    orderInfo: {
      type: Object,
      default: () => {}
    },
    refundInfo: {
      type: Object,
      default: () => {}
    },
    orderId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      recoveryList: []
    }
  },
  mounted() {
    this.getEquityRecoveryList()
  },
  methods: {
    async getEquityRecoveryList() {
      const { ret, data } = await _getEquityRecoveryList({ busTradeNo: this.orderInfo.busTradeNo })
      if (ret === 1) {
        this.recoveryList = data
      }
    },
    clickDownload(url, name) {
      console.log(url, name)
      // const aLink = document.createElement('a')
      // aLink.setAttribute('href', url)
      // aLink.setAttribute('download', name)
      // aLink.setAttribute('crossOrigin', 'anonymous')
      // document.body.appendChild(aLink)
      // aLink.click()
      // aLink.remove()
      fetch(url).then(res =>
        res.blob().then(blob => {
          var a = document.createElement('a')
          var url = window.URL.createObjectURL(blob)
          a.href = url
          a.download = name
          a.click()
          window.URL.revokeObjectURL(url)
        })
      )
    },
    formatterStatus(status) {
      const statusMap = {
        1: '退款成功',
        2: '退款失败',
        3: '退款申请'
      }
      return statusMap[status]
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-info {
  border-bottom: 1px solid #e9edf7;
  margin-bottom: 20px;
  &-title {
    font-weight: 500;
    margin-bottom: 20px;
  }
  &-content {
    padding-left: 20px;
  }
  &-item {
    margin-bottom: 20px;
  }
  ::v-deep .el-checkbox-group {
    margin-top: 10px;
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: $primary !important;
      border-color: $primary !important;
    }
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
      border-color: #fff;
    }
    .el-checkbox__input.is-disabled + span.el-checkbox__label {
      color: $primary !important;
    }
  }
  .refund-info-file {
    display: flex;
  }
  .refund-file-item-container {
    margin-top: -2px;
  }
  .refund-file-item {
    display: flex;
    align-items: center;
    // flex-wrap: wrap;
    line-height: 20px;
  }
  ::v-deep .swd-icon {
    .svg-fill {
      fill: $primary;
    }
  }
}
</style>
