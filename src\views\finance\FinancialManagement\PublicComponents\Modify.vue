<template>
  <div class="order-detail">
    <base-dialog
      @cancel="handleClose"
      @ok="confirmModify"
      :title="$t('9NTH28CxVugtC2EK38qjg')"
      :visible.sync="dialogVisible"
      width="582px"
      :before-close="handleClose"
      @open="handleOpen"
    >
      <div class="batch-import">
        <el-form ref="modifyForm" label-position="right" label-width="80px" :model="modifyForm" :rules="rules">
          <el-form-item prop="obj" :label="'对象' + ':'">
            <el-radio-group v-model="modifyForm.obj" @input="handleTypeChange">
              <el-radio :label="1">{{ 'IMEI' }}</el-radio>
              <el-radio :label="2">{{ '手机号' }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('o7LB26olpIL6MYrh3AEkB') + ':'" prop="packId">
            <div class="package">
              <CopyPackage style="width: 320px" :dropWidth="320" :packType="2" :onSaleType="1" ref="CopyPackage" :isCur="true" @select="handleCopyPackage" />
              <el-button type="text" @click="showDetailDialog(1)">{{ $t('d2rfxOdgIK8sx-aXn_UjT') }}</el-button>
            </div>
          </el-form-item>
          <el-form-item style="border:0;width:100%;" class="card-type" :label="$t('TlgmpSebIew_qw82Z7273') + ':'">
            <el-radio-group v-model="modifyForm.uploadType" @change="handleUploadType">
              <el-radio :label="1" class="first-radio">{{ $t('hHj3x3eBVUf2fJQt7aHYK') }}</el-radio>
              <el-radio :label="2">{{ $t('PSBfdFXVla4Vk5sigF3Ct') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="form-textarea" prop="imeiSerial" v-if="modifyForm.uploadType === 1">
            <el-input
              :placeholder="modifyForm.obj === 1 ? $t('asdioi_asdjhsjiohs4ij', [500]) : '一行输入一个手机号(建议每次不超过500个手机号)'"
              type="textarea"
              v-model="modifyForm.imeiSerial"
              :rows="6"
              @blur="clearInput"
              style="width:300px;vertical-align:middle;"
            ></el-input>
            <div style="color: #C5C5C5">{{ $t('VLNvCttQNLcWxHSN-fPOl') }}</div>
          </el-form-item>
          <el-form-item v-if="modifyForm.uploadType === 2">
            <el-upload
              :action="uploadUrl"
              :before-upload="handleBeforeUpload"
              :on-remove="handleFileRemove"
              :limit="1"
              :on-success="handleFileSuccess"
              :file-list="fileList"
            >
              <el-button type="primary">{{ $t('urtoz-lPdO_PHQIlaz5Zs') }}</el-button>
              <div slot="tip" class="el-upload__tip">{{ $t('1926HGNuX84IOWFR5BjL5') }}</div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div class="el-upload__tip" style="line-height: 18px;">注：仅支持对IMEI号赠送硬件相关套餐服务；仅支持对手机号赠送手机定位相关套餐</div>
        <batch-import-dialog :dialogVisible.sync="showBatch" @confirm="batchSearchImei"></batch-import-dialog>
        <result-dialog :dialogVisible.sync="resultDialog" :icon="resultIcon" :content="resultContent" :succeedFun="confirmWarnFun" :detail="resultDetail" />
        <warning-dialog :dialogVisible.sync="warningShow" :icon="icon" :content="dialogContent" :succeedFun="confirmWarnFun" />
        <batch-result :title="batchTitle" :columns="resultProps" :dialogVisible.sync="resultShow" :content="batchContent" :deviceData="resultArr" />
      </div>
    </base-dialog>
  </div>
</template>

<script>
import { _modifyPackage, _getAllPackage } from '@/api/order.js'
import '@/filter/type.js'
import { mapGetters, mapState } from 'vuex'
import WarningDialog from '@/components/Dialogs/WarningDialog'
import BatchImportDialog from '@/components/Bussiness/BatchImportDialog'
import BatchResult from '@/components/Dialogs/BatchResult'
import ResultDialog from '@/components/Dialogs/ResultDialog'
import CopyPackage from '../../PackageManage/components/CopyPackage.vue'
import dayjs from 'dayjs'
export default {
  props: {
    visible: {
      type: Boolean
    },
    addUserInfo: {
      type: Object,
      default: null
    }
  },
  components: { BatchImportDialog, WarningDialog, BatchResult, ResultDialog, CopyPackage },
  data() {
    return {
      isLoading: false,
      packInfo: {}, // 当前选择的套餐信息
      machineSearch: '',
      deviceData: [],
      fileList: [], // 文件上传列表
      modifyForm: {
        // packType: 2, // 套餐类型
        // baseOrCompose: 2, // 套餐类型
        uploadType: 1, // 上传方式
        packId: undefined, // 套餐内容id
        imeiSerial: '', //表单项的值放回表单对象里
        obj: 1 //通过什么查询方式修改，1-imei，2-手机
      },
      uploadFile: undefined, // 上传的文件
      machineName: '',
      // 初始化设备下拉框内容
      machines: [],
      // 过滤后设备下拉框内容
      filterMachines: [],
      rules: {
        imeiSerial: [{ required: true, message: this.$t('iAVxi7DIE0592dotpSI-W'), trigger: 'change' }],
        packId: [{ required: true, message: this.$t('A_9Rad7zVPj2GBmUGpEVZ'), trigger: 'submit' }],
        obj: [{ required: true, message: this.$t('lg.pleaseChoose'), trigger: 'change' }]
      },
      showBatch: false,
      warningShow: false,
      confirmWarnFun: null,
      icon: null,
      resultDialog: false,
      resultShow: false,
      dialogContent: null,
      resultIcon: 'succeed',
      targetUser: '',
      permissionArr: [],
      resultProps: [
        {
          prop: 'imei',
          label: this.$t('TlgmpSebIew_qw82Z7273')
        },
        {
          prop: 'msg',
          label: this.$t('MRqCFQeb7tjZ4vzCQR9Rn')
        }
      ],
      resultArr: [],
      batchContent: null,
      batchTitle: this.$t('lfSrcQrITYhRu4d4uMcDs'),
      resultContent: '',
      resultDetail: '',
      wireless: null,
      selectUser: null,
      toClientServiceType: 0,
      packageOptions: [
        { label: this.$t('QNLyVdYTL-D09KKJszyHr'), value: 2 },
        { label: this.$t('rYEVE8FCA2SXW955yf98B'), value: 1 },
        { label: this.$t('SnUXBvZwZ_rci24Ivvgrf'), value: 3 }
      ],
      packContentArr: [],
      uploadUrl: '/carManager/editCarBatch.do'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val) // openCardDialog改变的时候通知父组件
      }
    },
    ...mapGetters(['name', 'roles', 'userInfo']),
    ...mapState({
      permissions: state => state.permissions
    }),
    packContentOptions() {
      if (this.modifyForm.packType === 1) {
        return this.packContentArr.filter(item => item.baseOrCompose == 1 && item.packType == 1) || []
      } else if (this.modifyForm.packType === 2) {
        return this.packContentArr.filter(item => item.baseOrCompose == 2) || []
      } else if (this.modifyForm.packType === 3) {
        return this.packContentArr.filter(item => item.baseOrCompose == 1 && item.packType == 7) || []
      }
      return []
    }
  },
  watch: {
    'modifyForm.imeiSerial': function(val) {
      let imeiReg = this.modifyForm.obj === 1 ? /^[\d]{15}$/ : /^[\d]{11}$/
      if (val) {
        this.deviceData = this.modifyForm.imeiSerial.replace(/[ ]/g, '').split('\n')
        this.deviceData = this.deviceData.filter(item => {
          return item && imeiReg.test(item)
        })
      } else {
        return []
      }
    }
  },
  mounted() {
    this.permissionArr = this.permissions.filter(item => {
      return item.type === 2
    })
    this.permissionArr = this.permissionArr.map(item => item.perms)
    if (process.env.NODE_ENV === 'development') {
      this.uploadUrl = '/api/carManager/editCarBatch.do'
    }

    // this.getAllPackage()
  },
  methods: {
    handleTypeChange(val) {
      this.$refs.CopyPackage.filterPackType(val === 1 ? 2 : 9)
    },
    // 复制套餐
    handleCopyPackage(info) {
      if (info) {
        this.packInfo = info || {}
        this.modifyForm.packId = info.packId
      } else {
        this.packInfo = {}
        this.modifyForm.packId = undefined
      }
    },
    // 打开套餐详情弹窗
    showDetailDialog(type) {
      this.$emit('showDetailDialog', { type, visible: true, info: this.packInfo })
    },
    handleBaseOrCompose() {
      this.modifyForm.packId = undefined
    },
    // 修改套餐
    confirmModify() {
      if (!this.deviceData.length && this.modifyForm.uploadType === 1) {
        this.icon = 'warning'
        this.warningShow = true
        this.dialogContent = this.$t('_L3SCIlbztecHYB9zhGmZ')
        this.confirmWarnFun = null
        return
      }
      if (this.deviceData.length > 1000) {
        this.$message.warning(this.$t('asduiunkj_sojdAojdjio', [1000]))
        return
      }
      this.$refs.modifyForm.validate(async valid => {
        if (valid) {
          this.isLoading = true
          let params = JSON.parse(JSON.stringify(this.modifyForm))
          // 不同套餐类型不同参数
          if (this.modifyForm.packType === 1 || this.modifyForm.packType === 3) {
            params['baseOrCompose'] = 1
          } else if (this.modifyForm.packType === 2) {
            params['baseOrCompose'] = 2
          }
          // 不同上传方式不同参数
          if (this.modifyForm.uploadType === 1) {
            params['imeis'] = this.modifyForm.imeiSerial
              .split('\n')
              .filter(item => !!item)
              .join(',')
          } else if (this.modifyForm.uploadType === 2) {
            params['file'] = this.uploadFile
          }
          delete params.imeiSerial
          // params = filterParams(params)
          let formData = new FormData()
          Object.keys(params).forEach(key => {
            formData.append(key, params[key])
          })
          let res = await _modifyPackage(formData)
          console.log(res)
          if (res.type === 'application/json') {
            const reader = new FileReader()
            reader.onload = () => {
              const result = JSON.parse(reader.result)
              if (result.ret == 1) {
                this.$message.success(this.$t('lg.success'))
                this.resetForm()
                this.$emit('success', { activeName: 'fifth' })
              } else {
                this.$message.error(result.msg)
              }
            }
            reader.readAsText(res)
          } else {
            const blob = new Blob([res])
            const url = URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = `${this.$t('importRemindFile')}-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`
            link.click()
            URL.revokeObjectURL(url)
            this.resetForm()
            this.$message.error(this.$t('regaliErrorTip'))
            this.$emit('success', { activeName: 'fifth' })
          }
          this.isLoading = false
        } else {
          return false
        }
      })
    },
    // 获取所有套餐
    async getAllPackage() {
      let res = await _getAllPackage()
      if (res.ret == 1) {
        this.packContentArr = res.data || []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 选择imei上传方式
    handleUploadType(val) {
      if (val === 1) {
        return
      } else if (val === 2) {
        this.modifyForm.imeiSerial = ''
      }
    },
    // 文件上传成功
    handleFileSuccess(res, file) {
      this.uploadFile = file.raw
    },
    // 文件移除
    handleFileRemove() {
      this.uploadFile = null
    },
    // 上传校验
    handleBeforeUpload(file) {
      let type = file.type || (file.raw && file.raw.type) || ''
      let isValid = [
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ].indexOf(type)

      if (!type || isValid == -1) {
        this.$message.warning('上传失败')
        return false
      }
      if (file.size / 1024 / 1024 > 3) {
        return false
      }
      return true
    },
    addUser() {
      this.$emit('showAddCustomer', this.selectUser)
    },
    querySearch(queryString) {
      this.filterMachines = queryString ? this.createFilter(queryString) : this.machines
    },
    createFilter(queryString) {
      return this.machines.filter(machine => {
        return machine.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1
      })
    },
    clearInput() {
      if (!this.modifyForm.imeiSerial) {
        this.deviceData = []
      }
    },
    isRepeat(value) {
      return this.deviceData.some(item => {
        return item.imei === value
      })
    },
    deleteDevice(index) {
      this.deviceData.splice(index, 1)
    },
    addSingleDevice() {
      let imeiReg = /^[\d]{15}$/
      if (!imeiReg.test(this.singleDevice)) {
        this.icon = 'warning'
        this.warningShow = true
        this.dialogContent = this.$t('Qd6gmq65WiHFshYKYFr4F')
        this.singleDevice = null
        return
      }
      if (this.isRepeat(this.singleDevice)) {
        this.icon = 'warning'
        this.warningShow = true
        this.dialogContent = this.$t('EGou8TaGJwBFQKJoHe1Gy')
        this.singleDevice = null
        return
      }
      this.deviceData.push({ imei: this.singleDevice })
      this.singleDevice = null
    },
    showBatchAdd() {
      this.showBatch = true
    },
    batchSearchImei(imeis) {
      imeis = imeis.filter(item => {
        return !this.isRepeat(item)
      })
      imeis = imeis.map(item => {
        return {
          imei: item
        }
      })
      this.deviceData = this.deviceData.concat(imeis)
    },
    resetForm() {
      this.deviceData = []
      let packId = this.modifyForm.packId
      this.modifyForm = {
        // packType: 2,
        // baseOrCompose: 2, // 套餐类型
        uploadType: 1, // 上传方式
        packId, // 套餐内容id
        imeiSerial: '', //表单项的值放回表单对象里
        obj: 1
      }
      this.$nextTick(() => {
        this.$refs.modifyForm.clearValidate()
      })
    },
    handleOpen() {
      this.$nextTick(() => {
        this.$refs.CopyPackage.refresh()
      })
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.machine-popover {
  width: 580px;
  .machine-search {
    padding: 5px;
  }
  ::v-deep .el-select-dropdown__item:hover {
    color: $primary;
    background-color: #ffffff;
  }
}
.batch-import {
  margin-top: 5px;
  .user-input {
    display: flex;
  }
  .machine-input {
    width: 312px !important;
  }
  .supported-equipment {
    margin-left: 11px;
    .supported-equipment-icon {
      margin-right: 5px;
    }
  }

  ::v-deep .el-button--primary {
    background: $primary;
  }
  ::v-deep .el-form {
    width: 100%;
    position: relative;
    height: 100%;
    .el-form-item {
      box-sizing: border-box;
      margin-bottom: 16px;
      .el-form-item__label {
        white-space: nowrap;
        font-size: 12px;
        padding: 10px 12px 9px 12px;
        line-height: 12px;
      }
      .el-form-item__content {
        line-height: 32px;
        .warp-name {
          width: 311px;
          .username {
            width: 100%;
          }
        }
        .el-textarea__inner {
          min-height: 63px !important;
          font-family: 'Notosans Regular';
        }
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          vertical-align: top;
          font-family: 'Notosans Regular';
        }
        .card-type {
          .el-radio__label {
            color: #606266;
          }
        }
      }
      &.form-button {
        margin-bottom: 0;
        .form-button_input {
          flex: 1;
          margin-right: 27px;
        }
        .el-radio {
          color: #606266;
        }
        .form-button_date {
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          flex-wrap: nowrap;
          .second-radio {
            margin: 0 16px;
          }
          .el-date-editor--date {
            margin-left: 16px;
            width: 162px;
          }
        }
      }
    }
  }
  .el-table {
    margin-bottom: 0;
  }
  .batch-sale_table {
    margin-bottom: 12px;
    margin-top: 16px;
  }
  .button {
    margin-left: 20px;
    display: flex;
  }
}
.first-radio {
  margin-right: 24px;
}
.package {
  display: flex;
  align-items: center;
}
</style>
