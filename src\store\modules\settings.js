import defaultSettings from '@/settings'
import { i18n } from '@/i18n' // 引入语言包

const { showSettings, tagsView, fixedHeader, sidebarLogo } = defaultSettings

// 环境变量常量定义
const REGION_DOMESTIC = 'domestic'
const REGION_OVERSEAS = 'overseas'

// 从环境变量获取地区配置
const CURRENT_REGION = process.env.VUE_APP_REGION || REGION_OVERSEAS

const state = {
  // theme: variables.theme,
  showNavBar: true,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  lang: 'cn',
  currencySymbol: CURRENT_REGION === REGION_DOMESTIC ? '￥' : '$',
  currencyParams: CURRENT_REGION === REGION_DOMESTIC ? 'CNY' : 'USD'
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  SET_LANG: (state, val) => {
    state.lang = val
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
