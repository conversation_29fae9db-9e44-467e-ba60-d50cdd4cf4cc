<template>
  <base-dialog
    class="c-device-import"
    custom-class="finance-import-device-dialog"
    :title="$t('UCE4NbQTRf2L6QFdVr56w')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="630px"
    :before-close="handleClose"
    :header="false"
    @open="handleOpen"
    @ok="handleOk"
    @cancel="handleClose"
    :okText="activeName === 'IMEITransfer' ? '转换' : '确定'"
  >
    <div
      class="finance-import-device-dialog-body"
      v-loading="dialogLoading"
      :element-loading-text="$t('4weboexar_U03Ho0q5Shn')"
      element-loading-spinner="el-icon-loading"
    >
      <svg-icon icon-class="icon_close" class="detail-close-icon" @click="handleClose"></svg-icon>
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane lazy :label="$t('UCE4NbQTRf2L6QFdVr56w')" name="import">
          <div class="batch-import-container">
            <div class="batch-import">
              <el-form ref="importForm" label-width="80px" label-position="right" :model="importForm" :rules="rules">
                <el-form-item prop="targetUserId" class="user-input" :label="$t('C1iN37-gSdpTsqWkrHJ9e') + ':'">
                  <SearchUser ref="userTree" :showSearch="false" @autoSelectUser="userSelect" style="width:284px" />
                  <!-- 新增用户 -->
                  <el-link v-if="permissionArr.indexOf('user:add') !== -1" @click="addUser" :underline="false" class="add-user">
                    <div class="svg-div">
                      <svg-icon icon-class="add_user" className="bottom-svg user-color">{{ $t('lg.limits.Add_customer') }}</svg-icon>
                    </div>
                    <span class="text" style="line-height:17px;">{{ $t('sdgerhtnzbZKrtr4A445D') }}</span>
                  </el-link>
                </el-form-item>
                <el-form-item prop="machineName" :placeholder="$t('MTr2Ks8d0Ny8erQoUJ2kJ')" :label="$t('7fJLSw_PZ8o4rUM051emO') + ':'">
                  <el-select
                    v-model="importForm.machineName"
                    @change="getDeviceLabel"
                    ref="machine"
                    :placeholder="$t('7fJLSw_PZ8o4rUM051emO')"
                    class="machine-input"
                  >
                    <div class="machine-popover">
                      <div class="machine-search">
                        <el-input :placeholder="$t('bzVAxsJEtQtawIDU5RBt1')" v-model="machineSearch" @input="querySearch"> </el-input>
                      </div>
                      <el-row v-if="filterMachines.length > 0">
                        <el-col v-for="item in filterMachines" :key="item.label" :span="4">
                          <el-option :key="item.label" :label="item.machineTypeName" :value="item.machineTypeId"> </el-option>
                        </el-col>
                      </el-row>
                      <el-row v-else>
                        <el-col>
                          <el-option :disabled="true" value="">
                            <span></span>
                          </el-option>
                        </el-col>
                      </el-row>
                    </div>
                  </el-select>
                  <div v-if="importForm.domain !== null && importForm.machineName !== ''" class="domain">
                    <img src="@/assets/svg/warning_blue.svg" alt="warning_blue" />
                    <span class="nowrap">{{ $t('werERGEDjty47thwhrw2r') + '： ' + importForm.domain }}</span>
                  </div>
                </el-form-item>
                <el-form-item :label="$t('o7LB26olpIL6MYrh3AEkB') + ':'" prop="clientPackId">
                  <div class="package">
                    <CopyPackage style="width: 284px" ref="CopyPackage" :packType="'1,11'" :onSaleType="1" :isCur="true" @select="handleCopyPackage" />
                    <el-button type="text" @click="showDetailDialog(1)">{{ $t('d2rfxOdgIK8sx-aXn_UjT') }}</el-button>
                  </div>
                </el-form-item>
                <el-form-item class="form-textarea" prop="imeiSerial" :label="$t('TlgmpSebIew_qw82Z7273') + ':'">
                  <el-input
                    :placeholder="$t('asdioi_asdjhsjiohs4ij', [2000])"
                    type="textarea"
                    v-model="importForm.imeiSerial"
                    :rows="6"
                    @blur="clearInput"
                    style="width:284px;vertical-align:middle;"
                  ></el-input>
                </el-form-item>
              </el-form>
              <batch-import-dialog :dialogVisible.sync="showBatch" @confirm="batchSearchImei"></batch-import-dialog>
              <result-dialog
                :dialogVisible.sync="resultDialog"
                :icon="resultIcon"
                :content="resultContent"
                :succeedFun="confirmWarnFun"
                :detail="resultDetail"
              />
              <warning-dialog :dialogVisible.sync="warningShow" :icon="icon" :content="dialogContent" :succeedFun="confirmWarnFun" />
              <batch-result :title="batchTitle" :columns="resultProps" :dialogVisible.sync="resultShow" :content="batchContent" :deviceData="resultArr" />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('c7A7o4pVd1rpivRvna5Gh')" name="connect">
          <div class="iccid-tab-container">
            <div class="iccid-tab-body">
              <div class="upload-container">
                <el-upload
                  class="upload-demo"
                  action=""
                  ref="upload"
                  accept=".xls,.xlsx"
                  :on-exceed="handleExceed"
                  :auto-upload="false"
                  :on-change="handleChange"
                  :on-remove="handleRemove"
                  :multiple="false"
                  :limit="1"
                  :file-list="fileList"
                >
                  <el-button class="iccid-upload-btn" size="small" type="primary"
                    ><PcUpload size="14" style="margin-right:5px" />{{ $t('lg.upload') }}</el-button
                  >
                  <div slot="tip" class="el-upload__tip" style="color: #8C8C8C;">{{ $t('qC-26Qu65eWrPudHuLnuV', ['xls、xlsx']) }}</div>
                </el-upload>
              </div>
              <div class="tips">{{ $t('s02oAL3-DrwNUsBNxn6Bw') }}</div>
              <div class="download-tmp" style="color:#0068ff;line-height:17px;">
                <a :href="tempExcelUrl" type="download">{{ $t('4dGkAxc8S8l2COYbcDF-g') }}</a>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane lazy :label="'SN批量关联MAC'" name="snConnect">
          <SnConnect ref="snConnect" />
        </el-tab-pane>
        <el-tab-pane lazy :label="'IMEI转换'" name="IMEITransfer">
          <IMEITransfer ref="IMEITransfer" @close="dialogVisible = false" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </base-dialog>
</template>

<script>
import '@/filter/type.js'
import SearchUser from '@/components/Bussiness/SearchUser'

import WarningDialog from '@/components/Dialogs/WarningDialog'
import BatchImportDialog from '@/components/Bussiness/BatchImportDialog'
import { DateToFormatString, TimeStringToUTC } from '@/utils/common.js'
import BatchResult from '@/components/Dialogs/BatchResult'
import ResultDialog from '@/components/Dialogs/ResultDialog'
import CopyPackage from '../../PackageManage/components/CopyPackage.vue'
import { PcUpload } from '@/assets/icon'
import SnConnect from './SnConnect.vue'
import IMEITransfer from './IMEITransfer.vue'

import { _improtCarIccidExcel, _getCarIccidTmpExcelUrl } from '@/api/device.js'
import { getMachineType, getDeviceByIMEI, getIMEIBatch, batchImport, getUserPoint, salerImportBatch } from '@/api/asset.js'
import { mapGetters, mapState } from 'vuex'

export default {
  props: {
    visible: {
      type: Boolean
    },
    addUserInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { SearchUser, BatchImportDialog, WarningDialog, BatchResult, ResultDialog, CopyPackage, PcUpload, SnConnect, IMEITransfer },
  data() {
    return {
      isLoading: false,
      packInfo: {}, // 当前选择的套餐信息
      machineSearch: '',
      deviceData: [],
      singleDevice: null,
      importForm: {
        targetUserId: null,
        platformTime: DateToFormatString(new Date().getTime() + 31556736000),
        machineType: null,
        imeis: null,
        cardType: null,
        machineName: '',
        domain: null,
        // toClientServiceType: 1,
        imeiSerial: '', //表单项的值放回表单对象里
        clientPackId: undefined
      },
      machineName: '',
      // 初始化设备下拉框内容
      machines: [],
      // 过滤后设备下拉框内容
      filterMachines: [],
      rules: {
        targetUserId: [{ required: true, message: this.$t('fXrxuyIkLD3QEgjPvKuJa'), trigger: 'change' }],
        machineName: [{ required: true, message: this.$t('MTr2Ks8d0Ny8erQoUJ2kJ'), trigger: 'change' }],
        clientPackId: [{ required: true, message: this.$t('A_9Rad7zVPj2GBmUGpEVZ'), trigger: 'change' }],
        platformTime: [{ required: true, message: this.$t('60Cg-7z2oV7N30quX6B1c'), trigger: 'change' }],
        imeiSerial: [{ required: true, message: this.$t('iAVxi7DIE0592dotpSI-W'), trigger: 'change' }]
      },
      showBatch: false,
      warningShow: false,
      confirmWarnFun: null,
      icon: null,
      resultDialog: false,
      resultShow: false,
      dialogContent: null,
      resultIcon: 'succeed',
      targetUser: '',
      permissionArr: [],
      resultProps: [
        {
          prop: 'imei',
          label: this.$t('TlgmpSebIew_qw82Z7273')
        },
        {
          prop: 'msg',
          label: this.$t('MRqCFQeb7tjZ4vzCQR9Rn')
        }
      ],
      resultArr: [],
      batchContent: null,
      batchTitle: this.$t('lfSrcQrITYhRu4d4uMcDs'),
      resultContent: '',
      resultDetail: '',
      wireless: null,
      // imeiSerial: '',
      showDevicePop: false,
      selectUser: null,
      // toClientServiceType: 0,
      packageOptions: [
        {
          label: this.$t('vCx8UWyPG3dwdwKY7yN7I'),
          value: 1
        },
        {
          label: this.$t('yFydQode-qpYNF6prCBa8'),
          value: 2
        }
      ],
      activeName: 'import',
      fileList: [],
      file: '',
      tempExcelUrl: '',
      dialogLoading: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val) // openCardDialog改变的时候通知父组件
      }
    },
    ...mapGetters(['name', 'roles', 'userInfo']),
    ...mapState({
      permissions: state => state.permissions
    })
  },
  watch: {
    'importForm.imeiSerial': function(val) {
      let imeiReg = /^[a-zA-Z0-9]{15}$/
      if (val) {
        this.deviceData = this.importForm.imeiSerial.replace(/[ ]/g, '').split('\n')
        this.deviceData = this.deviceData.filter(item => {
          return item && imeiReg.test(item)
        })
      } else {
        return []
      }
    },
    addUserInfo: {
      handler: function(newVal) {
        if (newVal) {
          this.userSelect(newVal)
          this.$refs.userTree.setUser(newVal)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.permissionArr = this.permissions.filter(item => {
      return item.type === 2
    })
    this.permissionArr = this.permissionArr.map(item => item.perms)
    getMachineType().then(res => {
      if (res.ret) {
        let resultArr = []
        resultArr = res.data
        resultArr.sort((x1, x2) => {
          return x1.machineTypeName.localeCompare(x2.machineTypeName)
        })
        resultArr.forEach(item => {
          item.value = item.machineTypeName
        })
        this.machines = this.filterMachines = resultArr
      }
    })
    // if (this.roles === 0 && this.userInfo.parentId === -1) {
    //   this.importForm.cardType = 3
    // } else if (this.roles === 1 || this.roles === 8 || (this.roles === 0 && this.userInfo.parentId !== -1)) {
    //   this.importForm.cardType = 1
    // }
    this.importForm.cardType = 2
  },
  methods: {
    handleOk() {
      if (this.activeName === 'import') {
        this.confirmBatchImport()
      } else if (this.activeName === 'connect') {
        this.submitUpload()
      } else if (this.activeName === 'snConnect') {
        this.$refs.snConnect.submitUpload()
      } else if (this.activeName === 'IMEITransfer') {
        this.$refs.IMEITransfer.submitUpload()
      }
      // this.activeName === 'import' ? this.confirmBatchImport() : this.submitUpload()
    },
    async handleTabClick({ name }) {
      if (name === 'connect' && !this.tempExcelUrl) {
        const { ret, data } = await _getCarIccidTmpExcelUrl()
        if (ret === 1 && data) {
          this.tempExcelUrl = data
        }
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('SwOWzfqnAh0jCThFr4A6v', [files.length]))
    },
    async submitUpload() {
      // this.$refs.upload.submit()
      if (!this.fileList.length) {
        this.$message.error(this.$t('zSwW84PPwEdSY7KXU0_NP'))
        return
      }
      console.log(this.file)
      let isValid = this.file.name.indexOf('.xls') !== -1 || this.file.name.indexOf('.xlxs') !== -1
      if (!isValid) {
        this.$message.warning(this.$t('qC-26Qu65eWrPudHuLnuV', ['xls、xlsx']))
        return
      }
      if (this.file.size / 1024 > 100) {
        this.$message.error(this.$t('GbKT46C8x66GgpjgBjOqn', [100, 'kb']))
        return
      }
      let fileObj = { file: { file: this.file.raw } }
      this.dialogLoading = true
      const { ret, data, msg } = await _improtCarIccidExcel(fileObj)
      if (ret === 1) {
        if (data) {
          this.$message.error(this.$t('ek_N_GSAZAG9NyQXKbnJE'))
          const link = document.createElement('a')
          link.href = data
          link.download = this.$t('Bwg2kt_P1yhAb5-hjPOgO')
          link.click()
        } else {
          this.$message.success(this.$t('xbF35iFGLVAJnswRVH0-u'))
          this.dialogVisible = false
        }
      } else {
        this.$message.error(msg ? msg : this.$t('NoFYSU79Nfj9dt-xcgrQ8'))
      }
      this.fileList = []
      this.dialogLoading = false
    },
    handleChange(file, fileList) {
      this.file = file
      this.fileList = fileList
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    // 复制套餐
    handleCopyPackage(info) {
      if (info) {
        this.packInfo = info || {}
        this.importForm.clientPackId = info.packId
      } else {
        this.packInfo = {}
        this.importForm.clientPackId = undefined
      }
    },
    // 打开套餐详情弹窗
    showDetailDialog(type) {
      this.$emit('showDetailDialog', { type, visible: true, info: this.packInfo })
    },
    addUser() {
      this.$emit('showAddCustomer', this.selectUser)
    },
    querySearch(queryString) {
      this.filterMachines = queryString ? this.createFilter(queryString) : this.machines
    },
    createFilter(queryString) {
      return this.machines.filter(machine => {
        return machine.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1
      })
    },
    clearInput() {
      if (!this.importForm.imeiSerial) {
        this.deviceData = []
      }
    },
    userSelect(user) {
      this.selectUser = user
      this.importForm.targetUserId = user.userId
      this.targetUser = user.userName
    },
    isRepeat(value) {
      return this.deviceData.some(item => {
        return item.imei === value
      })
    },
    deleteDevice(index) {
      this.deviceData.splice(index, 1)
    },
    addSingleDevice() {
      let imeiReg = /^[\d]{15}$/
      if (!imeiReg.test(this.singleDevice)) {
        this.icon = 'warning'
        this.warningShow = true
        this.dialogContent = this.$t('Qd6gmq65WiHFshYKYFr4F')
        this.singleDevice = null
        return
      }
      if (this.isRepeat(this.singleDevice)) {
        this.icon = 'warning'
        this.warningShow = true
        this.dialogContent = this.$t('EGou8TaGJwBFQKJoHe1Gy')
        this.singleDevice = null
        return
      }
      this.deviceData.push({ imei: this.singleDevice })
      this.singleDevice = null
    },
    showDeviceList(status) {
      this.showDevicePop ? (this.showDevicePop = false) : (this.showDevicePop = true)
      this.$refs.deviceRadio.handleClose()
    },
    showBatchAdd() {
      this.showBatch = true
    },
    batchSearchImei(imeis) {
      imeis = imeis.filter(item => {
        return !this.isRepeat(item)
      })
      imeis = imeis.map(item => {
        return {
          imei: item
        }
      })
      this.deviceData = this.deviceData.concat(imeis)
    },
    confirmBatchImport() {
      this.$refs.importForm.validate(valid => {
        if (valid) {
          if (!this.deviceData.length) {
            this.icon = 'warning'
            this.warningShow = true
            this.dialogContent = this.$t('_L3SCIlbztecHYB9zhGmZ')
            this.confirmWarnFun = null
            return
          }
          this.icon = 'warning'
          this.confirmWarnFun = this.importCallback
          this.dialogContent = this.$t('mAyV3e2y7beLqKbttMEyP', [this.targetUser, this.deviceData.length, this.importForm.machineName])
          this.warningShow = true
        } else {
          return false
        }
      })
    },
    getDeviceLabel(e) {
      this.showDevicePop = false
      this.machines.forEach(item => {
        if (item.machineTypeId === e) {
          this.importForm.machineName = item.machineTypeName
          this.importForm.machineType = item.machineTypeId
          this.wireless = item.wireless
          this.importForm.domain = item.domainName + ': ' + item.locationPort
        }
      })
    },
    insupervisorImport() {
      getUserPoint()
        .then(res => {
          this.isLoading = true
          if (this.importForm.cardType === 1 && this.deviceData.length > res.data.commonImportCount) {
            this.resultIcon = 'failed'
            this.resultContent = this.$t('c9Ois07PLUg8-LT8ypZpx')
            this.resultDetail = this.$t('RnDDKP0_zNeMqMvt83zyQ', [this.$t('lg.yearCard'), this.deviceData.length])
            this.resultDialog = true
          } else if (this.importForm.cardType === 2 && this.deviceData.length > res.data.lifeImportCount) {
            this.resultIcon = 'failed'
            this.resultContent = this.$t('c9Ois07PLUg8-LT8ypZpx')
            this.resultDetail = this.$t('RnDDKP0_zNeMqMvt83zyQ', [this.$t('lg.lifetimeOfCard'), this.deviceData.length])
            this.resultDialog = true
          } else {
            this.salerBatchImport()
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    importCallback() {
      // this.postImport()
      if (this.roles === 0) {
        if (this.permissionArr.indexOf('account:batch:import') >= 0 && this.permissionArr.indexOf('admin:batch:import') >= 0) {
          this.postImport()
        } else if (this.permissionArr.indexOf('account:batch:import') >= 0 && this.permissionArr.indexOf('admin:batch:import') < 0) {
          this.insupervisorImport()
        } else if (this.permissionArr.indexOf('account:batch:import') < 0 && this.permissionArr.indexOf('admin:batch:import') >= 0) {
          this.postImport()
        }
      } else if (this.roles === 1 || this.roles === 8) {
        this.insupervisorImport()
      }
    },
    salerBatchImport() {
      let params = this.importForm
      params.platformTime = TimeStringToUTC(params.platformTime)
      params.wireless = this.wireless
      params.imeis = this.deviceData.join(',')
      params.imeiSerial = params.imeiSerial.trim()
      salerImportBatch(params).then(res => {
        if (res.ret) {
          if (res.data.length > 0) {
            let arr = []
            res.data.forEach(unit => {
              if (unit.code === '-20008') unit.msg = this.$t('mXaZcmYFfm7x55jOUq3WJ')
              unit.data.forEach(item => {
                arr.push({
                  imei: item,
                  msg: unit.msg
                })
              })
            })
            this.resultArr = arr
            this.batchContent = this.$t('ZrFQNTbU10MZPjYNkDZ0c', [this.deviceData.length - this.resultArr.length, this.resultArr.length])
            this.resultShow = true
            this.resetForm()
          } else {
            this.resultDialog = true
            this.resultIcon = 'succeed'
            this.resultContent = this.$t('5WGqM6dwWgfZNerSr08_j', [this.deviceData.length])
            this.resetForm()
          }
        } else {
          let error = {
            '-10027': this.$t('oC3aVgjq_fLItbKC4dzC0'),
            '-10018': this.$t('6LW1YZbmhuEq6LyLyYwqR'),
            '-20006': this.$t('_L3SCIlbztecHYB9zhGmZ'),
            '-20008': this.$t('mXaZcmYFfm7x55jOUq3WJ'),
            '-20020': this.$t('qmNtvRFOkHtbTvTeWLK0j'),
            '-10030': this.$t('kt7tkMjYVOmcxeQ8WIe42')
          }
          this.resultDialog = true
          this.resultIcon = 'failed'
          this.resultContent = error[res.code]
          this.resetForm()
        }
      })
    },
    postImport() {
      if (this.deviceData.length > 2000) {
        this.$message.warning(this.$t('asduiunkj_sojdAojdjio', [2000]))
        return
      }
      this.isLoading = true
      let params = this.importForm
      let imeis = [...new Set(this.deviceData)] // 去重
      params.platformTime = TimeStringToUTC(params.platformTime)
      params.imeis = imeis.join(',')
      params.imeiSerial = params.imeiSerial.trim()
      batchImport(params)
        .then(res => {
          if (res.ret) {
            if (res.data.length > 0) {
              let arr = []
              res.data.forEach(unit => {
                if (unit.code === '-20008') unit.msg = this.$t('mXaZcmYFfm7x55jOUq3WJ')
                unit.data.forEach(item => {
                  arr.push({
                    imei: item,
                    msg: unit.msg
                  })
                })
              })
              this.resultArr = arr
              this.batchContent = this.$t('ZrFQNTbU10MZPjYNkDZ0c', [this.deviceData.length - this.resultArr.length, this.resultArr.length])
              this.resultShow = true
              if (this.deviceData.length - this.resultArr.length > 0) {
                this.$emit('success', { activeName: 'sixth' })
              }
              this.resetForm()
            } else {
              this.resultIcon = 'succeed'
              this.resultContent = this.$t('lg.success')
              this.$emit('success', { activeName: 'sixth' })
              this.resetForm()
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    resetForm() {
      this.deviceData = []
      this.importForm = {
        targetUserId: null,
        platformTime: DateToFormatString(new Date().getTime() + 31556736000),
        machineType: null,
        imeis: null,
        cardType: null,
        machineName: '',
        domain: null,
        // toClientServiceType: 1,
        imeiSerial: '', //表单项的值放回表单对象里
        clientPackId: undefined
      }
      this.fileList = []
      // this.machineName = ''
      // this.imeiSerial = ''
      // if (this.roles === 0 && this.userInfo.parentId === -1) {
      //   this.importForm.cardType = 3
      // } else if (this.roles === 1 || this.roles === 8 || (this.roles === 0 && this.userInfo.parentId !== -1)) {
      //   this.importForm.cardType = 1
      // }
      this.importForm.cardType = 2
      this.$refs.userTree?.inputClear()
      this.$nextTick(() => {
        this.$refs.importForm?.clearValidate()
      })
    },
    handleMachineTypeManage() {
      let url = this.$router.resolve({ name: 'Machine', query: {} })
      console.log(url)
      window.open(url.href, '_blank')
    },
    handleClose() {
      this.resetForm()
      // this.$emit('update:visible', false)
      this.dialogVisible = false
    },
    handleOpen() {
      this.$nextTick(() => {
        this.$refs.userTree.setUser(this.userInfo)
        this.$refs.CopyPackage.refresh()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.machine-popover {
  width: 580px;
  .machine-search {
    padding: 5px;
  }
  ::v-deep .el-select-dropdown__item:hover {
    color: $primary;
    background-color: #ffffff;
  }
}
.batch-import {
  padding: 5px 24px 0;
  .user-input {
    display: flex;
    /*新增用户*/
    .add-user {
      position: absolute;
      left: 306px;
      top: 0px;
      ::v-deep .el-link--inner {
        display: flex;
      }
      .bottom-svg {
        width: 13px;
        height: 14px;
      }
      .text {
        margin-left: 6px;
      }
      .svg-div {
        width: 13px;
        overflow: hidden;
      }
      &:hover {
        .text {
          color: $primary;
        }
        .bottom-svg {
          &.user-color {
            filter: drop-shadow($primary 13px 0px);
            transform: translateX(-13px);
          }
        }
      }
    }
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .machine-input {
    width: 284px !important;
  }
  .el-link--inner {
    display: flex;
    align-items: center;
    .text {
      display: flex;
      align-items: center;
    }
  }
  .supported-equipment {
    margin-left: 11px;
    .supported-equipment-icon {
      margin-right: 5px;
    }
  }
  .domain {
    display: flex;
    align-items: center;
    margin-bottom: -15px;
    img {
      width: 14px;
      height: 14px;
    }
    span {
      margin-left: 3px;
      font-size: 12px;
      color: #606266;
    }
  }

  ::v-deep .el-button--primary {
    background: $primary;
  }
  ::v-deep .el-button {
    width: 65px;
    height: 32px;
    border-radius: 4px;
    font-family: PingFangSC-Regular, PingFang SC;
    .import {
      width: 33px;
      height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
    }
    &.right-buttton {
      margin-left: 13px;
    }
  }
  ::v-deep .el-button .span {
    width: 33px;
    height: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
    display: flex;
    justify-content: center;
  }
  ::v-deep .el-form {
    width: 100%;
    position: relative;
    height: 100%;
    .el-form-item {
      box-sizing: border-box;
      margin-bottom: 16px;
      .el-form-item__label {
        // white-space: nowrap;
        font-size: 12px;
        padding: 10px 12px 9px 0;
        line-height: 12px;
        flex-shrink: 0;
        word-break: normal;
      }
      .el-form-item__content {
        line-height: 32px;
        width: 100%;
        .warp-name {
          width: 311px;
          .username {
            width: 100%;
          }
        }
        .el-autocomplete {
          width: 100%;
          position: relative;
        }
        .el-select {
          width: 100%;
        }
        .el-textarea__inner {
          min-height: 63px !important;
          font-family: 'Notosans Regular';
        }
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          vertical-align: top;
          font-family: 'Notosans Regular';
        }
        .card-type {
          .el-radio__label {
            color: #606266;
          }
        }
      }
      &.form-button {
        margin-bottom: 0;
        .form-button_input {
          flex: 1;
          margin-right: 27px;
        }
        .el-radio {
          color: #606266;
        }
        .form-button_date {
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          flex-wrap: nowrap;
          .second-radio {
            margin: 0 16px;
          }
          .el-date-editor--date {
            margin-left: 16px;
            width: 162px;
          }
        }
      }
    }
  }
  .el-table {
    margin-bottom: 0;
  }
  .batch-sale_table {
    margin-bottom: 12px;
    margin-top: 16px;
  }
  .button {
    margin-left: 20px;
    display: flex;
    .el-button {
      width: auto;
    }
  }
}
.batch-import-tab-footer {
  height: 72px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}
.package {
  display: flex;
  align-items: center;
}
.iccid-tab-container {
  .iccid-tab-body {
    padding: 5px 24px 20px 69px;
    height: 269px;
  }
  ::v-deep .iccid-upload-btn > span {
    display: flex;
    align-items: center;
  }
  .el-upload__tip {
    line-height: 17px;
    margin-top: 4px;
  }
  .upload-container {
    margin-bottom: 20px;
  }
  .tips {
    color: #262626;
    line-height: 17px;
    font-size: 12px;
    margin-bottom: 8px;
  }
}
.finance-import-device-dialog-body {
  position: relative;
  min-height: 100px;
  background-color: #fff;
  .detail-close-icon {
    position: absolute;
    z-index: 1;
    right: 21px;
    top: 22px;
    cursor: pointer;
  }
  ::v-deep .el-tabs__header {
    .el-tabs__nav-scroll {
      // padding-left: 30px;
    }
    .el-tabs__item {
      height: 56px;
      line-height: 56px;
      &:nth-child(2) {
        padding-left: 30px;
      }
    }
  }
  ::v-deep .el-tabs__content {
  }
}
</style>
<style lang="scss">
.el-dialog.finance-import-device-dialog {
  border-radius: 2px;
  overflow: hidden;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
</style>
<style>
.order-detail__dialog .base-dialog__body {
  overflow: hidden;
}
.c-device-import .base-dialog__body {
  padding-top: 0;
}
</style>
