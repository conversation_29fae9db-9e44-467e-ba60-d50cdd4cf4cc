<!-- file: 新增服务商 --->
<template>
  <div class="c-add-service">
    <div class="flex justify-space-between center">
      <client-search
        style="width: 300px"
        size="small"
        ref="clientSearchRef"
        :isCur="true"
        :showSearch="false"
        :disabled="editRecord && !!Object.keys(editRecord).length"
        :placeholder="$t('llSrRUSNQCGG3Tzw4CSoM')"
        @autoSelectUser="getSelectOperateUser"
      />
      <svg-icon v-if="componentIndex !== 0" @click="() => $emit('close')" icon-class="delete_icon" class="c-add-service__delete"></svg-icon>
    </div>
    <!-- 服务商类型选择框 -->
    <div class="c-add-service__type-box" v-if="isHasPermissions('service:provider:type')">
      <span>{{ $t('NFsODE3FCSNwfl1kJhx4L') }}</span>
      <el-select class="c-add-service__type" v-model="type" :placeholder="$t('Kl8KHimuoV06FBms4UjzY')">
        <el-option :label="$t('djmIMlcXzVVOAmDmvw_vQ')" :value="String(1)"></el-option>
        <el-option :label="$t('hndIUBCh21ExxiafeHaPI')" :value="String(0)"></el-option>
      </el-select>
    </div>

    <div>
      <h3 class="c-add-service__title">{{ $t('setProfit') }}：</h3>
      <el-row>
        <el-form ref="addServiceRef" :model="formData" :rules="rules" label-width="80px" label-position="top">
          <el-col class="c-add-service__col" :span="7">
            <el-form-item :label="$t('equipmentService')" prop="deviceServiceProportion">
              <template #label>
                <div class="flex center">
                  <span class="ljdw-pr-8">{{ $t('equipmentService') }}</span>
                  <el-tooltip popper-class="c-add-service__tooltip" effect="dark" :content="packageClassifyTips.equipmentService" placement="top-start">
                    <PcFinanceTip size="12" />
                  </el-tooltip>
                </div>
              </template>
              <el-input
                v-model="formData.deviceServiceProportion"
                type="number"
                :disabled="!userInfo.bisUserId"
                :placeholder="placeholders.deviceServicePlaceholder"
                @blur="handleBlur('deviceServiceProportion', 'DeviceServiceProportion')"
              >
                <template #suffix>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col class="c-add-service__col" :span="7">
            <el-form-item :label="$t('softwareServices')" prop="softwareServiceProportion">
              <template #label>
                <div class="flex center">
                  <span class="ljdw-pr-8">{{ $t('softwareServices') }}</span>
                  <el-tooltip popper-class="c-add-service__tooltip" effect="dark" :content="packageClassifyTips.softwareServices" placement="top-start">
                    <PcFinanceTip size="12" />
                  </el-tooltip>
                </div>
              </template>
              <el-input
                v-model="formData.softwareServiceProportion"
                type="number"
                :disabled="!userInfo.bisUserId"
                :placeholder="placeholders.softwareServicesPlaceholder"
                @blur="handleBlur('softwareServiceProportion', 'SoftwareServiceProportion')"
              >
                <template #suffix>%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col class="c-add-service__col" :span="7">
            <el-form-item :label="$t('deviceActivation')" prop="deviceActiveProportion">
              <template #label>
                <div class="flex center">
                  <span class="ljdw-pr-8">{{ $t('deviceActivation') }}</span>
                  <el-tooltip popper-class="c-add-service__tooltip" effect="dark" :content="packageClassifyTips.deviceActivation" placement="top-start">
                    <PcFinanceTip size="12" />
                  </el-tooltip>
                </div>
              </template>
              <el-input
                v-model="formData.deviceActiveProportion"
                type="number"
                :disabled="!userInfo.bisUserId"
                :placeholder="placeholders.deviceActivationPlaceholder"
                @blur="handleBlur('deviceActiveProportion', 'DeviceActiveProportion')"
              >
                <template #suffix>%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div>
  </div>
</template>

<script>
import ClientSearch from './ResellerTree'
import { PcFinanceTip } from '@/assets/icon'
import { getRangeByUserProviderId, getPackageClassify, getRangeByServiceProviderId } from '@/api/order'
import { _getIsServiceAdmin } from '@/api/finance.js'
import { _getServiceProviderByBisUserId } from '@/api/order.js'
import permissionMixin from '@/mixins/permission'
// 数值是否在一个区间内
function isInInterval(number, min, max) {
  return number === Math.min(number, max) && number === Math.max(number, min)
}
export default {
  name: 'AddService',
  mixins: [permissionMixin],

  props: {
    componentIndex: {
      type: Number,
      default: 0
    },
    editRecord: {
      type: Object,
      default: null
    }
  },

  watch: {
    editRecord: {
      handler(value) {
        this.$nextTick(() => {
          if (!value) {
            return
          }
          this.$refs.clientSearchRef.userInfo.name = value.name
          for (const key in this.userInfo) {
            this.userInfo[key] = this.userInfo[key] || value[key]
          }
          for (const key in this.formData) {
            this.formData[key] = value[key]
          }
          this.type = String(value?.type) || '0'
          this.getRangesByServiceId(value.id)
        })
      },
      immediate: true
    }
  },

  components: {
    ClientSearch,
    PcFinanceTip
  },

  computed: {
    placeholders() {
      const { formDataRuleRecord, userInfo } = this
      return {
        deviceServicePlaceholder: !userInfo.name ? '' : `${formDataRuleRecord.DeviceServiceProportion.min} ~ ${formDataRuleRecord.DeviceServiceProportion.max}`,
        softwareServicesPlaceholder: !userInfo.name
          ? ''
          : `${formDataRuleRecord.SoftwareServiceProportion.min} ~ ${formDataRuleRecord.SoftwareServiceProportion.max}`,
        deviceActivationPlaceholder: !userInfo.name ? '' : `${formDataRuleRecord.DeviceActiveProportion.min} ~ ${formDataRuleRecord.DeviceActiveProportion.max}`
      }
    }
  },

  data() {
    return {
      userInfo: {
        currency: this.$store.getters.currencyParams,
        bisUserId: '',
        name: ''
      },

      type: '1', // 服务商类型 type 0-自营 1-生态,要求默认选中生态
      formData: {
        deviceServiceProportion: '',
        softwareServiceProportion: '',
        deviceActiveProportion: ''
      },
      rules: {
        deviceServiceProportion: [
          { required: true, message: this.$t('bzVAxsJEtQtawIDU5RBt1'), trigger: 'blur' },
          { validator: this.handleValidator, trigger: 'blur' }
        ],
        softwareServiceProportion: [
          { required: true, message: this.$t('bzVAxsJEtQtawIDU5RBt1'), trigger: 'blur' },
          { validator: this.handleValidator, trigger: 'blur' }
        ],
        deviceActiveProportion: [
          { required: true, message: this.$t('bzVAxsJEtQtawIDU5RBt1'), trigger: 'blur' },
          { validator: this.handleValidator, trigger: 'blur' }
        ]
      },
      formDataRuleRecord: {
        DeviceServiceProportion: { min: 0, max: 0 },
        SoftwareServiceProportion: { min: 0, max: 0 },
        DeviceActiveProportion: { min: 0, max: 0 }
      },
      packageClassifyTips: {
        equipmentService: '', // 设备服务类
        softwareServices: '', // 软件服务类
        deviceActivation: '' // 设备激活类
      }
    }
  },

  methods: {
    handleValidator(rule, value, callback) {
      if (value && !/^[1-9]\d*$/g.test(value)) {
        callback(new Error(this.$t('sTK0zTcZFNQTdsKoeupiA')))
      } else {
        callback()
      }
    },
    handleBlur(key, ruleKey) {
      const { formDataRuleRecord, formData } = this
      const value = formData[key]
      const rule = formDataRuleRecord[ruleKey]
      if (value === '') {
        return
      }
      if (!isInInterval(value, rule.min, rule.max)) {
        formData[key] = Math.min(Math.max(value, rule.min), rule.max)
      }
    },
    async getSelectOperateUser({ userId, name }) {
      this.userInfo.bisUserId = userId
      this.userInfo.name = name
      this.$emit('change', { userInfo: this.userInfo })
      if (userId) {
        const { data } = await _getServiceProviderByBisUserId({ userId })
        if (data) {
          this.type = String(data?.type || '1') //0-自营 1-生态 查不到默认生态
        } else {
          this.type = '1'
        }
      }

      await this.getRanges(userId)
      this.formData = this.$options.data.call(this).formData
    },
    async getRanges(targetUserId) {
      const { data } = await getRangeByUserProviderId({ targetUserId })
      this.setRangeValue(data)
    },
    async getPackageClassify() {
      const typeMap = {
        100: 'equipmentService', // 设备服务类
        101: 'softwareServices', // 软件服务类
        102: 'deviceActivation' // 设备激活类
      }
      const { data } = await getPackageClassify()
      data.forEach(item => {
        this.packageClassifyTips[typeMap[item.code]] = item.childrens.reduce(
          (pre, cur, index) => `${pre}${cur.name}${index === item.childrens.length - 1 ? '' : '、'}`,
          this.$t('packageIncluded') + '：'
        )
      })
    },
    async getRangesByServiceId(targetServiceProviderId) {
      const { data } = await getRangeByServiceProviderId({ targetServiceProviderId })
      this.setRangeValue(data)
    },
    setRangeValue(data) {
      for (const key in this.formDataRuleRecord) {
        this.formDataRuleRecord[key].min = data[`min${key}`]
        this.formDataRuleRecord[key].max = data[`max${key}`]
      }
    },
    getFormData() {
      return new Promise(resolve => {
        if (!this.userInfo.bisUserId) {
          this.$refs.clientSearchRef.isError = true
          resolve(false)
        }

        this.$refs.addServiceRef.validate(valid => {
          if (valid) {
            this.editRecord && Object.keys(this.editRecord).length
              ? resolve({ ...this.formData, serviceProviderId: this.editRecord.id, type: this.type })
              : resolve({ ...this.userInfo, ...this.formData, type: this.type })
          }

          resolve(false)
        })
      })
    }
  },
  created() {
    //this.getIsServiceAdmin()
  },
  mounted() {
    this.getPackageClassify()
  }
}
</script>

<style lang="scss">
.c-add-service {
  padding: 15px 0;
  .el-form-item__label {
    padding-bottom: 0;
    display: flex;
  }

  .c-add-service__title {
    margin: 30px 0 10px;
    font-size: 14px;
  }

  .c-add-service__delete {
    width: 22px;
    height: 22px;
    cursor: pointer;
  }

  &:not(:first-child) {
    border-top: 1px solid #c5c5c5;
  }
}
.c-add-service__col {
  padding: 0 20px;
  &:first-child {
    padding-left: 0;
  }
  &:last-child {
    padding-right: 0;
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}
.c-add-service__tooltip {
  max-width: 350px;
}
.c-add-service__type-box {
  margin-top: 20px;
  display: flex;
  align-items: left;
  justify-content: space-between;
  flex-direction: column;
  gap: 10px;
  color: #333;
}
.c-add-service__type {
  width: 300px;
}
</style>
