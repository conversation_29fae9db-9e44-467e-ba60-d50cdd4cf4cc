---
description:
globs:
alwaysApply: true
---
# API调用规范

本项目使用axios进行API调用。

## API目录结构
- [api/](mdc:src/api) 目录包含所有API请求
- 按模块或功能划分不同的API文件

## 请求方式
项目中API请求通常遵循以下模式：

```js
import request from '@/utils/request'

export function fetchData(params) {
  return request({
    url: '/some-endpoint',
    method: 'get',
    params
  })
}

export function postData(data) {
  return request({
    url: '/some-endpoint',
    method: 'post',
    data
  })
}
```

## 使用示例
在组件中使用API：

```js
import { fetchData } from '@/api/some-module'

export default {
  data() {
    return {
      listData: []
    }
  },
  methods: {
    async getData() {
      try {
        const res = await fetchData({ page: 1 })
        this.listData = res.data
      } catch (error) {
        console.error(error)
      }
    }
  },
  created() {
    this.getData()
  }
}
```
