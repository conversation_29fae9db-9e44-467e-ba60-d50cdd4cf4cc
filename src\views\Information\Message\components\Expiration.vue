<template>
  <div class="main-box">
    <div class="info">
      <p>{{ $t('4FFZ6E8mwSsIR99YCODZG') }}！</p>
      <p v-if="routerQuery.expire">{{ $t('CGfq1mj1IRuij49lT_V9r', [routerQuery.expire]) }}</p>
      <p v-else>{{ $t('EG1qollS3y8DsffUEQ11i', [routerQuery.expiring]) }}</p>
      <el-tag v-if="tagShow" closable @close="tagShow = false">
        <i class="el-icon-warning" />
        <span>{{ $t('Y33r2M_1dN1ut_Bp2XdSU') }}</span>
      </el-tag>
    </div>
    <div class="table-box">
      <el-table class="common-table" height="37vh" :data="tableData">
        <el-table-column :label="$t('lg.serial')" width="80px">
          <template v-slot="scope">
            {{ scope.$index + 1 + (query.pageIndex - 1) * query.pageSize }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('lg.machineName')" prop="machineName"> </el-table-column>
        <el-table-column :label="$t('TlgmpSebIew_qw82Z7273')" prop="imei"></el-table-column>
        <el-table-column :label="$t('pp7ggW3s8XBX7RhFkk1_v')">
          <template v-slot="scope">
            {{ scope.row.serviceTime ? scope.row.serviceTime : scope.row.platformTime | UTCTimeToLocalDate }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('xe_DoA8ge_gmef88_vh4p')">
          <template v-slot="scope">
            {{ scope.row.platformTime | UTCTimeToLocalDate }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('lg.status')">
          <template v-slot="scope">
            {{ scope.row.expireDay > 0 ? $t('H9Nda-k7XDEYdYb0Oxd01') : $t('Jet1u7xx2NupyskORg_e-') }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-box">
      <base-pagination
        class="common-pagination"
        background
        layout="prev, pager, next, sizes, jumper"
        :total="total"
        :current-page="query.pageIndex"
        :page-size="query.pageSize"
        :page-sizes="[15, 30, 45, 60]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </base-pagination>
    </div>
    <div class="footer">
      <el-button @click="goBack">{{ $t('JgHgaEMi5JzduvJ65HHG8') }}</el-button>
      <el-button type="primary" @click="handleRenew">{{ $t('lg.logDict.renew') }}</el-button>
    </div>

    <el-dialog
      :title="this.$t('lg.limits.Batch_renewal')"
      :visible.sync="showBatchCharge"
      :close-on-click-modal="false"
      append-to-body
      custom-class="device-recharge-dialog"
      @open="batchCharge"
      @handle-close="closeRecharge"
    >
      <recharge ref="deviceRecharge" @cancel="showBatchCharge = false" />
    </el-dialog>

    <el-dialog custom-class="parent-service-modal" :visible.sync="showParentService" :close-on-click-modal="false" width="410px" top="30vh">
      <div class="service-content">
        <div class="content-top">
          <div class="left">
            <img :src="serviceData.imageURL" class="user-avatar" />
          </div>
          <div class="right">
            <div class="row">
              <div class="label">{{ $t('lg.serviceProvide') }}：</div>
              <div class="txt">{{ serviceData.name }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('lg.linkMan') }}：</div>
              <div class="txt">{{ serviceData.linkMan }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('lg.linkPhone') }}：</div>
              <div class="txt">{{ serviceData.linkPhone }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('lg.address') }}：</div>
              <div class="txt">{{ serviceData.address }}</div>
            </div>
          </div>
        </div>
        <span class="close" @click="showParentService = false"></span>
        <div class="content-bottom">
          <div class="tips">{{ $t('1F7LiURKsTqo36ZrZkafp') }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getParentUserInfo } from '@/api/system.js'
import { getExpireMessageDetail } from '@/api/message.js'
import stationMixin from '@/mixins/station.js'
import '@/filter/publicfilter.js'
import { mapGetters } from 'vuex'
import ytrackUserpic from '@/icons/images/ytrack/userpic.png'
import caminoUserpic from '@/icons/images/camino-tech/userpic.png'
import geolockUserpic from '@/icons/images/geolock/userpic.png'
import publicUserpic from '@/assets/img/userpic.png'

const isDev = process.env.NODE_ENV === 'development'
let _api = ''
if (isDev) _api = '/api'

export default {
  name: 'Expiration',
  data() {
    return {
      tagShow: true, // 标签显示
      showBatchCharge: false, // 续费框显示
      showParentService: false, // 服务商框显示
      serviceData: {} // 服务商信息
    }
  },
  mixins: [stationMixin],
  components: {
    Recharge: () => import('@/components/Device/Recharge')
  },
  created() {
    //  优先展示到期数量
    if (this.routerQuery.expire) this.$parent.title = this.$t('CGfq1mj1IRuij49lT_V9r', [this.routerQuery.expire])
    else this.$parent.title = this.$t('WKkPZV30ap53zWCZyuwuD', [this.routerQuery.expiring])
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  methods: {
    getDetail() {
      getExpireMessageDetail({ detailId: this.$route.query.detailId, pageSize: this.query.pageSize, pageIndex: this.query.pageIndex }).then(res => {
        console.log('结果', res)
        this.tableData = res.data
        this.total = res.total
        // console.log(res)
      })
    },
    handleRenew() {
      if (this.userInfo.userType === 2) {
        //  普通用户
        getParentUserInfo().then(res => {
          if (res.ret === 1) {
            this.serviceData = res.data
            if (this.serviceData.imageURL) {
              this.serviceData.imageURL = '../..' + _api + '/image/getImage.do?imageId=' + this.serviceData.imageURL + '&token=' + this.$cookies.get('token')
            } else {
              switch (this.$cookies.get('site')) {
                case 'camino':
                  this.serviceData.imageURL = caminoUserpic
                  break
                case 'ytrack':
                  this.serviceData.imageURL = ytrackUserpic
                  break
                case 'geolock':
                  this.serviceData.imageURL = geolockUserpic
                  break
                default:
                  break
              }
              if (!this.serviceData.imageURL) {
                this.serviceData.imageURL = publicUserpic
              }
            }
            this.showParentService = true
          }
        })
        return
      }
      this.showBatchCharge = true
    },
    //  处理续费
    batchCharge() {
      let imei = []
      this.tableData.forEach(item => {
        imei.push(item.imei)
      })
      this.$nextTick(() => {
        this.$refs.deviceRecharge.batchAddImei(imei)
      })
      // this.showBatchCharge = true
    },
    //  关闭续费
    closeRecharge() {
      this.$refs.deviceRecharge.resetForm() // 清空表格
    }
  }
}
</script>

<style lang="scss" scoped>
.main-box {
  width: 50%;
}

.info {
  font-size: 18px;
  line-height: 32px;
  .el-tag {
    // width: 260px;
    height: auto;
    margin: 10px 0;
    padding: 5px 15px;
    background-color: #e6f7ff;
    span {
      font-size: 14px;
      white-space: pre-wrap;
      margin-left: 10px;
      color: #000000;
    }
  }
}

.pagination-box {
  margin-top: 30px;
}

.footer {
  margin-top: 30px;
  @include flex-row;
  .el-button {
    &:first-child {
      margin-right: 20px;
    }
  }
}

.device-recharge {
  ::v-deep .device-info_botton {
    padding-right: 40px;
  }
}

::v-deep .parent-service-modal {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .service-content {
    position: relative;
    padding: 20px 24px 30px 24px;
    .close {
      position: absolute;
      top: 16px;
      right: 24px;
      width: 16px;
      height: 16px;
      background-image: url('~@/assets/img/close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      cursor: pointer;
    }
    .content-top {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      margin-right: 20px;
      .user-avatar {
        height: 46px;
        width: 46px;
        border-radius: 50%;
      }
      .right {
        display: flex;
        flex-direction: column;
        margin-left: 20px;
        .row {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          margin-bottom: 12px;
          font-size: 14px;
          line-height: 14px;
          font-weight: 400;
          color: #333333;
          .label {
            min-width: 60px;
            text-align: right;
          }
          .txt {
            display: flex;
            flex: 1;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    .content-bottom {
      margin-top: 27px;
      .tips {
        font-size: 12px;
        font-weight: 400;
        color: #3f7efd;
        padding: 14px 12px;
        border-radius: 8px;
        background-color: #f7fafe;
      }
    }
  }
}
</style>
