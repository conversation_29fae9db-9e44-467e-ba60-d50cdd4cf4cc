import { httpPost, httpPostForm, httpGet, httpPostDownload, httpPostDownload1, httpPostUpload } from '@/utils/http'
import qs from 'qs'

// 计算订单金额
export function _getOrderTotalAccount(data) {
  return httpGet('/client/toClient/order/calcOrderTotalAccount.do', data)
}
// 计算订单金额
export function _getOrderAccumulateAccount(data) {
  return httpGet('/client/toClient/order/calcTotalIncome.do', data)
}

// 获取订单列表（旧）
// export function _getOrderList(data) {
//   return httpGet('/orderManage/getOrderList.do', data)
// }
// 获取订单列表
export function _getOrderList(data) {
  return httpGet('/client/toClient/order/getOrderList.do', data)
}

// 获取订单列表
export function _getOrderListV2(data) {
  return httpPost('/client/toClient/order/getOrderListV2.do', data)
}

// 导出订单（旧）
// export function _exportOrderList(data) {
//   return httpPostDownload('/orderManage/exportOrderList.do', data)
// }
// 导出订单
export function _exportOrderList(data) {
  return httpPostDownload('/client/toClient/order/exportOrderList.do', data)
}

// // 获取套餐列表
// export function _getPackageList(data) {
//   return httpGet('/package/getPackageList.do', data)
// }

// 获取套餐详情
export function _getPackageDetail(data) {
  return httpGet('/client/toClient/car/getCarServiceDetail.do', data)
}

// 获取套餐详情列表
export function _getPackageDetailList(data) {
  return httpGet('/package/getCarPackageDetail.do', data)
}

// 获取收支金额
export function _getIncomeBalance(data) {
  return httpGet('/toClient/accountInoutDetail/getBalance.do', data)
}

// 获取收支明细列表数据
export function _getIncomeTableList(data) {
  return httpGet('/toClient/accountInoutDetail/selectPage.do', data)
}

// 导出收支明细列表
export function _exportIncomeTableList(data) {
  return httpPostDownload('/toClient/accountInoutDetail/explore.do', data)
}

// 获取服务商列表
export function _getServiceTableList(data) {
  return httpGet('/toClient/serviceProvider/selectPage.do', data)
}

//根据bisUserId查询服务商
export function _getServiceProviderByBisUserId(data) {
  return httpGet('/toClient/serviceProvider/getByBisUserId.do', data)
}

// 导出服务商列表
export function _exportServiceTableList(data) {
  return httpPostDownload('/toClient/serviceProvider/export.do', data)
}

// 查询服务商配置信息(含最大可分配比例)
export function _queryServiceConfig(data) {
  return httpGet('/toClient/serviceProvider/queryServiceProviderConfig.do', data)
}

// 获取用户id查询可设值的区间
export function getRangeByUserProviderId(data) {
  return httpGet('/toClient/serviceProvider/settingRange.do', data)
}

// 获取用服务商id查询可设值的区间
export function getRangeByServiceProviderId(data) {
  return httpGet('/toClient/serviceProvider/settingRangeByServiceProviderId.do', data)
}

// 更新服务占比
export function updateProportion(data) {
  return httpPost('/toClient/serviceProvider/updateProportion.do', data)
}

// 新增服务商配置信息
export function _addServiceProvider(data) {
  return httpPost('/toClient/serviceProvider/add.do', data)
}

// 更新服务商占比
export function _updateServiceProvider(data) {
  return httpPost('/toClient/serviceProvider/add.do', data)
}

// 获取账号总额
export function _getAccountBalance(data) {
  return httpGet('/toClient/serviceProvider/getAccountBalance.do', data)
}

// 分账详单-分页查询
export function _profitSharingDetailSelectPage(data) {
  return httpGet('/toClient/profitSharingDetail/selectPage.do', data)
}

// 分账详单-导出
export function _profitSharingDetailExplore(data) {
  return httpPostDownload('/toClient/profitSharingDetail/explore.do', data)
}

// 分账详单-获取分账总额
export function _profitSharingDetailSelectTotalAmount(data) {
  return httpGet('/toClient/profitSharingDetail/selectTotalAmount.do', data)
}

// 根据服务商名称模糊查询
export function _queryByName(data) {
  return httpGet('/toClient/serviceProvider/queryByName.do', data)
}

// 套餐修改
export function _modifyPackage(data) {
  // return httpPostForm('/admin/settingPackage.do', data)
  return httpPostForm('/client/toClient/car/settingPackage.do', data, { responseType: 'blob' })
}

// 获取所有套餐
export function _getAllPackage(data) {
  return httpGet('/financialManagement/updateRecord/getAllPackageList.do', data)
}

// 获取导入记录列表
export function _getImportRecord(data) {
  return httpGet('/client/financialManagement/importRecord/getList.do', data)
}

// 导出导入记录
export function _exportImportRecord(data) {
  return httpPostDownload('/client/financialManagement/importRecord/exportList.do', data)
}

// 获取修改记录列表
export function _getModifyRecord(data) {
  return httpGet('/client/financialManagement/updateRecord/getList.do', data)
}

// 导出修改记录列表
export function _exportModifyRecord(data) {
  return httpPostDownload('/client/financialManagement/updateRecord/exportList.do', data)
}

// 套餐列表查询
export function _getPackageList(data) {
  return httpPost('/client/packageList/select.do', data)
}

// 套餐列表导出
export function _exportPackageList(data) {
  return httpPostDownload1('/client/packageList/export.do', data)
}

// 上下架状态切换
export function _changePackageStatus(data) {
  return httpPost('/client/packageList/updateStatus.do', data)
}

// 套餐列表-编辑
export function _editPackageList(data) {
  return httpPost('/client/packageList/update.do', data)
}

// 套餐代理列表查询
export function _getPackageProxyList(data) {
  return httpPost('/client/packageAgent/select.do', data)
}

// 套餐代理导出
export function _exportPackageProxy(data) {
  return httpPostDownload1('/client/packageAgent/export.do', data)
}

// 套餐代理删除
export function _deletePackageProxy(data) {
  return httpPost('/client/packageAgent/delete.do', data)
}

// 套餐代理新增
export function _addPackageProxy(data) {
  return httpPost('/client/packageAgent/create.do', data)
}

// 套餐代理编辑
export function _editPackageProxy(data) {
  return httpPost('/client/packageAgent/update.do', data)
}

// 获取套餐详情
export function _getPackageDetailInfo(data) {
  return httpGet('/client/packageConfig/detail.do', data)
}

// 获取套餐设置列表
export function _getPackageSettingList(data) {
  return httpPost('/client/packageConfig/select.do', data)
}

// 获取套餐分类信息
export function getPackageClassify() {
  if (getPackageClassify.result) {
    return Promise.resolve(getPackageClassify.result)
  } else {
    return new Promise(resolve => {
      ;(async () => {
        const result = await httpGet('/client/packageClassify/get.do')
        function recursion(list, lastRecord = {}) {
          for (const item of list) {
            item.packClassify = lastRecord.code
            if (item.childrens.length) {
              item.disabled = true
              recursion(item.childrens, item)
            }
          }
        }
        recursion(result.data)
        getPackageClassify.result = result
        resolve(getPackageClassify.result)
      })()
    })
  }
}

// 套餐设置列表导出
export function _exportPackageSettingList(data) {
  return httpPostDownload1('/client/packageConfig/export.do', data)
}

// 删除套餐代理
export function _deletePackageSetting(data) {
  return httpPost('/client/packageConfig/delete.do', data)
}

// 套餐设置新增
export function _addPackageSetting(data) {
  return httpPost('/client/packageConfig/create.do', data)
}

// 套餐设置编辑
export function _editPackageSetting(data) {
  return httpPost('/client/packageConfig/update.do', data)
}

// 解绑手机号
export function _unbindPhone(data) {
  return httpPost('/client/toClient/userCarBound/unboundCarByServiceProvider.do', data)
}

// 增值功能下拉列表
export function _getFunctionsSelector(data) {
  return httpGet('/client/packageConfig/functionsSelector.do', data)
}

// 获取所有套餐信息
export function _getPackageData(data) {
  return httpGet('/client/packageConfig/getClientCustomPackageList.do', data)
}

// 获取用户等级
export function _getClientLevel(data) {
  return httpGet('/web/client/serviceProvider/selectByBisUserId.do', data)
}

// 获取历史套餐详情
export function _getHistoryPackageDetail(data) {
  return httpGet('/client/financialManagement/updateRecord/selectHistoryPackageDetail.do', data)
}

// 更新录音单位
export function _updateVoiceType(data) {
  return httpPost('/toClient/serviceProvider/updateVoiceShowType.do', qs.stringify(data))
}

// 获取验证码
export function _getVerifyCode(data) {
  return httpPost('/toClient/user/sendSmsMsgNoLimit.do', data)
}
// 获取提现明细列表
export function _getWithdrawalList(data) {
  return httpPost('/withdrawalsDetail/selectPage.do', data)
}
// 获取/提现审核列表
export function _getWithdrawalVerifyList(data) {
  return httpPost('/withdrawalsDetail/selectReviewPage.do', data)
}

// 亚马逊单号列表查询
export function _getAmazonOrderList(data) {
  return httpGet('/client/toClient/userCarBoundRelation/amazonOrderSelect.do', data)
}

// 删除亚马逊订单
export function _deleteAmazonOrder(data) {
  return httpPost('/client/toClient/userCarBoundRelation/amazonOrderDelete.do', data)
}

// 亚马逊单号新增
export function _addAmazonOrder(data) {
  return httpPost('/client/toClient/userCarBoundRelation/amazonOrderInsert.do', data)
}

// 亚马逊单号编辑
export function _editAmazonOrder(data) {
  return httpPost('/client/toClient/userCarBoundRelation/amazonOrderUpdate.do', data)
}

// 调用百度OCR返回识别信息
export function _getCcrInfo(data) {
  return httpPostUpload('/ocrCommon/getInfo.do', data)
}

// 实名认证
export function _verifyPersonInfo(data) {
  return httpPost('/realNameAuth/insert.do', data)
}

// 获取服务商基本信息
export function _getServiceProviderInfo(data) {
  return httpGet('/realNameAuth/getByServiceProviderId.do', data)
}

// 查询文件地址
export function _getFileUrl(data) {
  return httpPost('/imageCommon/getUrl.do', qs.stringify(data))
}

// 修改实名认证手机号
export function _modifyPhone(data) {
  return httpPost('/realNameAuth/update.do', data)
}

// 新增企业提现账户
export function _updateCompanyAccount(data) {
  return httpPost('/enterpriseAccountExt/insertOrUpdate.do', data)
}

// 查询企业账户信息
export function _getCompanyAccountInfo(data) {
  return httpGet('/enterpriseAccountExt/getByServiceProviderId.do', data)
}

// 企业提现申请
export function _applyWithdraw(data) {
  return httpPost('/withdrawalsDetail/withdraw.do', data)
}

// 查询个人账户信息
export function _getPersonAccountInfo(data) {
  return httpGet('/personalAccountExt/getByServiceProviderId.do', data)
}

// 更新个人账户
export function _updatePersonAccount(data) {
  return httpPost('/personalAccountExt/insertOrUpdate.do', data)
}

// 获取提现明细合计金额
export function _getWithdrawalsTotalAmount(data) {
  return httpGet('/withdrawalsDetail/getWithdrawalsTotalAmount.do', data)
}
// 获取提现明细详情
export function _getWithdrawalsDetail(data) {
  return httpGet('/withdrawalsDetail/getById.do', data)
}
// 提现审核
export function _updateWithdrawalsDetail(data) {
  return httpPost('/withdrawalsDetail/enterpriseUpdate.do', data)
}

// 获取灵活就业协议
export function _getUserSignContract(data) {
  return httpPost('/account/getUserSignContract.do', qs.stringify(data))
}
// 获取服务商账户修改记录
export function _getAccountChangeRecords(data) {
  return httpPost('/toClient/serviceProvider/getAccountChangeRecords.do', data)
}
// 获取服务商账户修改记录 企业账户的详情
export function _getEntrpriseChangeDetail(data) {
  return httpGet('/enterpriseAccountExt/getById.do', data)
}
// 获取服务商账户修改记录 个人账户的详情
export function _getPersonalChangeDetail(data) {
  return httpGet('/personalAccountExt/getById.do', data)
}

// 获取导入记录设备详情
export function _getExportDeviceDetail(data) {
  return httpGet('/client/financialManagement/importRecord/getByBatchNum.do', data)
}

// 导出收支明细列表
export function _exportWhithrawalList(data) {
  return httpPostDownload1('/withdrawalsDetail/explore.do', data)
}

// 查询赠送记录列表
export function _getDonateRecordList(data) {
  return httpGet('/client/financialManagement/updateRecord/getList.do', data)
}

// 导出赠送记录列表
export function _exportDonateRecordList(data) {
  return httpPostDownload('/client/financialManagement/updateRecord/exportList.do', data)
}

// 查询转移记录列表
export function _getTransferRecordList(data) {
  return httpGet('/client/packageTransferRecord/selectPage.do', data)
}

// 查询服务记录详情
export function _getServiceRecordList(data) {
  return httpGet('/client/packageTransferDetail/getByRecordId.do', data)
}

// 导出转移记录列表
export function _exportTransferRecordList(data) {
  return httpPostDownload('/client/packageTransferRecord/exportList.do', data)
}

// 根据关联订单号查询赠送记录详情
export function _getDonateDetailByOrder(data) {
  return httpGet('/client/financialManagement/updateRecord/getDetailByGiveOrderId.do', data)
}

// 根据批次号查询设备详情
export function _getDonateDetailByBatchNumber(data) {
  return httpGet('/client/financialManagement/updateRecord/getDetailByBatchNum.do', data)
}

// 服务商订单套餐带单价
export function _getServiceProviderPackage(data) {
  return httpGet('/client/providerOrder/getServiceProviderPackageList.do', data)
}

// 查询本月赠送、剩余额度
export function _getMonthGiftInfo(data) {
  return httpGet('/client/serviceProviderOrder/getGiftInfo.do', data)
}

// 套餐赠送生成订单
export function _createOrder(data) {
  return httpPost('/client/providerOrder/createOrder.do', data)
}

// 套餐赠送余额支付
export function _balancePayDonateOrder(data) {
  return httpPost('/client/providerOrder/payment.do', data)
}

// 套餐赠送生成支付记录、二维码
export function _createPayRecord(data) {
  return httpPost('/client/providerOrder/generatePaymentRecord.do', data)
}

// 轮训支付订单
export function _checkPay(data) {
  return httpGet('/client/providerOrder/getProviderOrderStatus.do', data)
}

// 获取旧设备功能余量
export function _getOldEquipmentMargin(data) {
  return httpGet('/client/packageTransfer/getOldEquipmentMargin.do', data)
}

// 套餐转移
export function _transferPackage(data) {
  return httpPost('/client/packageTransfer/transfer.do', data)
}

// 查询服务商订单
export function _getServiceProviderOrderList(data) {
  return httpGet('/client/serviceProviderOrder/selectPage.do', data)
}

// 查询服务商订单金额
export function _getServiceProviderOrderAmount(data) {
  return httpGet('/client/serviceProviderOrder/calcOrderTotalAccount.do', data)
}

// 服务商订单导出
export function _exportServiceProviderOrderList(data) {
  return httpPostDownload('/client/serviceProviderOrder/exportList.do', data)
}

// 服务商订单详情
export function _getServiceProviderOrderDetail(data) {
  return httpGet('/client/serviceProviderOrder/getDetailById.do', data)
}

// 服务商订单设备详情查询
export function _getServiceProviderOrderDetailV2(data) {
  return httpGet('/client/serviceProviderOrder/getMachineDetail.do', data)
}
// 服务商管理-查询累计分账、余额总计
export function _getServiceTotalAccount(data) {
  return httpGet('/toClient/serviceProvider/countAccruingAmountAndBalance.do', data)
}

// 个人实名审核-列表
export function _getPresionalAuthAudit(data) {
  return httpGet('/presionalAuthAudit/selectPage.do', data)
}
// 个人实名审核-审核
export function _updateAuthAudit(data) {
  return httpPost('/presionalAuthAudit/update.do', data)
}

// 冻结金额-查看明细
export function _getFreezeAccountBalanceDetail(data) {
  return httpGet('/toClient/serviceProvider/getFreezeAccountBalanceDetail.do', data)
}
// 订单退款-获取权益列表
export function _getEquityRecoveryList(data) {
  return httpGet('/client/refund/equityRecoveryList', data)
}
// 订单退款-发起退款
export function _orderRefund(data) {
  return httpPost('/client/refund/apply', data)
}
// 退款管理-列表
export function _getRefundList(data) {
  return httpPost('/client/refund/select', data)
}
// 退款管理-进度
export function _getRefundAdvance(data) {
  return httpGet('/client/refund/advance', data)
}
// 退款管理-审核
export function _refundVerify(data) {
  return httpPost('/client/refund/verify', data)
}
// 退款管理-订单详情
export function _getOrderInfo(data) {
  return httpGet('/client/toClient/order/getOrderInfo', data)
}
// 退款管理-退款分账
export function _getRefundProfit(data) {
  return httpGet('/refund/detail.do', data)
}
// 退款管理-重新发起退款
export function _orderReRefund(data) {
  return httpPost('/client/refund/re-apply', data)
}
// 退款管理-退款申请信息
export function _getRefundInfo(data) {
  return httpGet('/client/refund/info', data)
}
// 退款管理-导出
export function _exportRefundList(data) {
  return httpPostDownload('/client/refund/export', data)
}
