<template>
  <el-form :inline="true">
    <!-- 手机号 -->
    <el-form-item :label="$t('aoalRCCBuSPYz5q4mvZI7') + ':'">
      <el-input :placeholder="$t('M3jEXD8nRHgxmpsbQKAuz')" clearable v-model.trim="form.phoneNumber"></el-input>
    </el-form-item>
    <!-- 服务商 -->
    <el-form-item :label="$t('lg.serviceProvide') + ':'">
      <search-user
        ref="UserTree"
        :placeholder="$t('8QHejHRn_K6ApxX1HI_x2')"
        :isCur="true"
        :size="'small'"
        @autoSelectUser="getSelectOperateUser"
        :showSearch="false"
      ></search-user>
    </el-form-item>
    <!-- imei -->
    <el-form-item label="IMEI:">
      <el-input :placeholder="$t('3aj3cEG2NB2VfffUFAdC_')" clearable v-model.trim="form.imei"></el-input>
    </el-form-item>
    <!-- timePicker -->
    <el-form-item :label="$t('lg.createTime') + ':'">
      <base-date-picker
        class="data-picker-wdth"
        v-model="pickDateArr"
        type="datetimerange"
        range-separator="~"
        :start-placeholder="$t('XbFegIO6XdtwVCLj3tHjn')"
        :end-placeholder="$t('k3rb7GIYeArL-6QUB2jYR')"
        :default-time="['00:00:00', '23:59:59']"
        :format="systemDateFormat"
        prefix-icon="el-icon-date"
        @change="handleDateChange"
        :picker-options="pickerOptions"
      >
      </base-date-picker>
    </el-form-item>
    <!-- btn -->
    <el-form-item>
      <base-button type="primary" @click="handleSearch">
        {{ $t('lg.query') }}
      </base-button>
      <base-button color="#606266FF" type="default" icon="reset" class="device-button__reset" @click="handleReset">
        {{ $t('lg.reset') }}
      </base-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import SearchUser from './ClientSearch.vue'

import pickerOptionMixinx from '@/mixins/pickerOptions.js'

import { DateFromLocalToUTC } from '@/utils/common.js'

export default {
  components: {
    SearchUser
  },
  mixins: [pickerOptionMixinx],
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapState({
      systemDateFormat: state => state.user.systemDateFormat
    })
  },
  data() {
    return {
      isCNLang: null, // 中文状态
      pickDateArr: []
    }
  },
  created() {
    this.isCNLang = this.$cookies.get('lang') === 'cn' || this.$cookies.get('lang') === 'tw' ? true : false
  },
  methods: {
    // 获取设备服务商id
    // this.$refs.UserTree.inputClear() // 重置设备服务商
    getSelectOperateUser(user) {
      this.form.serviceProviderId = user.id
    },
    handleDateChange(e) {
      if (this.pickDateArr) {
        this.form.startTime = DateFromLocalToUTC(this.pickDateArr[0])
        this.form.endTime = DateFromLocalToUTC(this.pickDateArr[1])
      } else {
        this.form.startTime = null
        this.form.endTime = null
      }
    },
    handleSearch() {
      if (this.form.phoneNumber && !/^[0-9]*$/.test(this.form.phoneNumber)) {
        this.$message.warning(this.$t('xzJrxRX1dNU-Rb_0eqU-7'))
        return
      }
      if (this.form.imei && !/^[0-9]*$/.test(this.form.imei)) {
        this.$message.warning(this.$t('_L3SCIlbztecHYB9zhGmZ'))
        return
      }
      this.$emit('search')
    },
    handleReset() {
      this.$refs.UserTree.inputClear() // 重置设备服务商
      this.pickDateArr = []
      this.form.phoneNumber = ''
      this.form.serviceProviderId = this.$cookies.get('bisUserId')
      this.form.imei = ''
      this.form.startTime = null
      this.form.endTime = null
      this.$emit('search')
    }
  }
}
</script>

<style lang="scss" scoped>
.data-picker-wdth {
  width: 210px;
}

.el-form-item.el-form-item--small {
  margin-right: 30px;
}
.input-with-select {
  width: 246px;
}
</style>

<style lang="scss">
.device-button__reset:hover {
  path:last-child {
    fill: $primary;
  }
}
</style>
