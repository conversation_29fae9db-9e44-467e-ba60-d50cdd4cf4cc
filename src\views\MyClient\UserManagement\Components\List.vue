<template>
  <div class="table-container">
    <el-table
      fit
      v-loading="dataLoading"
      :data="tableData"
      class="swd-table"
      :header-cell-style="{
        background: '#FAFAFA',
        color: 'rgb(0,0,0,0.85)',
        height: '54px'
      }"
      height="calc(100% - 52px)"
      row-key="id"
    >
      <el-table-column :label="$t('lg.serial')" align="left" width="50">
        <span slot-scope="scope">
          {{ (searchParams.pageNo - 1) * searchParams.pageSize + scope.$index + 1 }}
        </span>
      </el-table-column>
      <el-table-column :label="$t('8nrk8LIy6KzpJQv8D1o0w')" prop="name" width="150">
        <template slot-scope="{ row }">
          {{ row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('aoalRCCBuSPYz5q4mvZI7')" prop="linkPhone" minWidth="100"> </el-table-column>
      <el-table-column :label="$t('nJDVObsHH5ogCl6MxIO4C')" minWidth="150">
        <template slot-scope="{ row }">
          {{ row.ownedServiceProvider ? row.ownedServiceProvider.join('、 ') : '-' }}
        </template>
      </el-table-column>
      <!-- 设备数 -->
      <el-table-column :label="$t('设备数')" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.boundCount || '-' }}
        </template>
      </el-table-column>

      <el-table-column :label="$t('daGXDH-K0OYEPY_EUdT-c')" prop="boundTime">
        <template slot-scope="{ row }">
          <!-- {{ row.cars | carsTolist }} -->
          <span class="swd-table-cell-btn" @click="$emit('onDevices', row.userId)">{{ $t('lg.view') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('qdU58CUu3U1NuSOt3DBVU')" prop="serviceStatus">
        <template slot-scope="{ row }">
          <div v-if="row.associateFriends && row.associateFriends.length">
            <div v-for="(item, index) in row.associateFriends" :key="index">
              {{ item }}
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('PxiKJqRk2NZgR5rrcfbxV')" prop="serviceStatus" minWidth="100">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" @click="$emit('onOrders', row.userId)">{{ $t('lg.view') }}</span>
        </template>
      </el-table-column>
      <!-- 首次购买的套餐类型 -->
      <el-table-column :label="$t('首购')" minWidth="80">
        <template slot-scope="{ row }">
          {{ getFirstPurchaseType(row.firstPurchaseType) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('t22DN7yhHaAkxCW69UYsh')" prop="serviceTime" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.lastOnlineTime | utcToLocalTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('zdVunWKu5AhodKvqgP4rI')" prop="importTime" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.updateTime | utcToLocalTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.createTime')" prop="importTime" minWidth="100">
        <template slot-scope="{ row }">
          {{ row.createTime | utcToLocalTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lg.operator')" v-if="[0].indexOf(+this.userType) !== -1" width="80">
        <template slot-scope="{ row }">
          <span class="swd-table-cell-btn" v-if="row.available" @click="$emit('onForbidden', row.userId)">{{ $t('D-b7dWflNTTmQ5ISj_Fcy') }}</span>
          <span class="swd-table-cell-btn" v-else @click="$emit('onEnable', row.userId)">{{ $t('lg.startUsing') }}</span>
        </template>
      </el-table-column>
      <template slot="empty">
        <p class="empty-container">
          {{ dataLoading ? '' : $t('lg.noData') }}
        </p>
      </template>
    </el-table>
    <!-- pagination -->
    <div class="swd-pagination-container">
      <!-- <el-tooltip popper-class="device-client-icon" class="item" effect="dark" :content="$t('8Hy4f3sEGqYcZA0E2Tgwm')" placement="top">
        <el-button type="primary" :loading="exportLoading" class="swd-download-btn" @click="downloadTableData">
          <svg-icon v-if="!exportLoading" icon-class="client_download" class="download-icon"></svg-icon>
        </el-button>
      </el-tooltip> -->
      <base-pagination
        :current-page.sync="searchParams.pageNo"
        :page-sizes="[10, 15, 20, 30]"
        :page-size.sync="searchParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="$emit('pageChange')"
        @size-change="sizeChange"
      >
      </base-pagination>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    dataLoading: {
      type: Boolean,
      default: false
    },
    searchParams: {
      type: Object,
      default: () => {}
    },
    tableData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    page: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 15
    },
    pageSizes: {
      type: Array,
      default: () => [10, 15, 20, 30]
    },
    layout: {
      type: String,
      default: 'total, prev, pager, next, sizes, jumper'
    },
    tableHeight: {
      type: [Number],
      default: 500
    }
  },
  data() {
    return {
      column: [
        {
          label: this.$t('lg.serial'),
          align: 'left',
          width: '50',

          render: (h, scope) => {
            return <div> {(this.page - 1) * this.pageSize + scope.$index + 1}</div>
          }
        },
        {
          label: this.$t('8nrk8LIy6KzpJQv8D1o0w'),
          prop: 'name', //无需自定义字段，直接用data里的属性名即可
          align: 'left',
          width: '150',
          render: (h, scope) => {
            if (scope.row.name) {
              return h('span', scope.row.name)
            } else {
              return h('span', '-')
            }
          }
        },
        {
          label: this.$t('aoalRCCBuSPYz5q4mvZI7'),
          prop: 'linkPhone', //无需自定义字段，直接用data里的属性名即可
          align: 'left',
          minWidth: '100'
        },
        {
          label: this.$t('nJDVObsHH5ogCl6MxIO4C'),
          minWidth: '150',
          align: 'left',
          render: (h, scope) => {
            if (scope.row.ownedServiceProvider) {
              return h('span', scope.row.ownedServiceProvider.join('、 '))
            } else {
              return h('span', '-')
            }
          }
        },
        {
          label: this.$t('daGXDH-K0OYEPY_EUdT-c'),
          prop: 'updateTime', //无需自定义字段，直接用data里的属性名即可
          minWidth: '100',
          align: 'left',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="text"
                  onClick={() => {
                    this.$emit('onDevices', scope.row.userId)
                  }}
                >
                  {this.$t('lg.view')}
                </el-button>
              </div>
            )
          }
        },
        {
          label: this.$t('qdU58CUu3U1NuSOt3DBVU'),
          align: 'left',
          minWidth: '150',
          render: (h, scope) => {
            if (scope.row.associateFriends && scope.row.associateFriends.length > 0) {
              return h(
                'div',
                scope.row.associateFriends.map(item => {
                  return h('div', item)
                })
              )
            } else {
              return h('span', '-')
            }
          }
        },
        {
          label: this.$t('PxiKJqRk2NZgR5rrcfbxV'),
          align: 'left',
          minWidth: '100',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="text"
                  onClick={() => {
                    this.$emit('onOrders', scope.row.userId)
                  }}
                >
                  {this.$t('lg.view')}
                </el-button>
              </div>
            )
          }
        },
        {
          label: this.$t('t22DN7yhHaAkxCW69UYsh'),
          align: 'left',
          minWidth: '100',
          render: (h, scope) => {
            return <div>{scope.row.lastOnlineTime ? this.formatUTCTimeToLocalTime(scope.row.lastOnlineTime) : '-'}</div>
          }
        },
        {
          label: this.$t('zdVunWKu5AhodKvqgP4rI'),
          align: 'left',
          minWidth: '100',
          render: (h, scope) => {
            return <div>{scope.row.updateTime ? this.formatUTCTimeToLocalTime(scope.row.updateTime) : ''}</div>
          }
        },
        {
          label: this.$t('lg.createTime'),
          align: 'left',
          minWidth: '100',
          render: (h, scope) => {
            return <div>{scope.row.createTime ? this.formatUTCTimeToLocalTime(scope.row.createTime) : ''}</div>
          }
        },
        {
          label: this.$t('lg.operator'),
          width: '100',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  vShow={scope.row.available}
                  type="text"
                  onClick={() => {
                    this.$emit('onForbidden', scope.row.userId)
                  }}
                >
                  {this.$t('D-b7dWflNTTmQ5ISj_Fcy')}
                </el-button>
                <span></span>
                <el-button
                  vShow={!scope.row.available}
                  type="text"
                  onClick={() => {
                    this.$emit('onEnable', scope.row.userId)
                  }}
                >
                  {this.$t('lg.startUsing')}
                </el-button>
              </div>
            )
          }
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['userType'])
  },
  methods: {
    sizeChange(size) {
      this.$emit('sizeChange', size)
    },
    getFirstPurchaseType(type) {
      return (
        {
          0: '无',
          1: '亲友守护',
          2: '设备订阅'
        }[type] || '-'
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container {
  height: 0;
  flex: 1;
  margin-top: -5px;
  .user-manage-list-row-device {
    cursor: pointer;
    &-label {
      &.active {
        color: $primary;
      }
    }
    &:hover {
      text-decoration: underline;
      text-underline-offset: 3px;
      // text-decoration-color: $primary;
    }
  }
  .swd-pagination-container {
    position: relative;
    padding: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;
    .swd-download-btn {
      position: absolute;
      right: 0;
    }
  }
}
</style>
