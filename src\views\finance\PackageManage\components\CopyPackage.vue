<template>
  <div class="warp-name" :class="{ disabled: disabled }">
    <el-autocomplete
      class="username"
      v-model="packName"
      :fetch-suggestions="querySearchAsync"
      :clearable="clearable"
      :popper-class="'user-popper'"
      :disabled="disabled"
      @select="handleSelect"
      @focus="inputFocus"
      @clear="inputClear"
      @blur="inputBlur"
    >
    </el-autocomplete>
    <el-dropdown class="dropdown-tree" @visible-change="visibleChange" trigger="click" ref="dropDownTree" :disabled="disabled">
      <div class="open-tree-panel"><i class="el-icon-arrow-down" :class="{ rorate: treepanelVisible }" /></div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item class="search-user-dropdown" :style="{ width: dropWidth + 'px' }" v-for="item in packageOptions" :key="item.packId">
          <div class="tree-panel" @click="handleSelect(item)">{{ item.packName }}</div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
let treeInx = 0
import { _queryByName, _getPackageData, _getServiceProviderPackage } from '@/api/order.js'
import { getParentName } from '@/api/user.js'
export default {
  props: {
    info: Object,
    isCur: {
      type: Boolean,
      default() {
        return false
      }
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('lg.customer')
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    dropWidth: {
      type: [Number, String],
      default: 250
    },
    packType: {
      type: [Number, String],
      default: undefined
    },
    onSaleType: {
      type: [Number, String],
      default: 0 // 0：全部 1：上架 2：下架
    },
    searchType: {
      type: [Number, String],
      default: 1 // 1 正常查询 2 带单价
    },
    customPackageOptions: {
      //某个特殊客户，要求前端写死可以重置的套餐类型
      type: [null, Array],
      default: null
    }
  },
  data() {
    return {
      packName: '',
      totalPackList: [],
      packageOptions: [], // 套餐列表,过滤掉特定类型显示
      hasClick: false,
      userInfo: {
        userId: '',
        // userName: '', // 设备服务商没有userName
        parentId: '',
        parentName: '',
        userType: '',
        name: '',
        id: ''
      },
      setCurNodeInterval: null, // 搜索查找树节点定时器
      props: {
        label: 'name',
        children: 'child',
        isLeaf: 'isLeaf'
      },
      treepanelVisible: false,
      TreeClass: 'treeIndex',
      isFocus: false
    }
  },
  computed: {
    onSale() {
      let ret
      switch (Number(this.onSaleType)) {
        case 0:
          ret = undefined
          break
        case 1:
          ret = 1
          break
        case 2:
          ret = 0
          break
        default:
          ret = undefined
          break
      }
      return ret
    }
  },
  created() {
    this.TreeClass = this.TreeClass + treeInx
    treeInx++
    this.getPackageData()
  },
  mounted() {},
  methods: {
    filterPackType(packType) {
      this.packageOptions = this.totalPackList.filter(item => {
        return item.packType == packType
      })
      this.packName = this.packageOptions[0].packName
      this.$emit('select', this.packageOptions[0])
    },
    // 刷新组件数据
    refresh() {
      this.getPackageData()
    },
    visibleChange(e) {
      if (e) {
        this.treepanelVisible = true
      }
      this.treepanelVisible = e
    },
    // 获取所有套餐
    async getPackageData() {
      if (this.searchType == 1) {
        try {
          let res = await _getPackageData({ serviceProviderId: this.$cookies.get('bisUserId'), onSale: this.onSale })
          if (res.ret === 1) {
            this.totalPackList = res.data || []
            if (this.customPackageOptions) {
              this.packageOptions = this.customPackageOptions
            } else if (this.packType) {
              let packTypeArr = String(this.packType).split(',')
              this.packageOptions =
                res.data.filter(item => {
                  return packTypeArr.indexOf(String(item.packType)) !== -1
                }) || []
            } else {
              this.packageOptions = res.data || []
            }
            if (this.isCur && this.packageOptions.length) {
              this.packName = this.packageOptions[0].packName
              this.$emit('select', this.packageOptions[0])
            }
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          throw new Error(error)
        }
      } else if (this.searchType == 2) {
        try {
          let res = await _getServiceProviderPackage({ serviceProviderId: this.$cookies.get('bisUserId'), onSale: this.onSale })
          if (res.ret === 1) {
            if (this.packType) {
              let packTypeArr = String(this.packType).split(',')
              this.packageOptions =
                res.data.filter(item => {
                  return packTypeArr.indexOf(String(item.packType)) !== -1
                }) || []
            } else {
              this.packageOptions = res.data || []
            }
            if (this.isCur && this.packageOptions.length) {
              this.packName = this.packageOptions[0].packName
              this.$emit('select', this.packageOptions[0])
            }
          } else {
            this.$message.error(res.msg)
          }
        } catch (error) {
          throw new Error(error)
        }
      }
    },
    showTreePanel() {
      this.treepanelVisible = !this.treepanelVisible
    },
    inputFocus() {
      this.treepanelVisible = false
      this.isFocus = true
    },
    inputBlur() {
      if (!this.packName) {
        this.$emit('select', undefined)
      }
      // this.isFocus = false
    },
    // 模糊搜索
    querySearchAsync(queryString, cb) {
      if (queryString) {
        _getPackageData({
          serviceProviderId: this.$cookies.get('bisUserId'),
          packName: queryString,
          onSale: this.onSale
        }).then(res => {
          if (res.ret === 1) {
            let results = res.data.map(e => {
              e.value = e.packName
              return e
            })
            cb(results)
          } else {
            cb([])
          }
        })
      } else {
        cb([])
      }
    },
    inputClear() {
      this.packName = ''
      this.$emit('select', undefined)
    },
    handleSelect(item) {
      this.packName = item.packName
      this.$emit('select', item)
    }
  },
  destroyed() {
    this.setCurNodeInterval = null
  }
}
</script>

<style scoped lang="scss">
.warp-name {
  position: relative;
  width: 260px;
  display: flex;
  align-items: center;
  padding-right: 32px;
  border: 1px #dee0e3 solid;
  border-radius: 6px;
  box-sizing: border-box;
  &.disabled {
    background-color: #f5f7fa;
  }
}
.open-tree-panel {
  height: 30px;
  width: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  outline: none;
  border: none;
  .el-icon-caret-bottom {
    transition: transform 0.3s;
    &.rorate {
      transform: rotateZ(-180deg);
    }
  }
}
.dropdown-tree {
  position: absolute;
  right: 0;
}
.username {
  width: 270px;
  line-height: 30px;
  ::v-deep .el-input__inner {
    border-radius: 20px;
    height: 30px;
    line-height: 30px;
    border: none;
    background-color: transparent;
  }
  ::v-deep .el-input--suffix {
    height: 30px;
  }
  ::v-deep .el-input__prefix {
    line-height: 30px;
    .el-input__icon {
      line-height: 30px;
    }
  }
}
.tree-panel {
  // position: absolute;
  // top: 34px;
  // left: 10px;
  // z-index: 2;
  // border: 1px #dee0e3 solid;
  // background-color: #ffffff;
  .clientThree {
    width: 280px;
    height: 300px;
    overflow: scroll;
    box-sizing: border-box;
    ::v-deep .el-tree-node__expand-icon {
      color: #73baff;
      font-size: 14px;
      &.is-leaf {
        color: transparent;
        cursor: default;
      }
    }
    ::v-deep .el-tree-node__content {
      .svg-icon {
        margin-right: 2px;
      }
    }
    // &::-webkit-scrollbar-thumb {
    //   border-style: dashed;
    //   background-color: rgba(157, 165, 183, 0.4);
    //   border-color: transparent;
    //   border-width: 2px;
    //   background-clip: padding-box;
    // }
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background-color: #fff;
      cursor: pointer;
      border-radius: 8px;
    }
  }
}
.search-user-dropdown:focus,
.search-user-dropdown:not(.is-disabled):hover {
  background-color: #ffffff;
  color: #ffffff;
}
::v-deep .el-dropdown-menu {
  left: 0;
}
::v-deep .disabled-input.is-disabled .el-input__inner {
  width: 240px;
}
</style>
<style lang="scss">
.el-autocomplete-suggestion.user-popper {
  min-width: 180px;
}
.el-dropdown-menu {
  max-height: 300px;
  overflow: auto;
}
</style>
